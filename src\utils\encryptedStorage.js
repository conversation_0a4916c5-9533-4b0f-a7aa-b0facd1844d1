import CryptoJS from 'crypto-js';

// Secret key for encryption (in production, this should be more secure)
const SECRET_KEY = 'TriTrackz-Auth-Key-2024';

/**
 * Encrypt data using AES encryption
 * @param {string} data - Data to encrypt
 * @returns {string} - Encrypted data
 */
export const encryptData = (data) => {
  try {
    return CryptoJS.AES.encrypt(data, SECRET_KEY).toString();
  } catch (error) {
    console.error('Encryption error:', error);
    return null;
  }
};

/**
 * Decrypt data using AES decryption
 * @param {string} encryptedData - Encrypted data to decrypt
 * @returns {string} - Decrypted data
 */
export const decryptData = (encryptedData) => {
  try {
    const bytes = CryptoJS.AES.decrypt(encryptedData, SECRET_KEY);
    return bytes.toString(CryptoJS.enc.Utf8);
  } catch (error) {
    console.error('Decryption error:', error);
    return null;
  }
};

/**
 * Store encrypted credentials in localStorage
 * @param {string} email - User email
 * @param {string} password - User password
 */
export const storeRememberedCredentials = (email, password) => {
  try {
    const credentials = {
      email,
      password,
      timestamp: Date.now()
    };
    
    const encryptedCredentials = encryptData(JSON.stringify(credentials));
    if (encryptedCredentials) {
      localStorage.setItem('rememberedCredentials', encryptedCredentials);
    }
  } catch (error) {
    console.error('Error storing credentials:', error);
  }
};

/**
 * Retrieve and decrypt stored credentials from localStorage
 * @returns {Object|null} - Decrypted credentials or null
 */
export const getRememberedCredentials = () => {
  try {
    const encryptedCredentials = localStorage.getItem('rememberedCredentials');
    if (!encryptedCredentials) {
      return null;
    }
    
    const decryptedData = decryptData(encryptedCredentials);
    if (!decryptedData) {
      return null;
    }
    
    const credentials = JSON.parse(decryptedData);
    
    // Check if credentials are not older than 30 days
    const thirtyDaysInMs = 30 * 24 * 60 * 60 * 1000;
    if (Date.now() - credentials.timestamp > thirtyDaysInMs) {
      clearRememberedCredentials();
      return null;
    }
    
    return {
      email: credentials.email,
      password: credentials.password
    };
  } catch (error) {
    console.error('Error retrieving credentials:', error);
    clearRememberedCredentials(); // Clear corrupted data
    return null;
  }
};

/**
 * Clear stored credentials from localStorage
 */
export const clearRememberedCredentials = () => {
  try {
    localStorage.removeItem('rememberedCredentials');
  } catch (error) {
    console.error('Error clearing credentials:', error);
  }
};

/**
 * Check if credentials are currently stored
 * @returns {boolean} - True if credentials are stored
 */
export const hasRememberedCredentials = () => {
  return localStorage.getItem('rememberedCredentials') !== null;
};
