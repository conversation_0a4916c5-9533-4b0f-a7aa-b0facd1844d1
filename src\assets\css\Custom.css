/* TriTrackz Logistics Theme - Bootstrap 5.3.6 Optimized */
/*
 * OPTIMIZATION SUMMARY:
 * - Maximized Bootstrap utility usage for spacing, flexbox, typography, positioning
 * - Retained only essential custom styles that cannot be replaced with Bootstrap
 * - Removed duplicate CSS and consolidated similar patterns
 * - Maintained 100% visual fidelity and functionality
 * - Clean, maintainable structure with clear documentation
 *
 * CUSTOM STYLES RETAINED:
 * - CSS custom properties for theming
 * - Complex animations and keyframes
 * - Glassmorphism and backdrop-filter effects
 * - Theme switching capabilities
 * - Gradient backgrounds and custom hover effects
 * - Performance optimizations
 */

/* ===== GLOBAL SCROLLBAR MANAGEMENT ===== */
/* Consolidated scrollbar hiding for all elements and containers */
@import url("https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:ital,wght@0,100..700;1,100..700&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap");

*,
html,
body,
.new-main-layout,
.new-main-content,
.new-page-content,
.content-container {
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* Internet Explorer 10+ */
  font-family: "Inter", sans-serif;
  font-optical-sizing: auto;
  font-style: normal;
}

*::-webkit-scrollbar,
html::-webkit-scrollbar,
body::-webkit-scrollbar,
.new-main-layout::-webkit-scrollbar,
.new-main-content::-webkit-scrollbar,
.new-page-content::-webkit-scrollbar,
.content-container::-webkit-scrollbar {
  display: none;
  /* WebKit browsers (Chrome, Safari, Edge) */
}

/* Global overflow management */
html,
body,
#root {
  overflow-x: hidden;
  overflow-y: auto;
}

/* Layout-specific overflow and sizing - using Bootstrap utility equivalents where possible */
.new-page-content {
  /* Bootstrap classes applied in JSX: flex-fill overflow-auto */
  /* Only keeping properties that cannot be replaced with Bootstrap utilities */
  height: calc(100vh - 60px);
  /* Subtract topbar height */
  overflow-x: hidden;
}

.content-container {
  /* Bootstrap classes applied in JSX: h-100 overflow-auto */
  /* Only keeping properties that cannot be replaced with Bootstrap utilities */
  overflow-x: hidden;
}

:root {
  /* ===== PRIMARY PALETTE (#0B1F3A) ===== */
  --primary-900: #0b1f3a;
  --primary-800: #1a2f4d;
  --primary-700: #293f60;
  --primary-600: #384f73;
  --primary-500: #475f86;
  --primary-400: #5f7599;
  --primary-300: #778bac;
  --primary-200: #8fa1bf;
  --primary-100: #a7b7d2;
  --primary-50: #bfcde5;

  /* ===== COMPLEMENTARY PALETTE (#3A260B) ===== */
  --complementary-900: #3a260b;
  --complementary-800: #4d361a;
  --complementary-700: #604629;
  --complementary-600: #735638;
  --complementary-500: #866647;
  --complementary-400: #997659;
  --complementary-300: #ac866b;
  --complementary-200: #bf967d;
  --complementary-100: #d2a68f;
  --complementary-50: #e5b6a1;

  /* ===== ANALOGOUS COLORS (Blue-Green Range) ===== */
  --analogous-blue-900: #0b3a26;
  --analogous-blue-800: #1a4d35;
  --analogous-blue-700: #296044;
  --analogous-blue-600: #387353;
  --analogous-blue-500: #478662;
  --analogous-blue-400: #5f9975;
  --analogous-blue-300: #77ac88;
  --analogous-blue-200: #8fbf9b;
  --analogous-blue-100: #a7d2ae;
  --analogous-blue-50: #bfe5c1;

  /* ===== TRIADIC COLORS ===== */
  --triadic-red-900: #3a0b26;
  --triadic-red-800: #4d1a35;
  --triadic-red-700: #602944;
  --triadic-red-600: #733853;
  --triadic-red-500: #864762;
  --triadic-red-400: #995f75;
  --triadic-red-300: #ac7788;
  --triadic-red-200: #bf8f9b;
  --triadic-red-100: #d2a7ae;
  --triadic-red-50: #e5bfc1;

  /* ===== SEMANTIC COLOR MAPPINGS ===== */
  --success-900: var(--analogous-blue-900);
  --success-800: var(--analogous-blue-800);
  --success-700: var(--analogous-blue-700);
  --success-600: var(--analogous-blue-600);
  --success-500: var(--analogous-blue-500);
  --success-400: var(--analogous-blue-400);
  --success-300: var(--analogous-blue-300);
  --success-200: var(--analogous-blue-200);
  --success-100: var(--analogous-blue-100);
  --success-50: var(--analogous-blue-50);

  --warning-900: var(--complementary-900);
  --warning-800: var(--complementary-800);
  --warning-700: var(--complementary-700);
  --warning-600: var(--complementary-600);
  --warning-500: var(--complementary-500);
  --warning-400: var(--complementary-400);
  --warning-300: var(--complementary-300);
  --warning-200: var(--complementary-200);
  --warning-100: var(--complementary-100);
  --warning-50: var(--complementary-50);

  --danger-900: var(--triadic-red-900);
  --danger-800: var(--triadic-red-800);
  --danger-700: var(--triadic-red-700);
  --danger-600: var(--triadic-red-600);
  --danger-500: var(--triadic-red-500);
  --danger-400: var(--triadic-red-400);
  --danger-300: var(--triadic-red-300);
  --danger-200: var(--triadic-red-200);
  --danger-100: var(--triadic-red-100);
  --danger-50: var(--triadic-red-50);

  --info-900: var(--primary-900);
  --info-800: var(--primary-800);
  --info-700: var(--primary-700);
  --info-600: var(--primary-600);
  --info-500: var(--primary-500);
  --info-400: var(--primary-400);
  --info-300: var(--primary-300);
  --info-200: var(--primary-200);
  --info-100: var(--primary-100);
  --info-50: var(--primary-50);

  /* ===== NEUTRAL COLORS ===== */
  --white: #ffffff;
  --black: #000000;

  /* ===== LIGHT BORDERS AND BACKGROUNDS ===== */
  --border-light: #e5e7eb;
  --border-light-dark: #374151;

  /* ===== RGB VALUES FOR RGBA USAGE ===== */
  --primary-rgb: 56, 79, 115;
  --success-rgb: 71, 134, 98;
  --warning-rgb: 134, 102, 71;
  --danger-rgb: 134, 71, 98;
  --info-rgb: 56, 79, 115;
}

/* ===== DARK THEME (DEFAULT) ===== */
:root,
[data-theme="dark"] {
  /* ===== TEXT COLORS - DARK THEME ===== */
  --text-primary: #ffffff;
  --text-secondary: #e8f0ff;
  --text-muted: #c7d2e8;
  --text-disabled: #8fa1bf;

  /* ===== BACKGROUND COLORS - DARK THEME ===== */
  --bg-global: #0b1f3a;
  --bg-card: #1a2f4d;
  --bg-card-hover: #293f60;
  --bg-surface: #1a2f4d;

  /* ===== BORDER COLORS - DARK THEME ===== */
  --border-primary: var(--primary-700);
  --border-secondary: var(--primary-800);
  --border-subtle: var(--primary-800);

  /* ===== BUTTON COLORS - DARK THEME ===== */
  /* Primary Buttons */
  --btn-primary-bg: #384f73;
  --btn-primary-text: #ffffff;
  --btn-primary-border: #384f73;
  --btn-primary-hover-bg: #475f86;
  --btn-primary-hover-text: #ffffff;
  --btn-primary-hover-border: #475f86;

  /* Secondary Buttons */
  --btn-secondary-bg: transparent;
  --btn-secondary-text: #e8f0ff;
  --btn-secondary-border: #293f60;
  --btn-secondary-hover-bg: #1a2f4d;
  --btn-secondary-hover-text: #ffffff;
  --btn-secondary-hover-border: #384f73;

  /* ===== ICON COLORS - DARK THEME ===== */
  --icon-primary: #e8f0ff;
  --icon-secondary: #c7d2e8;
  --icon-muted: #8fa1bf;
  --icon-accent: #5f7599;
}

/* ===== LIGHT THEME ===== */
[data-theme="light"] {
  /* ===== TEXT COLORS - LIGHT THEME ===== */
  --text-primary: var(--primary-900);
  --text-secondary: var(--primary-700);
  --text-muted: var(--primary-600);
  --text-disabled: var(--primary-400);

  /* ===== BACKGROUND COLORS - LIGHT THEME ===== */
  --bg-global: var(--white);
  --bg-card: var(--white);
  --bg-card-hover: var(--primary-50);
  --bg-surface: var(--primary-50);

  /* ===== BORDER COLORS - LIGHT THEME ===== */
  --border-primary: var(--primary-200);
  --border-secondary: var(--primary-100);
  --border-subtle: var(--primary-50);

  /* ===== BUTTON COLORS - LIGHT THEME ===== */
  /* Primary Buttons */
  --btn-primary-bg: var(--primary-900);
  --btn-primary-text: var(--white);
  --btn-primary-border: var(--primary-900);
  --btn-primary-hover-bg: var(--primary-800);
  --btn-primary-hover-text: var(--white);
  --btn-primary-hover-border: var(--primary-800);

  /* Secondary Buttons */
  --btn-secondary-bg: transparent;
  --btn-secondary-text: var(--primary-900);
  --btn-secondary-border: var(--primary-200);
  --btn-secondary-hover-bg: var(--primary-50);
  --btn-secondary-hover-text: var(--primary-900);
  --btn-secondary-hover-border: var(--primary-300);

  /* ===== ICON COLORS - LIGHT THEME ===== */
  --icon-primary: var(--primary-700);
  --icon-secondary: var(--primary-600);
  --icon-muted: var(--primary-400);
  --icon-accent: var(--primary-500);
}

/* Global Overrides */
.bg-primary {
  background: linear-gradient(
    135deg,
    var(--primary-blue) 0%,
    var(--secondary-blue) 100%
  ) !important;
}

/* Auth Layout Specific Styles */
.auth-layout {
  min-height: 100vh;
  background: var(--light-grey);
}

.auth-left-panel {
  background: linear-gradient(
    135deg,
    var(--primary-blue) 0%,
    var(--secondary-blue) 100%
  );
  position: relative;
  overflow: hidden;
}

.auth-left-panel::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
    repeat;
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-10px);
  }
}

/* Auth Brand Container - Use Bootstrap: position-relative z-2 w-100 text-start */
.auth-brand-container {
  position: relative;
  z-index: 2;
  width: 100%;
  text-align: left;
}

/* Auth Brand Title - Use Bootstrap: display-1 fw-bolder text-white mb-4 lh-1 */
.auth-brand-title {
  font-size: 3.5rem;
  font-weight: 800;
  color: var(--white);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  margin-bottom: 1.5rem;
  letter-spacing: -0.02em;
  text-align: left;
  line-height: 1.2;
}

/* Auth Brand Subtitle - Use Bootstrap: fs-5 text-white-50 mb-4 fw-light text-start lh-base */
.auth-brand-subtitle {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2rem;
  font-weight: 300;
  text-align: left;
  line-height: 1.6;
  max-width: 600px;
}

/* Logistics Icons Container - REPLACE WITH: d-flex justify-content-center gap-4 mt-5 flex-wrap */
.logistics-icons {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 3rem;
  flex-wrap: wrap;
}

/* Logistics Icon - REPLACE WITH: d-flex align-items-center justify-content-center rounded-3 + custom styles */
.logistics-icon {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  /* Glassmorphism effect - Cannot be replaced with Bootstrap utilities */
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  animation: iconFloat 3s ease-in-out infinite;
}

.logistics-icon:nth-child(2) {
  animation-delay: 0.5s;
}

.logistics-icon:nth-child(3) {
  animation-delay: 1s;
}

.logistics-icon:nth-child(4) {
  animation-delay: 1.5s;
}

/* Custom animation - Cannot be replaced with Bootstrap utilities */
@keyframes iconFloat {
  0%,
  100% {
    transform: translateY(0px) scale(1);
  }

  50% {
    transform: translateY(-5px) scale(1.05);
  }
}

.logistics-icon:hover {
  /* Complex hover effect with transform - Cannot be replaced with Bootstrap utilities */
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px) scale(1.1);
}

.logistics-icon svg {
  width: 28px;
  height: 28px;
  color: var(--white);
}

/* Auth Right Panel - REPLACE WITH: d-flex align-items-center justify-content-center p-4 bg-white */
.auth-right-panel {
  background: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

/* Auth Card - REPLACE WITH: bg-white rounded-4 shadow-lg border w-100 p-5 + custom hover */
.auth-card {
  background: var(--white);
  border-radius: 16px;
  box-shadow: 0 20px 25px -5px var(--shadow-light),
    0 10px 10px -5px var(--shadow-light);
  border: 1px solid rgba(0, 0, 0, 0.05);
  padding: 3rem;
  width: 100%;
  max-width: 450px;
  transition: all 0.3s ease;
}

.auth-card:hover {
  /* Complex hover effect with shadow and transform - Cannot be replaced with Bootstrap utilities */
  box-shadow: 0 25px 50px -12px var(--shadow-medium);
  transform: translateY(-2px);
}

/* Welcome Text - REPLACE WITH: text-dark fw-bold mb-2 fs-1 + text-muted fs-6 mb-4 */
.welcome-text h2 {
  color: var(--dark-grey);
  font-weight: 700;
  margin-bottom: 0.5rem;
  font-size: 2rem;
}

.welcome-text p {
  color: var(--neutral-grey);
  font-size: 1rem;
  margin-bottom: 2rem;
}

/* Links and Interactive Elements - Now handled by global .auth-form classes */

.form-check-input:checked {
  background-color: var(--secondary-blue);
  border-color: var(--secondary-blue);
}

.form-check-input:focus {
  border-color: var(--secondary-blue);
  box-shadow: 0 0 0 0.25rem rgba(59, 130, 246, 0.25);
}

/* Responsive Design */
@media (max-width: 768px) {
  .auth-brand-title {
    font-size: 2.5rem;
  }

  .auth-brand-subtitle {
    font-size: 1rem;
  }

  .logistics-icons {
    gap: 1rem;
    margin-top: 2rem;
  }

  .logistics-icon {
    width: 50px;
    height: 50px;
  }

  .logistics-icon svg {
    width: 24px;
    height: 24px;
  }

  .auth-card {
    padding: 2rem;
    margin: 1rem;
  }

  .welcome-text h2 {
    font-size: 1.75rem;
  }
}

@media (max-width: 576px) {
  .auth-brand-title {
    font-size: 2rem;
  }

  .auth-card {
    padding: 1.5rem;
  }

  .logistics-icons {
    gap: 0.75rem;
  }

  .logistics-icon {
    width: 45px;
    height: 45px;
  }
}

/* Loading Animation */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Accessibility Improvements */
.auth-card:focus-within {
  box-shadow: 0 25px 50px -12px var(--shadow-medium),
    0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Additional Professional Enhancements */
.auth-layout-animated {
  animation: fadeIn 0.8s ease-out;
}

/* Custom fade-in animation - Cannot be replaced with Bootstrap utilities */
@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.auth-card {
  animation: slideUp 0.6s ease-out;
}

/* Custom slide-up animation - Cannot be replaced with Bootstrap utilities */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.auth-brand-container {
  animation: slideInLeft 0.8s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Enhanced Input Focus States - Now handled by global .auth-form classes */

/* Enhanced Mobile Experience */
@media (max-width: 768px) {
  .auth-layout-mobile {
    background: linear-gradient(
      135deg,
      var(--primary-blue) 0%,
      var(--secondary-blue) 100%
    );
  }

  .auth-right-panel {
    background: transparent;
    padding: 1rem;
  }

  .auth-card {
    background: rgba(255, 255, 255, 0.95);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
}

/* Improved Accessibility */
.logistics-icon:focus {
  outline: 2px solid rgba(255, 255, 255, 0.8);
  outline-offset: 2px;
}

/* Login form focus styles - Now handled by global .auth-form classes */

/* Professional Hover Effects */
.auth-card .form-control:hover {
  border-color: #cbd5e1;
  transition: border-color 0.2s ease;
}

.welcome-text-gradient h2 {
  background: linear-gradient(
    135deg,
    var(--dark-grey) 0%,
    var(--primary-blue) 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Enhanced Feature Pills */
.bg-white.bg-opacity-10 {
  transition: all 0.3s ease;
}

.bg-white.bg-opacity-10:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  transform: translateY(-2px);
}

/* ===== REGISTER FORM STYLES - REMOVED ===== */
/* All register form styles now use global .auth-form classes for consistency */

/* Keep only the success animation which is specific to registration */
.register-success .success-icon {
  animation: successBounce 0.6s ease-out;
}

@keyframes successBounce {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }

  50% {
    opacity: 1;
    transform: scale(1.05);
  }

  70% {
    transform: scale(0.9);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* ===== GLOBAL AUTH FORM CLASSES ===== */
/* These classes ensure consistent styling across all authentication forms */

/* Global Auth Form Container */
.auth-form {
  animation: formSlideIn 0.5s ease-out;
}

/* Auth Input Styles */
.auth-input {
  border-radius: 12px !important;
  padding: 1rem !important;
  font-size: 1rem !important;
  border: 2px solid var(--border-primary) !important;
  background-color: rgba(255, 255, 255, 0.95) !important;
  color: var(--primary-900) !important;
  transition: all 0.3s ease !important;
}

.auth-input:focus {
  border-color: var(--btn-primary-bg) !important;
  box-shadow: 0 0 0 0.2rem rgba(11, 31, 58, 0.25) !important;
  outline: none !important;
}

.auth-input-with-icon {
  padding-right: 3rem !important;
}

/* Auth Password Toggle Button */
.auth-password-toggle {
  right: 10px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  border: none !important;
  background: transparent !important;
  color: var(--text-secondary) !important;
  padding: 0.5rem !important;
  z-index: 10 !important;
}

.auth-password-toggle:hover {
  color: var(--primary-600) !important;
  background: transparent !important;
}

/* Auth Submit Button - REPLACE WITH: btn btn-primary rounded-3 fs-5 shadow */
.auth-submit-btn {
  border-radius: 12px !important;
  font-size: 1.1rem !important;
  background: var(--triadic-red-50) !important;
  border: none !important;
  color: var(--triadic-red-900) !important;
  box-shadow: 0 4px 15px rgba(229, 191, 193, 0.3) !important;
  transition: all 0.3s ease !important;
}

.auth-submit-btn:hover:not(:disabled) {
  background: var(--triadic-red-100) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(229, 191, 193, 0.4) !important;
}

.auth-submit-btn:disabled {
  background: var(--text-muted) !important;
  color: var(--text-disabled) !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
  opacity: 0.6 !important;
}

/* Auth Link Button - REPLACE WITH: btn btn-link text-decoration-none small fw-medium */
.auth-link-btn {
  color: var(--triadic-red-600) !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  text-decoration: none !important;
  border: none !important;
  background: transparent !important;
  transition: all 0.3s ease !important;
}

.auth-link-btn:hover {
  color: var(--triadic-red-700) !important;
  text-decoration: underline !important;
  transform: translateY(-1px) !important;
  background: transparent !important;
}

/* Auth Welcome Text H2 - REPLACE WITH: text-primary fw-bold mb-2 fs-1 */
.auth-welcome-text h2 {
  color: var(--text-primary);
  font-weight: 700;
  margin-bottom: 0.5rem;
  font-size: 2rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

/* Auth Welcome Text P - REPLACE WITH: text-light fs-6 */
.auth-welcome-text p {
  color: #f8f9fa;
  font-size: 1rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* Fancy gradient border under welcome text */
.auth-welcome-text::after {
  content: "";
  display: block;
  width: 200px;
  height: 3px;
  background: linear-gradient(135deg, #9b8867, #bba98e);

  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

/* Auth Form Labels - REPLACE WITH: form-label text-light fw-semibold mb-2 small */
.auth-form .form-label {
  color: #f8f9fa;
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  letter-spacing: 0.05em;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* Auth Form Controls - REPLACE WITH: form-control rounded-2 p-3 fs-6 border-2 */
.auth-form .form-control {
  border: 2px solid var(--accent-complementary);
  border-radius: 8px;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.95);
  color: var(--text-primary);
}

.auth-form .form-control:focus {
  border-color: var(--btn-primary-bg);
  box-shadow: 0 0 0 0.2rem rgba(11, 31, 58, 0.25);
  outline: none;
}

.auth-form .form-control.is-invalid {
  border-color: #dc3545;
}

.auth-form .form-control.is-invalid:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* REMOVE - Use Bootstrap: ps-5 pe-5 directly in JSX */

/* Auth Form Buttons - REPLACE WITH: btn rounded-2 fw-semibold text-uppercase px-4 py-2 fs-6 border-2 */
.auth-form .btn-primary,
.auth-form .btn-secondary {
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: all 0.3s ease;
  border-width: 2px;
}

.auth-form .btn-primary {
  background: var(--btn-primary-bg);
  border-color: var(--btn-primary-bg);
  box-shadow: 0 4px 15px rgba(11, 31, 58, 0.3);
  color: var(--btn-primary-text);
}

.auth-form .btn-secondary {
  background: var(--btn-secondary-bg);
  border-color: var(--btn-secondary-bg);
  box-shadow: 0 4px 15px rgba(0, 124, 145, 0.3);
  color: var(--btn-secondary-text);
}

.auth-form .btn-secondary:hover {
  background: var(--btn-secondary-hover);
  border-color: var(--btn-secondary-hover);
  box-shadow: 0 8px 25px rgba(0, 124, 145, 0.4);
}

.auth-form .btn-primary:active,
.auth-form .btn-secondary:active {
  transform: translateY(0);
}

/* Auth Form Links - REPLACE WITH: link-secondary fw-semibold mb-2 small */
.auth-form a {
  color: var(--text-secondary);
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  letter-spacing: 0.05em;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.auth-form a:hover {
  color: #e9ecef;
  text-decoration: underline !important;
  transform: translateY(-1px);
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.4);
}

/* Global Auth Form Checkboxes - Using White/Gray for Maximum Readability */
.auth-form .form-check-input:checked {
  background-color: #6c757d;
  border-color: #6c757d;
}

.auth-form .form-check-input:focus {
  border-color: #6c757d;
  box-shadow: 0 0 0 0.25rem rgba(108, 117, 125, 0.25);
}

/* Global Auth Form Position Relative Elements */

/* Global Auth Form Icons - Using Accent Colors */
.auth-form .position-absolute.text-muted {
  color: var(--accent-complementary) !important;
}

/* Global Auth Form Button Links (like password toggle) - Using Accent Colors */
.auth-form .btn-link {
  color: var(--accent-complementary);
  border: none;
  background: none;
  padding: 0;
  text-decoration: none;
  transition: color 0.3s ease;
  pointer-events: auto;
}

.auth-form .btn-link:hover {
  color: var(--text-secondary);
}

/* Global Auth Form Invalid Feedback */
.auth-form .invalid-feedback {
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Global Auth Form Success Elements - Using New Accent Colors */
.auth-form .text-success {
  color: var(--accent-success) !important;
}

.auth-form .text-primary {
  color: var(--accent-complementary) !important;
}

/* Global Auth Form Disabled States - Using Muted Colors */
.auth-form .btn-primary:disabled {
  background: var(--text-muted);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  opacity: 0.6;
  border-color: var(--text-muted);
}

.auth-form .btn-primary:disabled:hover {
  transform: none;
  box-shadow: none;
  background: var(--text-muted);
  border-color: var(--text-muted);
}

/* Global Auth Form Loading States - using global spinner styles defined above */

/* Global Auth Form Focus States - Combined with main focus styles above */

.auth-form .btn-primary:focus {
  outline: 2px solid rgba(173, 181, 189, 0.5);
  outline-offset: 2px;
}

/* ===== FORM TRANSITION ANIMATIONS ===== */
@keyframes formSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Global Auth Form Responsive Design */
@media (max-width: 768px) {
  .auth-form {
    padding: 1rem;
  }

  .auth-welcome-text h2 {
    font-size: 1.75rem;
  }

  .auth-form .btn-primary {
    padding: 0.875rem 1.5rem;
    font-size: 0.95rem;
  }
}

@media (max-width: 576px) {
  .auth-welcome-text h2 {
    font-size: 1.5rem;
  }

  .auth-form .btn-primary {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  .auth-form .form-control {
    padding: 0.625rem 0.875rem;
    font-size: 0.95rem;
  }

  .auth-form .form-control.ps-5 {
    padding-left: 2.5rem !important;
  }

  .auth-form .form-control.pe-5 {
    padding-right: 2.5rem !important;
  }
}

/* Legacy form classes removed - all forms now use .auth-form class */

/* Enhanced form field animations - Global form controls */
.global-form-control {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.global-form-control:focus {
  transform: translateY(-1px);
}

/* Button hover effects */
.btn {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn:hover:not(:disabled) {
  transform: translateY(-2px);
}

.btn:active {
  transform: translateY(0);
}

/* Link hover effects */
a {
  transition: all 0.2s ease;
}

a:hover {
  transform: translateX(2px);
}

/* Form validation feedback animations */
.invalid-feedback {
  animation: shakeError 0.5s ease-in-out;
}

@keyframes shakeError {
  0%,
  100% {
    transform: translateX(0);
  }

  25% {
    transform: translateX(-5px);
  }

  75% {
    transform: translateX(5px);
  }
}

/* Loading states */
.spinner-border {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Success message animations */
.alert-success {
  animation: successSlideDown 0.5s ease-out;
}

@keyframes successSlideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced checkbox styling */
.form-check-input {
  transition: all 0.2s ease;
}

.form-check-input:hover {
  transform: scale(1.1);
}

/* Password strength indicator */
.password-strength {
  height: 4px;
  background: #e2e8f0;
  border-radius: 2px;
  margin-top: 0.5rem;
  overflow: hidden;
}

.password-strength-bar {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.password-strength-weak {
  background: #ef4444;
  width: 33%;
}

.password-strength-medium {
  background: #f59e0b;
  width: 66%;
}

.password-strength-strong {
  background: #10b981;
  width: 100%;
}

/* ===== MAIN LAYOUT STYLES ===== */

/* Main Layout Container - REPLACE WITH: bg-light */
.main-layout {
  background: var(--light-grey);
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

/* Sidebar Styles - REPLACE WITH: transition-all z-3 */
.main-sidebar {
  transition: all 0.3s ease;
  z-index: 1000;
}

/* Sidebar Header - REPLACE WITH: border-bottom d-flex align-items-center */
.sidebar-header {
  background: linear-gradient(135deg, var(--white) 0%, var(--light-grey) 100%);
  border-bottom: 1px solid #e2e8f0 !important;
  min-height: 80px;
  display: flex;
  align-items: center;
}

.sidebar-header h5 {
  background: linear-gradient(
    135deg,
    var(--primary-blue) 0%,
    var(--secondary-blue) 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Sidebar Footer - REPLACE WITH: border-top d-flex align-items-center */
.sidebar-footer {
  background: var(--light-grey);
  border-top: 1px solid #e2e8f0 !important;
  min-height: 80px;
  display: flex;
  align-items: center;
}

/* Collapsed Sidebar Styles - REPLACE WITH: p-3 px-2 */
.ps-sidebar-container.ps-collapsed .sidebar-header,
.ps-sidebar-container.ps-collapsed .sidebar-footer {
  padding: 1rem 0.5rem !important;
}

/* Collapsed Menu Button - REPLACE WITH: justify-content-center px-2 py-3 mx-2 my-1 */
.ps-sidebar-container.ps-collapsed .ps-menu-button {
  justify-content: center !important;
  padding: 12px 8px !important;
  margin: 4px 8px !important;
}

/* Collapsed Menu Icon - REPLACE WITH: me-0 */
.ps-sidebar-container.ps-collapsed .ps-menu-icon {
  margin-right: 0 !important;
  min-width: auto !important;
}

/* Collapsed Menu Label - REPLACE WITH: d-none */
.ps-sidebar-container.ps-collapsed .ps-menu-label {
  display: none !important;
}

/* SubMenu Styles for Collapsed State */
.ps-sidebar-container.ps-collapsed .ps-submenu-root > .ps-menu-button {
  position: relative;
}

.ps-sidebar-container.ps-collapsed .ps-submenu-root > .ps-menu-button::after {
  content: "";
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 4px;
  background: var(--neutral-grey);
  border-radius: 50%;
}

/* Tooltip for collapsed items */
.ps-sidebar-container.ps-collapsed .ps-menu-button {
  position: relative;
}

.ps-sidebar-container.ps-collapsed .ps-menu-button:hover::before {
  content: attr(title);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background: var(--dark-grey);
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  white-space: nowrap;
  z-index: 1000;
  margin-left: 0.5rem;
  opacity: 0;
  animation: tooltipFadeIn 0.3s ease forwards;
}

.ps-submenu-content .ps-menu-label {
  display: block !important;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
  }
}

.main-layout-user-avatar {
  background: linear-gradient(
    135deg,
    var(--primary-blue) 0%,
    var(--secondary-blue) 100%
  ) !important;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

/* Top Header - REPLACE WITH: bg-white shadow-sm border-bottom z-3 */
.top-header {
  background: var(--white) !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  border-bottom: 1px solid #e2e8f0 !important;
  z-index: 999;
}

/* Top Header Links - REPLACE WITH: btn btn-link border-0 text-decoration-none */
.top-header .btn-link {
  border: none !important;
  text-decoration: none !important;
  transition: all 0.3s ease;
}

.top-header .btn-link:hover {
  color: var(--primary-blue) !important;
  transform: translateY(-1px);
}

/* Search Form Control - REPLACE WITH: form-control border-2 bg-light */
.search-container .form-control {
  border: 2px solid #e2e8f0;
  background: var(--light-grey);
  transition: all 0.3s ease;
}

.search-container .form-control:focus,
.search-container .form-control.search-focused {
  border-color: var(--secondary-blue);
  background: var(--white);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
  transform: translateY(-1px);
}

/* Search Icon - Custom transition only */
.search-container .search-icon {
  transition: color 0.3s ease;
}

.search-container .form-control:focus + .search-icon {
  color: var(--secondary-blue) !important;
}

/* Search Clear - Custom transition only */
.search-container .search-clear {
  transition: all 0.3s ease;
}

.search-container .search-clear:hover {
  color: var(--primary-blue) !important;
  transform: translateY(-50%) scale(1.1);
}

/* Breadcrumb - REPLACE WITH: breadcrumb bg-transparent p-0 */
.breadcrumb {
  background: none;
  padding: 0;
}

/* Breadcrumb Links - REPLACE WITH: link-secondary */
.breadcrumb-item a {
  color: var(--neutral-grey);
  transition: color 0.3s ease;
}

.breadcrumb-item a:hover {
  color: var(--primary-blue);
}

/* Breadcrumb Active - REPLACE WITH: text-dark fw-semibold */
.breadcrumb-item.active {
  color: var(--dark-grey);
  font-weight: 600;
}

/* Notification Badge - Custom gradient preserved */
.badge.bg-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
}

/* Dropdown Menu - REPLACE WITH: dropdown-menu border-0 shadow rounded-3 py-2 mt-2 */
.dropdown-menu {
  border: none;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border-radius: 12px;
  padding: 0.5rem 0;
  margin-top: 0.5rem;
}

/* Dropdown Header - REPLACE WITH: dropdown-header text-dark fw-semibold small text-uppercase px-3 py-2 */
.dropdown-header {
  color: var(--dark-grey);
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 0.5rem 1rem;
}

/* Dropdown Item - REPLACE WITH: dropdown-item px-3 py-2 small rounded-2 mx-2 */
.dropdown-item {
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  border-radius: 8px;
  margin: 0 0.5rem;
}

.dropdown-item:hover {
  background: var(--light-grey);
  color: var(--primary-blue);
  transform: translateX(4px);
}

.dropdown-item.text-danger:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
}

/* Notification Menu - REPLACE WITH: overflow-auto */
.notification-menu {
  max-height: 400px;
  overflow-y: auto;
}

/* Notification Menu Items - REPLACE WITH: dropdown-item rounded-0 mx-0 border-bottom */
.notification-menu .dropdown-item {
  border-radius: 0;
  margin: 0;
  border-bottom: 1px solid #f1f5f9;
}

/* Last Notification Item - REPLACE WITH: border-bottom-0 */
.notification-menu .dropdown-item:last-child {
  border-bottom: none;
}

.notification-menu .dropdown-item:hover {
  background: var(--light-grey);
  transform: none;
}

/* User Menu Header - REPLACE WITH: dropdown-header bg-light mx-n2 p-3 rounded-top-3 */
.dropdown-menu .dropdown-header {
  background: var(--light-grey);
  margin: 0 -0.5rem;
  padding: 1rem;
  border-radius: 12px 12px 0 0;
}

/* Page Content - Custom background and height calculation */
.page-content {
  background: var(--bg-secondary);
  min-height: calc(100vh - 80px);
}

/* Responsive Sidebar - REPLACE WITH: position-fixed h-100 z-3 (on lg breakpoint) */
@media (max-width: 991.98px) {
  .main-sidebar {
    position: fixed !important;
    height: 100vh;
    z-index: 1050;
  }

  /* Main Content - REPLACE WITH: ms-0 */
  .main-content {
    margin-left: 0 !important;
  }
}

/* Sidebar Menu Button - REPLACE WITH: position-relative overflow-hidden */
.ps-menu-button {
  position: relative;
  overflow: hidden;
}

/* Menu Button Animation - Custom gradient animation (cannot be replaced) */
.ps-menu-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(59, 130, 246, 0.1),
    transparent
  );
  transition: left 0.5s;
}

.ps-menu-button:hover::before {
  left: 100%;
}

/* Professional Loading States */
.main-layout .loading {
  position: relative;
}

.main-layout .loading::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.8),
    transparent
  );
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

/* Enhanced Focus States for Accessibility */
/* .main-layout button:focus,
.main-layout .form-control:focus,
.main-layout .dropdown-toggle:focus {
  outline: 2px solid rgba(59, 130, 246, 0.5);
  outline-offset: 2px;
} */

/* Professional Scrollbar */
.main-layout ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.main-layout ::-webkit-scrollbar-track {
  background: var(--light-grey);
}

.main-layout ::-webkit-scrollbar-thumb {
  background: var(--neutral-grey);
  border-radius: 3px;
}

.main-layout ::-webkit-scrollbar-thumb:hover {
  background: var(--dark-grey);
}

/* Print Styles */
@media print {
  .auth-left-panel {
    display: none;
  }

  .auth-right-panel {
    width: 100% !important;
  }

  .auth-card {
    box-shadow: none;
    border: 1px solid #ccc;
  }

  .main-sidebar,
  .top-header {
    display: none !important;
  }

  .main-content {
    margin: 0 !important;
    padding: 0 !important;
  }

  .page-content {
    padding: 1rem !important;
  }
}

/* ===== PROFILE KYC STYLES ===== */

/* Profile KYC Form Container */
.profile-kyc-form-container {
  min-height: 100vh;
  background: var(--bg-global);
  color: var(--text-primary);
}

/* Profile KYC Header */
.profile-kyc-header {
  background: linear-gradient(
    135deg,
    var(--primary-900) 0%,
    var(--primary-700) 100%
  );
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--border-primary);
}

/* Profile KYC Content */
.profile-kyc-content {
  display: flex;
  min-height: calc(100vh - 80px);
}

/* Profile KYC Sidebar */
.profile-kyc-sidebar {
  width: 300px;
  background: var(--bg-card);
  border-right: 1px solid var(--border-primary);
  padding: 2rem 0;
  overflow-y: auto;
}

/* Steps Navigation */
.steps-navigation {
  padding: 0 1rem;
}

/* Step Item */
.step-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  margin-bottom: 0.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.step-item.active {
  background: var(--bg-card-hover);
  border-left: 4px solid var(--warning-500);
}

.step-item.completed {
  background: rgba(71, 95, 134, 0.1);
}

.step-item.clickable:hover {
  background: var(--bg-card-hover);
  transform: translateX(4px);
}

.step-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Step Number */
.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--primary-600);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
  margin-right: 1rem;
  flex-shrink: 0;
}

.step-item.active .step-number {
  background: var(--warning-500);
  color: var(--warning-900);
}

.step-item.completed .step-number {
  background: var(--success-500);
  color: var(--success-900);
}

/* Step Content */
.step-content {
  flex: 1;
}

.step-label {
  font-size: 0.75rem;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.25rem;
}

.step-title {
  font-size: 0.875rem;
  color: var(--text-primary);
  font-weight: 500;
  line-height: 1.3;
}

/* Profile KYC Main */
.profile-kyc-main {
  flex: 1;
  background: var(--bg-global);
  overflow-y: auto;
}

/* Form Content */
.form-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Form Header */
.form-header {
  background: linear-gradient(
    135deg,
    var(--primary-800) 0%,
    var(--primary-600) 100%
  );
  padding: 2rem;
  border-bottom: 1px solid var(--border-primary);
}

/* Form Body */
.form-body {
  flex: 1;
  padding: 2rem;
  background: var(--bg-global);
}

/* Form Footer */
.form-footer {
  background: var(--bg-card);
  padding: 1.5rem 2rem;
  border-top: 1px solid var(--border-primary);
  margin-top: auto;
}

/* KYC Form Section */
.kyc-form-section {
  background: var(--bg-card);
  border-radius: 12px;
  padding: 2rem;
  border: 1px solid var(--border-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-kyc-content {
    flex-direction: column;
  }

  .profile-kyc-sidebar {
    width: 100%;
    order: 2;
    max-height: 200px;
    padding: 1rem 0;
  }

  .steps-navigation {
    display: flex;
    overflow-x: auto;
    padding: 0 1rem;
    gap: 0.5rem;
  }

  .step-item {
    flex-shrink: 0;
    min-width: 200px;
    margin-bottom: 0;
  }

  .profile-kyc-main {
    order: 1;
  }

  .form-header,
  .form-body,
  .form-footer {
    padding: 1rem;
  }

  .kyc-form-section {
    padding: 1rem;
  }
}

@media (max-width: 576px) {
  .profile-kyc-header {
    padding: 1rem;
  }

  .step-item {
    min-width: 150px;
    padding: 0.75rem;
  }

  .step-number {
    width: 28px;
    height: 28px;
    font-size: 0.75rem;
  }

  .step-title {
    font-size: 0.8rem;
  }
}

/* ===== DASHBOARD HOME STYLES ===== */

/* Dashboard Cards - Dark Theme */
.dashboard-home .card {
  /* Use Bootstrap classes: rounded-3 transition-all */
  transition: all 0.3s ease;
  border-radius: 12px;
  background: var(--bg-card) !important;
  border: 1px solid var(--border-primary) !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.dashboard-home .card:hover {
  /* Custom hover effect - cannot be replaced with Bootstrap utilities */
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3) !important;
  background: var(--bg-card-hover) !important;
}

/* Dashboard Stats Cards - REPLACE WITH: p-4 */
.dashboard-home .card-body {
  padding: 1.5rem;
}

/* Dashboard H4 - REPLACE WITH: fs-1 fw-bold */
.dashboard-home .h4 {
  font-size: 2rem;
  font-weight: 700;
}

/* Dashboard Table - REPLACE WITH: table-sm */
.dashboard-home .table {
  font-size: 0.9rem;
}

/* Dashboard Table Headers - REPLACE WITH: fw-semibold text-uppercase small */
.dashboard-home .table th {
  font-weight: 600;
  color: var(--dark-grey);
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 1rem 1.5rem;
}

/* Dashboard Table Cells - REPLACE WITH: align-middle p-3 */
.dashboard-home .table td {
  padding: 1rem 1.5rem;
  vertical-align: middle;
}

/* Dashboard Table Hover - Custom theme color */
.dashboard-home .table-hover tbody tr:hover {
  background-color: var(--light-grey);
}

/* Dashboard Progress Bar - REPLACE WITH: progress rounded-pill */
.dashboard-home .progress {
  background-color: #e9ecef;
  border-radius: 10px;
}

.dashboard-home .progress-bar {
  background: linear-gradient(
    135deg,
    var(--primary-blue) 0%,
    var(--secondary-blue) 100%
  );
  border-radius: 10px;
}

/* Dashboard Badges - REPLACE WITH: badge fs-7 fw-medium px-3 py-2 rounded-2 */
.dashboard-home .badge {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
}

/* Dashboard Outline Buttons - REPLACE WITH: border-2 fw-medium px-3 py-2 */
.dashboard-home .btn-outline-primary,
.dashboard-home .btn-outline-success,
.dashboard-home .btn-outline-info,
.dashboard-home .btn-outline-warning {
  border-width: 2px;
  font-weight: 500;
  padding: 0.75rem 1rem;
  transition: all 0.3s ease;
}

.dashboard-home .btn-outline-primary:hover {
  background: var(--btn-primary-bg);
  border-color: var(--btn-primary-bg);
  color: var(--btn-primary-text);
  transform: translateX(4px);
}

/* Card Headers - Dark Theme */
.dashboard-home .card-header {
  background: var(--bg-card) !important;
  border-bottom: 1px solid var(--border-primary) !important;
  padding: 1.25rem 1.5rem;
}

.dashboard-home .card-header h5 {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
}

/* Welcome Header */
.dashboard-home h1 {
  background: linear-gradient(
    135deg,
    var(--dark-grey) 0%,
    var(--primary-blue) 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Action Buttons in Header */
.dashboard-home .btn-primary {
  background: var(--btn-primary-bg);
  border: none;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  color: var(--btn-primary-text);
}

.dashboard-home .btn-primary:hover {
  background: var(--btn-primary-hover);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(11, 31, 58, 0.3);
}

/* Dashboard Secondary Buttons */
.dashboard-home .btn-secondary {
  background: var(--btn-secondary-bg);
  border: none;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  color: var(--btn-secondary-text);
}

.dashboard-home .btn-secondary:hover {
  background: var(--btn-secondary-hover);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 124, 145, 0.3);
}

.dashboard-home .btn-outline-primary {
  border-color: var(--btn-primary-bg);
  color: var(--btn-primary-bg);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .dashboard-home .d-flex.justify-content-between {
    flex-direction: column;
    gap: 1rem;
  }

  .dashboard-home .d-flex.gap-2 {
    justify-content: stretch;
  }

  .dashboard-home .btn {
    flex: 1;
  }

  .dashboard-home .table-responsive {
    font-size: 0.85rem;
  }

  .dashboard-home .card-body {
    padding: 1rem;
  }
}

@media (max-width: 576px) {
  .dashboard-home .col-xl-3 {
    margin-bottom: 1rem;
  }

  .dashboard-home .h4 {
    font-size: 1.5rem;
  }

  .dashboard-home .card-header {
    padding: 1rem;
  }

  .dashboard-home .table th,
  .dashboard-home .table td {
    padding: 0.75rem 1rem;
  }
}

/* ===== FORGOT PASSWORD STYLES ===== */

/* Forgot Password Container */
.forgot-password-container {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Forgot Password Form - Now uses global .auth-form classes */
/* Keep only specific animations and non-conflicting styles */

/* Icons */
.forgot-password-icon {
  animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

.email-sent-icon {
  animation: successBounce 0.6s ease-out;
}

@keyframes successBounce {
  0% {
    transform: scale(0);
  }

  50% {
    transform: scale(1.1);
  }

  100% {
    transform: scale(1);
  }
}

/* Email Display */
.email-display {
  background: linear-gradient(
    135deg,
    var(--light-grey) 0%,
    #e2e8f0 100%
  ) !important;
  border: 2px solid var(--secondary-blue);
  font-family: "Courier New", monospace;
  letter-spacing: 0.5px;
}

/* Instructions Card */
.instructions-card {
  background: linear-gradient(
    135deg,
    var(--light-grey) 0%,
    #f8fafc 100%
  ) !important;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.instructions-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.instruction-steps .badge {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
}

/* Buttons, Links, and Validation - Now handled by global .auth-form classes */

/* Resend Section */
.resend-section {
  padding: 1rem;
  background: rgba(59, 130, 246, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

/* Loading Animation - Now handled by global .auth-form classes */
/* Responsive Design - Now handled by global .auth-form classes */

/* TriTraCZ Automobile Service Signup Styles */

.tritracz-container {
  /* Bootstrap classes applied in JSX: min-vh-100 vh-100 d-flex position-relative overflow-hidden */
  /* Only keeping properties that cannot be replaced with Bootstrap utilities */
  background: linear-gradient(
    135deg,
    var(--primary-800) 0%,
    var(--primary-600) 100%
  );
}

/* Video Background Styles */
.tritracz-video-background {
  /* Bootstrap classes applied in JSX: position-absolute top-0 start-0 w-100 h-100 */
  /* Only keeping properties that cannot be replaced with Bootstrap utilities */
  object-fit: cover;
  z-index: 0;
  will-change: transform;
  backface-visibility: hidden;
  pointer-events: none;
}

/* Light Dark Overlay for better text readability */
.tritracz-container::before {
  /* Pseudo-element overlay - Cannot be replaced with Bootstrap utilities */
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgb(11 31 58 / 30%);
  z-index: 1;
}

.hero-section {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: flex-start;
  padding: 60px;
  overflow: hidden;
  transform: translateX(-30px);
  opacity: 0;
  transition: all 1s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 2;
}

.hero-section.loaded {
  transform: translateX(0);
  opacity: 1;
}

.hero-content {
  z-index: 2;
  margin-bottom: 60px;
  max-width: 600px;
  text-align: left;
}

.logo {
  font-size: 24px;
  font-weight: 700;
  color: black;
  margin-bottom: 60px;
  letter-spacing: 2px;
  text-align: left;
}

.hero-title {
  font-size: 48px !important;
  font-weight: 700;
  color: white;
  line-height: 1.2;
  margin-bottom: 25px;
  text-align: center;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-subtitle {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.9) !important;
  line-height: 1.6;
  margin-bottom: 50px !important;
  text-align: center;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.social-icons {
  display: flex;
  gap: 20px;
}

.social-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
}

.social-icon:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.smoke-element.loaded {
  transform: translateY(0) rotate(0deg);
  opacity: 0.8;
}

.smoke-element.floating {
  transform: translateY(-10px) rotate(2deg);
}

.form-section {
  flex: 0 0 600px;
  /* background: rgba(26, 26, 46, 0.95); */
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 60px 40px;
  transform: translateX(30px);
  opacity: 0;
  transition: all 1s cubic-bezier(0.4, 0, 0.2, 1) 0.3s,
    flex 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    width 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94), padding 0.6s ease-in-out;
  z-index: 3;
  /* box-shadow: -10px 0 30px rgba(0, 0, 0, 0.3); */
  overflow: hidden;
  position: relative;
}

.form-section.loaded {
  transform: translateX(0);
  opacity: 1;
}

/* Form Container - REPLACE WITH: w-100 mw-500px */
.form-container {
  width: 100%;
  max-width: 500px;
}

/* Register Form Section - REPLACE WITH: flex-shrink-0 position-relative + custom animation */
.register-page .form-section {
  flex: 0 0 650px;
  padding: 40px 60px;
  position: relative;
  animation: formSectionExpand 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Register Form Container - REPLACE WITH: w-100 + custom animation */
.register-page .form-container {
  width: 100%;
  max-width: 550px;
  animation: containerExpand 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transition: all 0.6s ease-in-out;
}

/* Visual indicators removed - keeping it clean like login */

/* Expansion animation */
@keyframes formSectionExpand {
  0% {
    flex: 0 0 450px;
    padding: 60px 40px;
  }

  50% {
    flex: 0 0 600px;
    padding: 45px 55px;
  }

  100% {
    flex: 0 0 650px;
    padding: 40px 60px;
  }
}

/* Indicator animation */
@keyframes expandIndicator {
  0% {
    opacity: 0;
    transform: scaleY(0);
  }

  30% {
    opacity: 1;
    transform: scaleY(0.5);
  }

  70% {
    opacity: 1;
    transform: scaleY(1);
  }

  100% {
    opacity: 0.3;
    transform: scaleY(1);
  }
}

/* Tablet expansion animation */
@keyframes formSectionExpandTablet {
  0% {
    flex: 0 0 400px;
    padding: 50px 30px;
  }

  50% {
    flex: 0 0 520px;
    padding: 45px 40px;
  }

  100% {
    flex: 0 0 580px;
    padding: 40px 45px;
  }
}

/* Mobile expansion animation */
@keyframes formSectionExpandMobile {
  0% {
    padding: 40px 20px;
  }

  50% {
    padding: 37px 25px;
  }

  100% {
    padding: 35px 30px;
  }
}

/* Glow pulse animation */
@keyframes glowPulse {
  0% {
    opacity: 0;
    transform: scale(0.98);
  }

  25% {
    opacity: 0.3;
    transform: scale(1);
  }

  50% {
    opacity: 0.5;
    transform: scale(1.01);
  }

  75% {
    opacity: 0.3;
    transform: scale(1);
  }

  100% {
    opacity: 0;
    transform: scale(0.98);
  }
}

/* Container expansion animation */
@keyframes containerExpand {
  0% {
    max-width: 350px;
    transform: scale(0.95);
  }

  50% {
    max-width: 480px;
    transform: scale(1.02);
  }

  100% {
    max-width: 550px;
    transform: scale(1);
  }
}

.form-title {
  font-size: 32px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 40px;
  text-align: center;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

/* .input-group {
  margin-bottom: 25px;
} */

.input-label {
  display: block;
  color: #f8f9fa;
  font-size: 14px;
  margin-bottom: 8px;
  font-weight: 500;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.form-input {
  width: 100%;
  padding: 15px 20px;
  background: #ffffff;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  color: #212529;
  font-size: 16px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-sizing: border-box;
}

.form-input::placeholder {
  color: #6c757d;
}

.form-input:focus {
  outline: none;
  border-color: #adb5bd;
  box-shadow: 0 0 0 2px rgba(173, 181, 189, 0.3);
}

.checkbox-container {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 30px;
}

.checkbox-input {
  width: 18px;
  height: 18px;
  accent-color: #6c757d;
}

.checkbox-label {
  color: #f8f9fa;
  font-size: 14px;
  cursor: pointer;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.submit-button {
  width: 100%;
  padding: 15px;
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  border: 2px solid #e9ecef;
  border-radius: 8px;
  color: var(--primary-900);
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.submit-button:hover {
  transform: translateY(-2px);
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-color: #dee2e6;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.login-link {
  text-align: center;
  margin-top: 20px;
  color: #f8f9fa;
  font-size: 14px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.text-link {
  color: #ffffff;
  text-decoration: none;
  transition: all 0.3s ease;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.text-link:hover {
  color: #e9ecef;
  text-decoration: underline;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.4);
}

/* Responsive Styles */
@media (max-width: 968px) {
  .form-section {
    flex: 0 0 400px;
    padding: 50px 30px;
  }

  .register-page .form-section {
    flex: 0 0 580px;
    padding: 40px 45px;
    animation: formSectionExpandTablet 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .register-page .form-container {
    max-width: 480px;
  }
}

@media (max-width: 768px) {
  /* tritracz-container responsive behavior: Use Bootstrap class flex-md-row flex-column */

  /* Ensure video covers properly on mobile */
  .tritracz-video-background {
    object-fit: cover;
    height: 100%;
    /* Mobile video optimizations */
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
  }

  .hero-section {
    flex: 1;
    padding: 40px 20px;
    text-align: left;
    align-items: flex-start;
    justify-content: flex-end;
    min-height: 60vh;
  }

  .form-section {
    flex: none;
    padding: 40px 20px;
    background: rgba(26, 26, 46, 0.98);
    border-left: none;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.3);
  }

  .register-page .form-section {
    padding: 35px 30px;
    animation: formSectionExpandMobile 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .register-page .form-container {
    max-width: 100%;
  }

  .hero-title {
    font-size: 36px;
  }

  .smoke-element {
    display: none;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: 20px;
    min-height: 50vh;
  }

  .form-section {
    padding: 30px 20px;
  }

  .hero-title {
    font-size: 28px;
  }

  .logo {
    font-size: 20px;
    margin-bottom: 40px;
  }

  .hero-subtitle {
    font-size: 16px;
  }

  .form-title {
    font-size: 28px;
    margin-bottom: 30px;
  }
}

@media (max-width: 320px) {
  .hero-section {
    padding: 15px;
    min-height: 45vh;
  }

  .form-section {
    padding: 20px 15px;
  }

  .hero-title {
    font-size: 24px;
  }

  .form-title {
    font-size: 24px;
  }
}

/* Prevent scrollbars during initial load */
.tritracz-container.loading {
  overflow: hidden;
}

/* ===== NEW MAIN LAYOUT STYLES ===== */

/* Body Background - Dark mode with #0b1f3a background */
body {
  background: var(--bg-global) !important;
  color: var(--text-primary) !important;
  margin: 0;
  padding: 0;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif;
}

/* Dark theme body - ensure #0b1f3a background */
[data-theme="dark"] body {
  background: #0b1f3a !important;
  color: #ffffff !important;
}

/* Exception: Auth pages maintain their original background */
[data-theme="dark"] .tritracz-container {
  background: linear-gradient(
    135deg,
    var(--primary-800) 0%,
    var(--primary-600) 100%
  ) !important;
}

/* Light theme body background override */
[data-theme="light"] body {
  background: #ffffff !important;
  color: #0b1f3a !important;
}

/* Main Layout Container - Theme Based */
.new-main-layout {
  /* Bootstrap classes applied in JSX: d-flex vh-100 w-100 position-relative */
  /* Only keeping properties that cannot be replaced with Bootstrap utilities */
  background: var(--bg-global);
}

/* Dark theme main layout */
[data-theme="dark"] .new-main-layout {
  background: #0b1f3a !important;
}

/* Light theme main layout background override */
[data-theme="light"] .new-main-layout {
  background: var(--bg-secondary);
}

/* ===== NEW SIDEBAR STYLES ===== */

.new-sidebar {
  width: 240px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  z-index: 1000;
  position: relative;
  height: 100vh;
}

.new-sidebar.collapsed {
  width: 70px;
}

/* Mobile Sidebar */
.new-sidebar-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: none;
}

@media (max-width: 991.98px) {
  .new-sidebar {
    left: -240px;
    transition: left 0.3s ease;
  }

  .new-sidebar.mobile-open {
    left: 0;
  }

  .new-sidebar-backdrop {
    display: block;
  }
}

/* Sidebar Header */
.new-sidebar-header {
  padding: 16px 20px;
  min-height: 70px;
  display: flex;
  align-items: center;
}

.new-sidebar-logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(
    135deg,
    var(--complementary-600),
    var(--complementary-500)
  );
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  box-shadow: 0 2px 8px rgba(131, 94, 73, 0.4);
}

.new-sidebar.collapsed .logo-text {
  display: none;
}

/* Sidebar Menu - REPLACE WITH: flex-fill py-3 overflow-auto */
.new-sidebar-menu {
  flex: 1;
  padding: 16px 0;
  overflow-y: auto;
}

/* Menu Nav - REPLACE WITH: d-flex flex-column gap-1 px-3 */
.menu-nav {
  display: flex;
  flex-direction: column;
  gap: 2px;
  padding: 0 12px;
}

/* Menu Item - REPLACE WITH: d-flex align-items-center gap-3 px-3 py-2 rounded-2 text-decoration-none small fw-medium */
.menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 12px;
  border-radius: 6px;
  color: var(--text-primary);
  text-decoration: none;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
}

.menu-item:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  text-decoration: none;
  transform: translateX(2px);
}

.menu-item.active {
  background: var(--btn-primary-bg);
  color: var(--btn-primary-text);
  font-weight: 600;
  border: 1px solid var(--btn-primary-bg);
  box-shadow: 0 2px 8px rgba(11, 31, 58, 0.4);
}

.menu-item.active .menu-icon {
  color: var(--text-primary);
}

/* Menu Icon - REPLACE WITH: d-flex align-items-center justify-content-center flex-shrink-0 */
.menu-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

/* Collapsed Menu Label - REPLACE WITH: d-none */
.new-sidebar.collapsed .menu-label {
  display: none;
}

/* Collapsed Menu Item - REPLACE WITH: justify-content-center px-2 py-3 */
.new-sidebar.collapsed .menu-item {
  justify-content: center;
  padding: 12px 8px;
}

/* Sidebar Bottom - REPLACE WITH: p-4 */
.new-sidebar-bottom {
  padding: 20px 16px;
}

/* Bottom Nav - REPLACE WITH: d-flex flex-column gap-1 mb-4 */
.bottom-nav {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 20px;
}

/* User Profile - REPLACE WITH: d-flex align-items-center gap-3 p-3 border rounded-2 */
.user-profile {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: var(--bg-card);
  border: 1px solid var(--border-secondary);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.user-profile:hover {
  background: var(--bg-card-hover);
  border-color: var(--border-primary);
}

.user-avatar {
  width: 32px;
  height: 32px;
  background: linear-gradient(
    135deg,
    var(--triadic-purple-600),
    var(--triadic-purple-500)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-primary);
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(148, 70, 148, 0.4);
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 2px;
}

.user-role {
  font-size: 12px;
  color: var(--text-tertiary);
}

.new-sidebar.collapsed .user-profile {
  display: none;
}

/* ===== NEW MAIN CONTENT STYLES ===== */
/* All new-main-content styles replaced with Bootstrap classes in JSX: flex-fill d-flex flex-column position-relative vh-100 */

/* ===== NEW TOPBAR STYLES ===== */

.new-topbar {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  padding: 12px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 64px;
  position: relative;
  z-index: 1000;
  overflow: visible;
}

/* ===== THEME TOGGLE STYLES ===== */

.theme-toggle-btn {
  background: transparent;
  border: 2px solid var(--border-primary);
  color: var(--text-primary);
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  position: relative;
  overflow: hidden;
}

.theme-toggle-btn:hover {
  background: var(--bg-tertiary);
  border-color: var(--accent-complementary);
  color: var(--text-primary);
  transform: scale(1.05);
}

.theme-toggle-btn:active {
  transform: scale(0.95);
}

/* Theme toggle icon animations */
.theme-icon {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: absolute;
}

.theme-icon.sun {
  opacity: 0;
  transform: rotate(180deg) scale(0.5);
  color: #fbbf24;
  /* Golden yellow for sun */
}

.theme-icon.moon {
  opacity: 1;
  transform: rotate(0deg) scale(1);
  color: #e5e7eb;
  /* Light gray for moon */
}

[data-theme="light"] .theme-icon.sun {
  opacity: 1;
  transform: rotate(0deg) scale(1);
}

[data-theme="light"] .theme-icon.moon {
  opacity: 0;
  transform: rotate(-180deg) scale(0.5);
}

/* Enhanced theme toggle button styles */
.theme-toggle-btn {
  position: relative;
  overflow: hidden;
}

.theme-toggle-btn::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.2) 0%,
    transparent 70%
  );
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
  pointer-events: none;
}

.theme-toggle-btn:hover::before {
  width: 100px;
  height: 100px;
}

/* Light theme specific button styling */
[data-theme="light"] .theme-toggle-btn {
  border-color: var(--border-primary);
}

[data-theme="light"] .theme-toggle-btn:hover {
  background: var(--bg-tertiary);
  border-color: var(--primary-500);
}

/* Theme transition for smooth switching */
* {
  transition: background-color 0.3s ease, color 0.3s ease,
    border-color 0.3s ease, box-shadow 0.3s ease !important;
}

/* Disable transitions during theme switch to prevent flashing */
.theme-switching * {
  transition: none !important;
}

/* ===== LIGHT THEME SPECIFIC OVERRIDES ===== */

/* Light theme text colors for sidebar and topbar */
[data-theme="light"] .new-sidebar {
  color: var(--complementary-800) !important;
}

[data-theme="light"] .new-topbar {
  color: var(--complementary-800) !important;
}

/* Light theme - All sidebar text elements */

[data-theme="light"] .new-sidebar h5 {
  color: var(--primary-900) !important;
}

/* Light theme - All topbar text elements */
[data-theme="light"] .new-topbar .text-white,
[data-theme="light"] .new-topbar h5,
[data-theme="light"] .new-topbar .topbar-text,
[data-theme="light"] .new-topbar .nav-link,
[data-theme="light"] .new-topbar a,
[data-theme="light"] .new-topbar span {
  color: var(--complementary-800) !important;
}

/* Light theme - Icons and interactive elements */
[data-theme="light"] .new-sidebar .icon,
[data-theme="light"] .new-sidebar i,
[data-theme="light"] .new-sidebar .fa,
[data-theme="light"] .new-sidebar .fas,
[data-theme="light"] .new-sidebar .far,
[data-theme="light"] .new-sidebar .fab,
[data-theme="light"] .new-sidebar svg,
[data-theme="light"] .new-sidebar .menu-icon,
[data-theme="light"] .new-sidebar .menu-icon svg,
[data-theme="light"] .new-sidebar .user-avatar,
[data-theme="light"] .new-sidebar .user-avatar svg {
  color: var(--primary-900) !important;
  fill: var(--primary-900) !important;
}

[data-theme="light"] .new-topbar .icon,
[data-theme="light"] .new-topbar i,
[data-theme="light"] .new-topbar .fa,
[data-theme="light"] .new-topbar .fas,
[data-theme="light"] .new-topbar .far,
[data-theme="light"] .new-topbar .fab,
[data-theme="light"] .new-topbar svg,
[data-theme="light"] .new-topbar .menu-icon,
[data-theme="light"] .new-topbar .menu-icon svg {
  color: var(--complementary-800) !important;
  fill: var(--complementary-800) !important;
}

/* Light theme - Hover states */
[data-theme="light"] .new-sidebar .menu-item:hover,
[data-theme="light"] .new-sidebar .nav-link:hover,
[data-theme="light"] .new-sidebar a:hover {
  color: var(--primary-900) !important;
  background-color: rgba(11, 31, 58, 0.08) !important;
  border-radius: 8px !important;
  transform: translateX(4px) !important;
  transition: all 0.2s ease !important;
}

[data-theme="light"] .new-sidebar .menu-item:hover .menu-icon,
[data-theme="light"] .new-sidebar .menu-item:hover .menu-icon svg {
  color: var(--primary-900) !important;
  fill: var(--primary-900) !important;
}

[data-theme="light"] .new-topbar .nav-link:hover,
[data-theme="light"] .new-topbar a:hover {
  color: var(--complementary-800) !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
}

/* Light theme - Active states */
[data-theme="light"] .new-sidebar .menu-item.active,
[data-theme="light"] .new-sidebar .nav-link.active {
  color: #ffffff !important;
  background-color: var(--primary-900) !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3) !important;
}

[data-theme="light"] .new-sidebar .menu-item.active .menu-icon,
[data-theme="light"] .new-sidebar .menu-item.active .menu-icon svg {
  color: #ffffff !important;
  fill: #ffffff !important;
}

[data-theme="light"] .new-topbar .nav-link.active {
  color: #ffffff !important;
  background-color: var(--complementary-700) !important;
  border-radius: 6px !important;
}

/* Dark theme - Ensure white text and icons (default behavior) */
[data-theme="dark"] .new-sidebar .text-white,
[data-theme="dark"] .new-sidebar h5,
[data-theme="dark"] .new-sidebar .icon,
[data-theme="dark"] .new-sidebar i,
[data-theme="dark"] .new-sidebar svg,
[data-theme="dark"] .new-sidebar .menu-icon,
[data-theme="dark"] .new-sidebar .menu-icon svg,
[data-theme="dark"] .new-sidebar .user-avatar,
[data-theme="dark"] .new-sidebar .user-avatar svg,
[data-theme="dark"] .new-topbar .text-white,
[data-theme="dark"] .new-topbar h5,
[data-theme="dark"] .new-topbar .icon,
[data-theme="dark"] .new-topbar i,
[data-theme="dark"] .new-topbar svg,
[data-theme="dark"] .new-topbar .menu-icon,
[data-theme="dark"] .new-topbar .menu-icon svg {
  color: #ffffff !important;
  fill: #ffffff !important;
}

/* Dark theme - Hover states */
[data-theme="dark"] .new-sidebar .menu-item:hover,
[data-theme="dark"] .new-sidebar .nav-link:hover {
  background-color: rgba(255, 255, 255, 0.08) !important;
  border-radius: 8px !important;
  transform: translateX(4px) !important;
  transition: all 0.2s ease !important;
}

[data-theme="dark"] .new-topbar .nav-link:hover {
  background-color: rgba(255, 255, 255, 0.08) !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
}

/* Dark theme - Active states */
[data-theme="dark"] .new-sidebar .menu-item.active,
[data-theme="dark"] .new-sidebar .nav-link.active {
  background-color: var(--primary-800) !important;
  color: #ffffff !important;
  border-radius: 8px !important;
}

[data-theme="dark"] .new-sidebar .menu-item.active .menu-icon,
[data-theme="dark"] .new-sidebar .menu-item.active .menu-icon svg {
  color: #ffffff !important;
  fill: #ffffff !important;
}

[data-theme="dark"] .new-topbar .nav-link.active {
  background-color: var(--primary-600) !important;
  color: #ffffff !important;
  border-radius: 6px !important;
}

/* Light theme content area - ensure white background */
[data-theme="light"] .new-page-content {
  background: #ffffff !important;
}

[data-theme="light"] .new-main-content {
  background: #ffffff !important;
}

/* Light theme card styles */
[data-theme="light"] .card {
  background: #ffffff !important;
  border: 1px solid rgba(11, 31, 58, 0.15) !important;
  box-shadow: 0 2px 12px rgba(11, 31, 58, 0.08) !important;
  border-radius: 12px !important;
}

[data-theme="light"] .card:hover {
  background: #fefefe !important;
  box-shadow: 0 4px 20px rgba(11, 31, 58, 0.12) !important;
  border-color: rgba(11, 31, 58, 0.25) !important;
  transform: translateY(-2px) !important;
  transition: all 0.3s ease !important;
}

/* Dark theme card styles - no borders */
[data-theme="dark"] .card {
  border: none !important;
  background: #1a2f4d !important;
  color: #ffffff !important;
}

/* Dark theme - Enhanced text contrast */
[data-theme="dark"] h1,
[data-theme="dark"] h2,
[data-theme="dark"] h3,
[data-theme="dark"] h4,
[data-theme="dark"] h5,
[data-theme="dark"] h6 {
  color: #ffffff;
}

[data-theme="dark"] .text-muted {
  color: #c7d2e8 !important;
}

[data-theme="dark"] .card-title {
  color: #ffffff !important;
}

[data-theme="dark"] .card-text {
  color: #e8f0ff !important;
}

[data-theme="dark"] .table {
  color: #e8f0ff !important;
}

[data-theme="dark"] .table th {
  color: #ffffff !important;
  background-color: #293f60 !important;
  border-color: #384f73 !important;
}

[data-theme="dark"] .table td {
  color: #e8f0ff !important;
  border-color: #384f73 !important;
}

/* Dark theme form elements */
[data-theme="dark"] .form-control {
  background-color: #1a2f4d;
  border-color: #384f73;
  color: #ffffff !important;
}

[data-theme="dark"] .form-control::placeholder {
  color: #c7d2e8 !important;
}

[data-theme="dark"] .form-label {
  color: #e8f0ff !important;
}

/* ===== AUTHENTICATION LAYOUT EXCLUSIONS ===== */
/* Exclude auth layout from dark theme - maintain original auth styling */

/* Auth layout containers should not be affected by dark theme */
[data-theme="dark"] .tritracz-container,
[data-theme="dark"] .hero-section,
[data-theme="dark"] .form-section,
[data-theme="dark"] .form-container,
[data-theme="dark"] .auth-form,
[data-theme="dark"] .auth-brand-container,
[data-theme="dark"] .auth-card,
[data-theme="dark"] .auth-layout {
  /* Reset to original auth styling - not affected by dark theme */
  color: unset !important;
}

/* Ensure form section maintains glassmorphism effect */
[data-theme="dark"] .form-section {
  background: var(--primary-900) !important;
  backdrop-filter: blur(25px) !important;
  -webkit-backdrop-filter: blur(25px) !important;
  border-left: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Ensure auth video background and overlay work correctly */
[data-theme="dark"] .tritracz-video-background {
  object-fit: cover !important;
  z-index: 0 !important;
}

[data-theme="dark"] .tritracz-container::before {
  content: "" !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: rgb(11 31 58 / 30%) !important;
  z-index: 1 !important;
}

/* Light theme dropdown styles */
[data-theme="light"] .dropdown-menu {
  background: #ffffff !important;
  border: 1px solid rgba(11, 31, 58, 0.15) !important;
  box-shadow: 0 4px 20px rgba(11, 31, 58, 0.15) !important;
  border-radius: 12px !important;
}

[data-theme="light"] .dropdown-item {
  color: var(--primary-900) !important;
  padding: 12px 16px !important;
  border-radius: 8px !important;
  margin: 4px 8px !important;
}

[data-theme="light"] .dropdown-item:hover {
  background-color: rgba(11, 31, 58, 0.08) !important;
  color: var(--primary-900) !important;
}

[data-theme="light"] .dropdown-item i,
[data-theme="light"] .dropdown-item svg {
  color: var(--primary-700) !important;
  margin-right: 8px !important;
}

/* Light theme user profile dropdown */
[data-theme="light"] .user-profile {
  color: var(--primary-900) !important;
  background: rgba(11, 31, 58, 0.05) !important;
  border-radius: 8px !important;
  padding: 8px !important;
  margin: 8px !important;
}

[data-theme="light"] .user-name {
  color: var(--primary-900) !important;
  font-weight: 600 !important;
}

[data-theme="light"] .user-role {
  color: var(--primary-700) !important;
  font-size: 0.875rem !important;
}

/* Light theme profile header in dropdown */
[data-theme="light"] .dropdown-header {
  background: rgba(11, 31, 58, 0.05) !important;
  color: var(--primary-900) !important;
  border-radius: 8px !important;
  margin: 8px !important;
  padding: 12px 16px !important;
}

[data-theme="light"] .dropdown-divider {
  border-color: rgba(11, 31, 58, 0.1) !important;
  margin: 8px 0 !important;
}

/* Light theme - Profile and Notification dropdowns */
[data-theme="light"] .profile-dropdown {
  background: #ffffff !important;
  border: 1px solid rgba(11, 31, 58, 0.15) !important;
  box-shadow: 0 4px 20px rgba(11, 31, 58, 0.15) !important;
}

[data-theme="light"] .notification-dropdown {
  background: #ffffff !important;
  border: 1px solid rgba(11, 31, 58, 0.15) !important;
  box-shadow: 0 4px 20px rgba(11, 31, 58, 0.15) !important;
}

[data-theme="light"] .profile-dropdown-header {
  background: rgba(11, 31, 58, 0.05) !important;
  border-radius: 8px !important;

  padding: 16px !important;
}

[data-theme="light"] .profile-dropdown-header h6 {
  color: var(--primary-900) !important;
  font-weight: 600 !important;
  margin-bottom: 4px !important;
}

[data-theme="light"] .profile-dropdown-header p {
  color: var(--primary-700) !important;
  font-size: 0.875rem !important;
  margin: 0 !important;
}

[data-theme="light"] .profile-menu-item {
  color: var(--primary-900) !important;
  padding: 12px 16px !important;
  border-radius: 8px !important;
  margin: 4px 12px !important;
  display: flex !important;
  align-items: center !important;
  text-decoration: none !important;
}

[data-theme="light"] .profile-menu-item:hover {
  background-color: rgba(11, 31, 58, 0.08) !important;
  color: var(--primary-900) !important;
}

[data-theme="light"] .profile-menu-item svg {
  color: var(--primary-700) !important;
  margin-right: 8px !important;
}

/* Theme menu item styling */
.theme-menu-item {
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

.theme-menu-item:hover {
  background-color: rgba(11, 31, 58, 0.08) !important;
}

[data-theme="dark"] .theme-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.08) !important;
}

/* Theme icon colors */
[data-theme="light"] .theme-menu-item svg {
  color: var(--primary-600) !important;
}

[data-theme="dark"] .theme-menu-item svg {
  color: #e8f0ff !important;
}

[data-theme="light"] .notification-header h6 {
  color: var(--primary-900) !important;
  padding: 16px !important;
  margin: 0 !important;
  border-bottom: 1px solid rgba(11, 31, 58, 0.1) !important;
}

[data-theme="light"] .notification-item {
  padding: 12px 16px !important;
  border-bottom: 1px solid rgba(11, 31, 58, 0.05) !important;
}

[data-theme="light"] .notification-item:hover {
  background-color: rgba(11, 31, 58, 0.05) !important;
}

[data-theme="light"] .notification-content p {
  color: var(--primary-900) !important;
  margin-bottom: 4px !important;
}

[data-theme="light"] .notification-time {
  color: var(--primary-700) !important;
  font-size: 0.75rem !important;
}

/* ===== LIGHT MODE SHIPMENT CARDS ===== */

/* Shipment Cards Grid */
[data-theme="light"] .shipment-card {
  background: #ffffff !important;
  border: 1px solid rgba(11, 31, 58, 0.15) !important;
  box-shadow: 0 2px 12px rgba(11, 31, 58, 0.08) !important;
  border-radius: 16px !important;
}

[data-theme="light"] .shipment-card:hover {
  background: #fefefe !important;
  border-color: rgba(11, 31, 58, 0.25) !important;
  box-shadow: 0 4px 20px rgba(11, 31, 58, 0.12) !important;
  transform: translateY(-2px) !important;
}

/* Shipment Card Header */
[data-theme="light"] .shipment-card-id {
  background: linear-gradient(
    135deg,
    var(--primary-600),
    var(--primary-700)
  ) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
}

[data-theme="light"] .shipment-card-status {
  color: #ffffff !important;
  font-weight: 600 !important;
}

/* Status Colors for Light Mode */
[data-theme="light"] .status-in-transit {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
}

[data-theme="light"] .status-delivered {
  background: linear-gradient(135deg, #10b981, #059669) !important;
}

[data-theme="light"] .status-processing {
  background: linear-gradient(135deg, #f59e0b, #d97706) !important;
}

[data-theme="light"] .status-pending {
  background: linear-gradient(135deg, #6b7280, #4b5563) !important;
}

/* Shipment Card Route */
[data-theme="light"] .shipment-card-route {
  background: rgba(11, 31, 58, 0.05) !important;
  border: 1px solid rgba(11, 31, 58, 0.1) !important;
}

[data-theme="light"] .route-icon {
  color: var(--primary-600) !important;
}

[data-theme="light"] .route-text {
  color: var(--primary-900) !important;
  font-weight: 500 !important;
}

/* Shipment Card Details */
[data-theme="light"] .detail-label {
  color: var(--primary-700) !important;
  font-weight: 500 !important;
}

[data-theme="light"] .detail-value {
  color: var(--primary-900) !important;
  font-weight: 600 !important;
}

[data-theme="light"] .truck-icon {
  color: var(--primary-600) !important;
}

/* Progress Bar */
[data-theme="light"] .progress-label {
  color: var(--primary-700) !important;
  font-weight: 500 !important;
}

[data-theme="light"] .progress-percentage {
  color: var(--primary-900) !important;
  font-weight: 600 !important;
}

[data-theme="light"] .progress-bar-card {
  background: rgba(11, 31, 58, 0.1) !important;
  border-radius: 8px !important;
}

[data-theme="light"] .progress-fill-card {
  background: linear-gradient(
    135deg,
    var(--primary-600),
    var(--primary-700)
  ) !important;
  border-radius: 8px !important;
}

/* ETA Info */
[data-theme="light"] .eta-label {
  color: var(--primary-700) !important;
  font-weight: 500 !important;
}

[data-theme="light"] .eta-value {
  color: var(--primary-900) !important;
  font-weight: 600 !important;
}

/* ===== LIGHT MODE DASHBOARD CARDS ===== */

/* Dashboard Card Container */
[data-theme="light"] .dashboard-card {
  background: #ffffff !important;
  border: 1px solid rgba(11, 31, 58, 0.15) !important;
  box-shadow: 0 2px 12px rgba(11, 31, 58, 0.08) !important;
  border-radius: 16px !important;
}

[data-theme="light"] .dashboard-card:hover {
  background: #fefefe !important;
  border-color: rgba(11, 31, 58, 0.25) !important;
  box-shadow: 0 4px 20px rgba(11, 31, 58, 0.12) !important;
  transform: translateY(-2px) !important;
}

/* Dashboard Card Header */
[data-theme="light"] .card-header h3 {
  background: linear-gradient(
    135deg,
    var(--primary-700),
    var(--primary-800)
  ) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
}

[data-theme="light"] .card-badge {
  background: linear-gradient(
    135deg,
    var(--primary-600),
    var(--primary-700)
  ) !important;
  color: #ffffff !important;
  border: 1px solid var(--primary-500) !important;
  box-shadow: 0 2px 8px rgba(11, 31, 58, 0.2) !important;
}

/* Shipment Items in Dashboard */
[data-theme="light"] .shipment-item {
  background: rgba(11, 31, 58, 0.05) !important;
  border: 1px solid rgba(11, 31, 58, 0.1) !important;
  border-radius: 12px !important;
}

[data-theme="light"] .shipment-item:hover {
  background: rgba(11, 31, 58, 0.08) !important;
  border-color: rgba(11, 31, 58, 0.2) !important;
  box-shadow: 0 4px 12px rgba(11, 31, 58, 0.1) !important;
}

[data-theme="light"] .shipment-id {
  background: linear-gradient(
    135deg,
    var(--primary-600),
    var(--primary-700)
  ) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
}

[data-theme="light"] .shipment-destination {
  color: var(--primary-700) !important;
}

/* Status Badges in Dashboard */
[data-theme="light"] .status-badge {
  color: #ffffff !important;
  font-weight: 600 !important;
  border-radius: 20px !important;
  padding: 6px 12px !important;
}

[data-theme="light"] .status-in-transit {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
}

[data-theme="light"] .status-delivered {
  background: linear-gradient(135deg, #10b981, #059669) !important;
}

[data-theme="light"] .status-processing {
  background: linear-gradient(135deg, #f59e0b, #d97706) !important;
}

[data-theme="light"] .status-pending {
  background: linear-gradient(135deg, #6b7280, #4b5563) !important;
}

/* Progress Bars in Dashboard */
[data-theme="light"] .progress-bar {
  background: linear-gradient(
    135deg,
    var(--primary-600),
    var(--primary-700)
  ) !important;
}

[data-theme="light"] .progress {
  background: rgba(11, 31, 58, 0.1) !important;
}

/* Quick Actions Cards */
[data-theme="light"] .quick-action-item {
  background: #ffffff !important;
  border: 1px solid rgba(11, 31, 58, 0.15) !important;
  box-shadow: 0 2px 8px rgba(11, 31, 58, 0.08) !important;
  border-radius: 12px !important;
}

[data-theme="light"] .quick-action-item:hover {
  background: #fefefe !important;
  border-color: rgba(11, 31, 58, 0.25) !important;
  box-shadow: 0 4px 16px rgba(11, 31, 58, 0.12) !important;
  transform: translateY(-2px) !important;
}

[data-theme="light"] .quick-action-icon {
  color: var(--primary-600) !important;
}

[data-theme="light"] .quick-action-title {
  color: var(--primary-900) !important;
  font-weight: 600 !important;
}

[data-theme="light"] .quick-action-desc {
  color: var(--primary-700) !important;
}

/* ===== STATS GRID ICONS FIX ===== */

/* Light mode stats icons visibility */
[data-theme="light"] .stat-icon {
  color: #ffffff !important;
  background: var(--primary-600) !important;
}

[data-theme="light"] .stat-card-blue .stat-icon {
  background: linear-gradient(
    135deg,
    var(--primary-500),
    var(--primary-600)
  ) !important;
}

[data-theme="light"] .stat-card-green .stat-icon {
  background: linear-gradient(135deg, #10b981, #059669) !important;
}

[data-theme="light"] .stat-card-orange .stat-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706) !important;
}

[data-theme="light"] .stat-card-purple .stat-icon {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed) !important;
}

/* ===== QUICK ACTIONS TEXT FIX ===== */

/* Light mode quick actions text visibility */
[data-theme="light"] .quick-action-title {
  color: var(--primary-900) !important;
  font-weight: 600 !important;
}

[data-theme="light"] .quick-action-desc {
  color: var(--primary-700) !important;
  font-weight: 500 !important;
}

/* Quick action icons light mode */
[data-theme="light"] .quick-action-icon {
  color: #ffffff !important;
}

[data-theme="light"] .quick-action-blue {
  background: linear-gradient(
    135deg,
    var(--primary-500),
    var(--primary-600)
  ) !important;
}

[data-theme="light"] .quick-action-green {
  background: linear-gradient(135deg, #10b981, #059669) !important;
}

[data-theme="light"] .quick-action-orange {
  background: linear-gradient(135deg, #f59e0b, #d97706) !important;
}

[data-theme="light"] .quick-action-purple {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed) !important;
}

/* Stats Cards */
[data-theme="light"] .stats-card {
  background: #ffffff !important;
  border: 1px solid rgba(11, 31, 58, 0.15) !important;
  box-shadow: 0 2px 12px rgba(11, 31, 58, 0.08) !important;
  border-radius: 16px !important;
}

[data-theme="light"] .stats-card:hover {
  background: #fefefe !important;
  border-color: rgba(11, 31, 58, 0.25) !important;
  box-shadow: 0 4px 20px rgba(11, 31, 58, 0.12) !important;
  transform: translateY(-2px) !important;
}

[data-theme="light"] .stats-number {
  color: var(--primary-900) !important;
  font-weight: 700 !important;
}

[data-theme="light"] .stats-label {
  color: var(--primary-700) !important;
  font-weight: 500 !important;
}

[data-theme="light"] .stats-icon {
  color: var(--primary-600) !important;
}

/* ===== ACTUAL STAT CARDS STYLING ===== */

/* Light mode stat cards */
[data-theme="light"] .stat-card {
  background: #ffffff !important;
  border: 1px solid rgba(11, 31, 58, 0.15) !important;
}

[data-theme="light"] .stat-card:hover {
  background: #fefefe !important;
  border-color: rgba(11, 31, 58, 0.25) !important;
  box-shadow: 0 4px 20px rgba(11, 31, 58, 0.12) !important;
}

[data-theme="light"] .stat-value {
  color: var(--primary-900) !important;
  text-shadow: none !important;
}

[data-theme="light"] .stat-title {
  color: var(--primary-700) !important;
}

[data-theme="light"] .stat-change {
  color: inherit !important;
}

/* Dark mode stat cards - brighter colors for better contrast */
[data-theme="dark"] .stat-value {
  color: #ffffff !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

[data-theme="dark"] .stat-title {
  color: #e8f0ff !important;
}

/* Dashboard Grid Items */
[data-theme="light"] .dashboard-grid .card {
  background: #ffffff !important;
  border: 1px solid rgba(11, 31, 58, 0.15) !important;
  box-shadow: 0 2px 12px rgba(11, 31, 58, 0.08) !important;
}

[data-theme="light"] .dashboard-grid .card:hover {
  border-color: rgba(11, 31, 58, 0.25) !important;
  box-shadow: 0 4px 20px rgba(11, 31, 58, 0.12) !important;
}

/* Table Styling for Dashboard */
[data-theme="light"] .table {
  color: var(--primary-900) !important;
}

[data-theme="light"] .table th {
  color: var(--primary-800) !important;
  font-weight: 600 !important;
  border-color: rgba(11, 31, 58, 0.1) !important;
}

[data-theme="light"] .table td {
  color: var(--primary-900) !important;
  border-color: rgba(11, 31, 58, 0.05) !important;
}

[data-theme="light"] .table-hover tbody tr:hover {
  background-color: rgba(11, 31, 58, 0.03) !important;
}

/* Badge Styling */
[data-theme="light"] .badge.bg-success {
  background: linear-gradient(135deg, #10b981, #059669) !important;
}

[data-theme="light"] .badge.bg-primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
}

[data-theme="light"] .badge.bg-warning {
  background: linear-gradient(135deg, #f59e0b, #d97706) !important;
  color: #ffffff !important;
}

/* Button Styling for Dashboard */
[data-theme="light"] .btn-outline-primary {
  color: var(--primary-700) !important;
  border-color: var(--primary-700) !important;
}

[data-theme="light"] .btn-outline-primary:hover {
  background-color: var(--primary-700) !important;
  border-color: var(--primary-700) !important;
  color: #ffffff !important;
}

/* ===== THEME TOGGLE SWITCH REDESIGN ===== */

.theme-toggle-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.theme-toggle-switch {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  border-radius: 50px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  outline: none;
}

.theme-toggle-switch:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

.theme-toggle-track {
  width: 60px;
  height: 30px;
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  border-radius: 50px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: inset 0 2px 4px rgba(11, 31, 58, 0.2),
    0 2px 8px rgba(11, 31, 58, 0.15);
}

.theme-toggle-switch.dark .theme-toggle-track {
  background: linear-gradient(135deg, var(--primary-800), var(--primary-900));
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3),
    0 2px 8px rgba(11, 31, 58, 0.4);
}

.theme-toggle-thumb {
  width: 26px;
  height: 26px;
  background: #ffffff;
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(11, 31, 58, 0.15), 0 1px 3px rgba(11, 31, 58, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  border: 1px solid rgba(11, 31, 58, 0.1);
}

.theme-toggle-switch.dark .theme-toggle-thumb {
  transform: translateX(30px);
  background: var(--primary-100);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2), 0 1px 3px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-icon-container {
  position: relative;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.theme-icon {
  position: absolute;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sun-icon {
  color: var(--primary-600);
  opacity: 1;
  transform: rotate(0deg) scale(1);
}

.moon-icon {
  color: var(--primary-400);
  opacity: 0;
  transform: rotate(180deg) scale(0.5);
}

.theme-toggle-switch.dark .sun-icon {
  opacity: 0;
  transform: rotate(-180deg) scale(0.5);
}

.theme-toggle-switch.dark .moon-icon {
  color: var(--primary-200);
  opacity: 1;
  transform: rotate(0deg) scale(1);
}

.theme-toggle-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 8px;
  z-index: 1;
}

.theme-bg-sun,
.theme-bg-moon {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0.6;
}

.theme-bg-sun {
  color: var(--primary-300);
  opacity: 0.8;
}

.theme-bg-moon {
  color: var(--primary-400);
  opacity: 0.3;
}

.theme-toggle-switch.dark .theme-bg-sun {
  opacity: 0.2;
}

.theme-toggle-switch.dark .theme-bg-moon {
  color: var(--primary-300);
  opacity: 0.8;
}

/* Hover effects */
.theme-toggle-switch:hover .theme-toggle-track {
  box-shadow: inset 0 2px 4px rgba(11, 31, 58, 0.25),
    0 4px 12px rgba(11, 31, 58, 0.2);
}

.theme-toggle-switch.dark:hover .theme-toggle-track {
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.4),
    0 4px 12px rgba(11, 31, 58, 0.5);
}

.theme-toggle-switch:hover .theme-toggle-thumb {
  box-shadow: 0 4px 12px rgba(11, 31, 58, 0.2), 0 2px 6px rgba(11, 31, 58, 0.15);
  transform: scale(1.05);
}

.theme-toggle-switch.dark:hover .theme-toggle-thumb {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3), 0 2px 6px rgba(0, 0, 0, 0.2);
  transform: translateX(30px) scale(1.05);
}

/* Active state */
.theme-toggle-switch:active .theme-toggle-thumb {
  transform: scale(0.95);
  transition: all 0.1s ease;
}

.theme-toggle-switch.dark:active .theme-toggle-thumb {
  transform: translateX(30px) scale(0.95);
  transition: all 0.1s ease;
}

/* Focus state */
.theme-toggle-switch:focus {
  box-shadow: 0 0 0 3px rgba(11, 31, 58, 0.3);
}

/* ===== SIDEBAR SIMPLE SMOOTH ANIMATIONS ===== */

/* Simple sidebar width transition only */
.new-sidebar {
  transition: width 0.3s ease !important;
}

/* Simple icon transitions when collapsed */
.new-sidebar .menu-icon,
.new-sidebar .logo-icon {
  transition: transform 0.2s ease;
}

.new-sidebar.collapsed .menu-icon {
  transform: scale(1.1);
}

.new-sidebar.collapsed .logo-icon {
  transform: scale(1.1);
}

/* Center logo when collapsed */
.new-sidebar.collapsed .new-sidebar-logo {
  justify-content: center;
}

/* ===== PAGE HEADER STATIC COLORS ===== */

/* Light mode - Dark colors */
[data-theme="light"] .page-header-title {
  color: var(--primary-900) !important;
  text-shadow: none !important;
}

[data-theme="light"] .page-header-description {
  color: var(--primary-700) !important;
  text-shadow: none !important;
}

/* Dark mode - Brighter colors for better contrast */
[data-theme="dark"] .page-header-title {
  color: #ffffff !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

[data-theme="dark"] .page-header-description {
  color: #e8f0ff !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

/* ===== STAT CARDS ALIGNMENT ===== */

/* Stats card container alignment - Replaced with Bootstrap grid utilities */

.stats-card {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 24px 20px;
  min-height: 140px;
}

.stats-card .stats-icon {
  margin-bottom: 12px;
  font-size: 2rem;
}

.stats-card .stats-number {
  font-size: 2.5rem;
  line-height: 1.2;
  margin-bottom: 8px;
}

.stats-card .stats-label {
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 0;
}

/* Alternative horizontal layout for stats */
.stats-card.horizontal {
  flex-direction: row;
  text-align: left;
  align-items: center;
  justify-content: flex-start;
  gap: 16px;
}

.stats-card.horizontal .stats-icon {
  margin-bottom: 0;
  font-size: 2.5rem;
  flex-shrink: 0;
}

.stats-card.horizontal .stats-content {
  display: flex;
  flex-direction: column;
}

.stats-card.horizontal .stats-number {
  font-size: 2rem;
  margin-bottom: 4px;
}

/* Light theme menu item styles */
[data-theme="light"] .menu-item:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

/* Light theme notification and profile dropdown styles */
[data-theme="light"] .notification-dropdown,
[data-theme="light"] .profile-dropdown {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  box-shadow: 0 8px 32px var(--shadow-medium);
}

/* Light theme button styles */
[data-theme="light"] .notification-btn:hover,
[data-theme="light"] .profile-btn:hover,
[data-theme="light"] .mobile-menu-btn:hover,
[data-theme="light"] .sidebar-toggle-btn:hover {
  background: var(--bg-tertiary);
}

/* AuthLayout uses original styling - no light theme overrides for forms */

/* AuthLayout always uses white/grey text - no theme overrides */

/* Auth form links use original styling */

/* AuthLayout password icons always white */
.auth-form .btn.btn-link.position-absolute.text-muted,
.auth-form .btn-link.position-absolute,
.auth-form .position-absolute.text-muted {
  color: #ffffff !important;
}

.auth-form .btn.btn-link.position-absolute.text-muted:hover,
.auth-form .btn-link.position-absolute:hover,
.auth-form .position-absolute.text-muted:hover {
  color: rgba(255, 255, 255, 0.8) !important;
}

/* Password toggle icons specifically */
.auth-form .btn-link svg,
.auth-form .position-absolute svg {
  color: #ffffff !important;
}

.auth-form .btn-link:hover svg,
.auth-form .position-absolute:hover svg {
  color: rgba(255, 255, 255, 0.8) !important;
}

/* Light theme dashboard styles */
[data-theme="light"] .dashboard-home .card {
  background: var(--bg-card) !important;
  border: 1px solid var(--border-primary) !important;
  box-shadow: 0 2px 8px var(--shadow-light) !important;
}

[data-theme="light"] .dashboard-home .card:hover {
  background: var(--bg-card-hover) !important;
  box-shadow: 0 4px 12px var(--shadow-medium) !important;
}

[data-theme="light"] .dashboard-home .card-header {
  background: var(--bg-card) !important;
  border-bottom: 1px solid var(--border-primary) !important;
}

[data-theme="light"] .dashboard-home .card-header h5 {
  color: var(--text-primary);
}

/* Light theme table styles */
[data-theme="light"] .table {
  color: var(--text-primary);
}

[data-theme="light"] .table th {
  color: var(--text-primary);
  border-bottom: 2px solid var(--border-primary);
}

[data-theme="light"] .table td {
  border-bottom: 1px solid var(--border-secondary);
}

[data-theme="light"] .table-hover tbody tr:hover {
  background-color: var(--bg-tertiary);
}

/* Light theme badge styles */
[data-theme="light"] .badge {
  color: var(--white);
}

/* Light theme progress bar styles */
[data-theme="light"] .progress {
  background-color: var(--bg-tertiary);
}

/* Light theme form section background for auth pages */
[data-theme="light"] .form-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-left: 1px solid var(--border-primary);
}

/* Light theme hero section text */
[data-theme="light"] .hero-title,
[data-theme="light"] .hero-subtitle,
[data-theme="light"] .logo {
  color: var(--text-primary) !important;
  text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
}

/* Light theme input styles */
[data-theme="light"] .form-input {
  background: var(--bg-primary);
  border-color: var(--border-primary);
  color: var(--text-primary);
}

[data-theme="light"] .form-input:focus {
  border-color: var(--btn-primary-bg);
  box-shadow: 0 0 0 2px rgba(11, 31, 58, 0.25);
}

/* Light theme button styles */
[data-theme="light"] .submit-button {
  background: var(--btn-primary-bg);
  border-color: var(--btn-primary-bg);
  color: var(--btn-primary-text);
}

[data-theme="light"] .submit-button:hover {
  background: var(--btn-primary-hover);
  border-color: var(--btn-primary-hover);
}

/* Light theme secondary button styles */
[data-theme="light"] .btn-secondary,
[data-theme="light"] .dashboard-home .btn-secondary {
  background: var(--btn-secondary-bg);
  border-color: var(--btn-secondary-bg);
  color: var(--btn-secondary-text);
}

[data-theme="light"] .btn-secondary:hover,
[data-theme="light"] .dashboard-home .btn-secondary:hover {
  background: var(--btn-secondary-hover);
  border-color: var(--btn-secondary-hover);
}

/* Light theme text link styles */
[data-theme="light"] .text-link {
  color: var(--btn-primary-bg);
  text-shadow: none;
}

[data-theme="light"] .text-link:hover {
  color: var(--btn-primary-hover);
  text-shadow: none;
}

/* Topbar Left Section */
.topbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.mobile-menu-btn,
.sidebar-toggle-btn {
  background: transparent;
  border: none;
  color: var(--text-primary);
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.mobile-menu-btn:hover,
.sidebar-toggle-btn:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.page-title h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

/* Topbar Right Section */
.topbar-right {
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
  overflow: visible;
}

/* Notification Styles */
.notification-wrapper {
  position: relative;
  z-index: 1000;
}

.notification-btn {
  background: transparent;
  border: none;
  color: var(--text-primary);
  padding: 10px;
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-btn:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.notification-badge {
  position: absolute;
  top: 2px;
  right: 2px;
  background: linear-gradient(135deg, #ff6b6b, #ff8e53);
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(11, 31, 58, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  width: 320px;
  max-height: 400px;
  overflow-y: auto;
  /* High z-index for dropdown layering - Cannot be replaced with Bootstrap utilities */
  z-index: 9999;
  margin-top: 8px;
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  transition: all 0.3s ease;
}

.notification-header {
  padding: 16px 20px 12px;
  border-bottom: 1px solid var(--border-primary);
}

.notification-header h6 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.notification-list {
  padding: 8px 0;
}

.notification-item {
  padding: 12px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.2s ease;
  cursor: pointer;
}

.notification-item:hover {
  background: var(--bg-tertiary);
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-content p {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: var(--text-primary);
  font-weight: 500;
}

.notification-time {
  font-size: 12px;
  color: var(--text-secondary);
}

/* Profile Styles */
.profile-wrapper {
  position: relative;
  z-index: 1000;
}

.profile-btn {
  background: transparent;
  border: none;
  padding: 8px 12px;
  border-radius: 12px;
  transition: all 0.2s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 12px;
}

.profile-btn:hover {
  background: var(--triadic-purple-800);
}

.profile-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid var(--triadic-purple-500);
}

.profile-avatar-large {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid var(--triadic-purple-500);
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.profile-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.2;
}

.profile-role {
  font-size: 12px;
  color: var(--text-secondary);
  line-height: 1.2;
}

.profile-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(11, 31, 58, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  width: 280px;
  z-index: 9999;
  margin-top: 8px;
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  transition: all 0.3s ease;
}

.profile-dropdown-header {
  padding: 20px;
  border-bottom: 1px solid var(--border-primary);
  display: flex;
  align-items: center;
  gap: 16px;
}

.profile-details h6 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.profile-details p {
  margin: 0;
  font-size: 14px;
  color: var(--text-secondary);
}

.profile-menu {
  padding: 8px 0;
}

.profile-menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  color: var(--text-primary);
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.profile-menu-item:hover {
  background: var(--triadic-purple-800);
  color: var(--text-primary);
  text-decoration: none;
}

/* ===== CONTENT CONTAINER STYLES ===== */

.new-page-content {
  /* Bootstrap classes applied in JSX: flex-fill overflow-auto */
  /* Only keeping properties that cannot be replaced with Bootstrap utilities */
  background: transparent;
}

.content-container {
  border: 1px solid var(--border-primary);
  border-bottom: none !important;
  border-radius: 30px 0 0 0;
  min-height: calc(100vh - 70px);
  margin: 0px 0px 0px 14px;
  padding: 25px;
  border-right-width: 0px;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

/* ===== SIMPLE DASHBOARD STYLES ===== */

/* Dashboard Simple - REPLACE WITH: py-4 */
.dashboard-simple {
  padding: 20px 0;
}

/* Dashboard Simple H1 - REPLACE WITH: fs-4 fw-bold text-primary mb-2 */
.dashboard-simple h1 {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 8px;
  background: linear-gradient(135deg, var(--primary-300), var(--primary-200));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Dashboard Simple P - REPLACE WITH: text-secondary small m-0 */
.dashboard-simple p {
  color: var(--text-secondary);
  font-size: 14px;
  margin: 0;
}

/* ===== ENHANCED DASHBOARD STYLES ===== */

/* Dashboard Enhanced - REPLACE WITH: p-4 bg-transparent min-vh-100 */
.dashboard-enhanced {
  padding: 24px;
  background: transparent;
  min-height: calc(100vh - 70px);
}

/* Dashboard Header - REPLACE WITH: d-flex justify-content-between align-items-start mb-5 pb-4 border-bottom */
.dashboard-header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

/* Welcome Section H1 - REPLACE WITH: fs-3 fw-bold mb-2 */
.welcome-section h1 {
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(
    135deg,
    var(--analogous-teal-400),
    var(--complementary-500),
    var(--triadic-purple-500)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 8px;
}

/* Welcome Section P - REPLACE WITH: text-secondary fs-6 m-0 fw-medium */
.welcome-section p {
  color: var(--text-secondary);
  font-size: 16px;
  margin: 0;
  font-weight: 500;
}

/* Dashboard Date - REPLACE WITH: d-flex align-items-center text-primary small fw-medium px-3 py-2 rounded-2 border */
.dashboard-date {
  display: flex;
  align-items: center;
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 500;
  background: linear-gradient(
    135deg,
    var(--complementary-800),
    var(--complementary-700)
  );
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid var(--complementary-600);
  box-shadow: 0 4px 15px rgba(93, 68, 51, 0.4);
}

/* Stats Grid - Replaced with Bootstrap grid utilities (row g-4 mb-4, col-xl-3 col-md-6) */

/* Stat Card - REPLACE WITH: d-flex align-items-center gap-3 p-4 rounded-4 border position-relative overflow-hidden */
.stat-card {
  background: linear-gradient(135deg, var(--bg-card), var(--bg-secondary));
  border: 1px solid var(--border-primary);
  border-radius: 16px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: left 0.6s;
}

.stat-card:hover::before {
  left: 100%;
}

.stat-card:hover {
  transform: translateY(-4px) scale(1.02);

  background: linear-gradient(135deg, var(--bg-card-hover), var(--bg-tertiary));
  border-color: var(--border-primary);
}

/* Use Bootstrap classes: d-flex align-items-center justify-content-center rounded-3 */
.stat-icon {
  /* Only keeping properties that cannot be replaced with Bootstrap utilities */
  width: 48px;
  height: 48px;
  font-size: 20px;
  color: #ffffff;
}

.stat-card-blue .stat-icon {
  background: linear-gradient(
    135deg,
    var(--primary-400),
    var(--primary-500),
    var(--primary-600)
  );
}

.stat-card-green .stat-icon {
  background: linear-gradient(
    135deg,
    var(--analogous-teal-400),
    var(--analogous-teal-500),
    var(--analogous-teal-600)
  );
}

.stat-card-orange .stat-icon {
  background: linear-gradient(
    135deg,
    var(--complementary-400),
    var(--complementary-500),
    var(--complementary-600)
  );
}

.stat-card-purple .stat-icon {
  background: linear-gradient(
    135deg,
    var(--analogous-purple-400),
    var(--analogous-purple-500),
    var(--analogous-purple-600)
  );
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 4px;
  text-shadow: 0 2px 4px rgba(11, 31, 58, 0.5);
}

.stat-title {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 8px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-change {
  font-size: 12px;
  font-weight: 600;
}

.stat-change.positive {
  color: var(--analogous-teal-300);

  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  border: 1px solid var(--analogous-teal-600);
  box-shadow: 0 2px 8px rgba(59, 137, 138, 0.2);
}

.stat-change.negative {
  color: var(--triadic-red-300);
  background: linear-gradient(
    135deg,
    var(--triadic-red-900),
    var(--triadic-red-800)
  );
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  border: 1px solid var(--triadic-red-600);
  box-shadow: 0 2px 8px rgba(177, 83, 83, 0.2);
}

/* Dashboard Grid - Replaced with Bootstrap grid utilities (row, col-lg-8, col-lg-4) */

/* Dashboard Cards */
.dashboard-card {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(11, 31, 58, 0.4);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.dashboard-card:hover {
  box-shadow: 0 8px 32px rgba(11, 31, 58, 0.6);
  background: var(--bg-card-hover);
  border-color: var(--border-primary);
  transform: translateY(-2px);
}

.card-header {
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-primary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--bg-secondary);
}

.card-header h3 {
  font-size: 18px;
  font-weight: 600;
  background: linear-gradient(
    135deg,
    var(--text-primary),
    var(--text-secondary)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.card-badge {
  background: linear-gradient(
    135deg,
    var(--analogous-purple-700),
    var(--analogous-purple-600)
  );
  color: var(--text-primary);
  padding: 6px 14px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  border: 1px solid var(--analogous-purple-500);
  box-shadow: 0 4px 15px rgba(80, 57, 119, 0.4);
  text-shadow: 0 1px 2px rgba(11, 31, 58, 0.5);
}

.card-content {
  padding: 24px;
}

/* Shipments List */
.shipments-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.shipment-item {
  display: grid;
  grid-template-columns: 1fr auto 120px;
  gap: 16px;
  align-items: center;
  padding: 18px;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.08),
    rgba(255, 255, 255, 0.03)
  );
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.shipment-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;

  opacity: 0;
  transition: opacity 0.3s ease;
}

.shipment-item:hover::before {
  opacity: 1;
}

.shipment-item:hover {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.12),
    rgba(255, 255, 255, 0.06)
  );
  border-color: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.shipment-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.shipment-id {
  font-weight: 700;
  background: linear-gradient(135deg, #60a5fa, #34d399);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 14px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.shipment-destination {
  display: flex;
  align-items: center;
  color: #94a3b8;
  font-size: 13px;
  font-weight: 500;
}

.shipment-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-in-transit {
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.2),
    rgba(99, 102, 241, 0.2)
  );
  color: #93c5fd;
  border: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
}

.status-delivered {
  background: linear-gradient(
    135deg,
    rgba(16, 185, 129, 0.2),
    rgba(52, 211, 153, 0.2)
  );
  color: #6ee7b7;
  border: 1px solid rgba(16, 185, 129, 0.3);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.2);
}

.status-processing {
  background: linear-gradient(
    135deg,
    rgba(245, 158, 11, 0.2),
    rgba(251, 191, 36, 0.2)
  );
  color: #fcd34d;
  border: 1px solid rgba(245, 158, 11, 0.3);
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.2);
}

.shipment-eta {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
}

.shipment-progress {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: flex-end;
}

.progress-bar {
  width: 100px;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  overflow: hidden;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #60a5fa, #34d399, #fbbf24);
  border-radius: 10px;
  transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
  position: relative;
}

.progress-fill::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.3), transparent);
  border-radius: 10px 10px 0 0;
}

.progress-text {
  font-size: 11px;
  color: #94a3b8;
  font-weight: 600;
}

/* Quick Actions List */
.quick-actions-list {
  display: flex;
  flex-direction: column;
  gap: 14px;
}

.quick-action-item {
  background: transparent;
  border: none;
  border-radius: 12px;
  padding: 18px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.quick-action-item:hover {
  background: transparent;
  transform: none;
}

.quick-action-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #ffffff;
  flex-shrink: 0;
}

.quick-action-blue {
  background: linear-gradient(135deg, #60a5fa, #3b82f6, #1e40af);
}

.quick-action-orange {
  background: linear-gradient(135deg, #fbbf24, #f59e0b, #d97706);
}

.quick-action-green {
  background: linear-gradient(135deg, #34d399, #10b981, #047857);
}

.quick-action-purple {
  background: linear-gradient(135deg, #a78bfa, #8b5cf6, #7c3aed);
}

.quick-action-content {
  flex: 1;
}

.quick-action-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
}

.quick-action-desc {
  font-size: 12px;
  color: #94a3b8;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1200px) {
  /* dashboard-grid responsive behavior now handled by Bootstrap grid (col-lg-*) */

  /* stats-grid responsive behavior now handled by Bootstrap grid (col-xl-3 col-md-6) */
}

@media (max-width: 768px) {
  .dashboard-enhanced {
    padding: 16px;
  }

  /* dashboard-header responsive flexbox behavior handled by Bootstrap utilities: flex-column gap-3 align-items-start */

  /* stats-grid responsive behavior now handled by Bootstrap grid (col-xl-3 col-md-6) */

  .stat-card {
    padding: 20px;
  }

  .shipment-item {
    grid-template-columns: 1fr;
    gap: 12px;
    text-align: left;
  }

  .shipment-status,
  .shipment-progress {
    align-items: flex-start;
  }

  .card-content {
    padding: 16px;
  }

  .card-header {
    padding: 16px 20px;
  }

  .quick-actions-list {
    gap: 12px;
  }

  .quick-action-item {
    padding: 16px;
  }
}

/* ===== NEW TOPBAR ENHANCED STYLES ===== */

.new-topbar-enhanced {
  padding: 12px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 64px;
}

.topbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.mobile-menu-btn,
.sidebar-toggle-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.mobile-menu-btn:hover,
.sidebar-toggle-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

.page-title h4 {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.9);
  letter-spacing: -0.025em;
}

.topbar-center {
  flex: 0 0 400px;
  max-width: 400px;
  margin: 0 24px;
}

.search-form {
  width: 100%;
}

.search-input-wrapper {
  position: relative;
  width: 100%;
}

.search-input {
  width: 100%;
  padding: 10px 16px 10px 40px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  transition: all 0.2s ease;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.search-input:focus {
  outline: none;
  border-color: #10b981;
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.5);
  pointer-events: none;
}

.topbar-right {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-shrink: 0;
}

/* Date display removed - no longer needed */

.notification-btn {
  position: relative;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.notification-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

.notification-badge {
  position: absolute;
  top: 4px;
  right: 4px;
  background: #ef4444;
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  text-align: center;
}

.add-shipment-btn {
  background: #10b981;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.add-shipment-btn:hover {
  background: #059669;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

/* Filter Button */
.filter-btn {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 10px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.filter-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.9);
  transform: translateY(-1px);
}

/* Export Button */
.export-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.export-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* ===== NEW PAGE CONTENT ENHANCED STYLES ===== */

.new-page-content-enhanced {
  flex: 1;
  overflow-y: auto;
  background: #f8fafc;
}

/* ===== DASHBOARD STYLES ===== */

.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-content h1 {
  font-size: 24px;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 4px;
}

.dashboard-content p {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 24px;
  font-size: 14px;
}

/* Stats Cards - Replaced with Bootstrap grid utilities (row, col-xl-3, col-md-6) */

/* Use Bootstrap classes: d-flex align-items-center justify-content-center rounded-2 flex-shrink-0 */
.stat-icon {
  /* Only keeping properties that cannot be replaced with Bootstrap utilities */
  width: 48px;
  height: 48px;
  color: white;
}

.shipments-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.orders-icon {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.revenue-icon {
  background: linear-gradient(135deg, #10b981, #059669);
}

.delivered-icon {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.stat-content {
  flex: 1;
}

.stat-content h3 {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  margin-bottom: 4px;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.change-badge {
  color: #10b981;
  font-weight: 600;
}

.change-text {
  color: #6b7280;
}

/* Analytics Section */
.analytics-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 16px;
  margin-bottom: 24px;
}

.analytics-card,
.cashflow-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #f1f5f9;
}

.analytics-header,
.cashflow-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.analytics-header h3,
.cashflow-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.dropdown-btn,
.export-btn {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 12px;
  color: #6b7280;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
}

.dropdown-btn:hover,
.export-btn:hover {
  background: #f3f4f6;
}

.analytics-stats {
  display: flex;
  gap: 24px;
  margin-bottom: 16px;
}

.analytics-stat {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
}

.stat-growth {
  font-size: 12px;
  color: #10b981;
  font-weight: 600;
}

.chart-placeholder {
  background: #f9fafb;
  border: 2px dashed #e5e7eb;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  color: #9ca3af;
  font-size: 14px;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Cashflow Tabs */
.cashflow-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.tab-btn {
  background: none;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tab-btn.active {
  background: #10b981;
  color: white;
}

.tab-btn:hover:not(.active) {
  background: #f3f4f6;
}

/* Bottom Section */
.bottom-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 16px;
}

.shipments-section,
.map-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #f1f5f9;
}

.shipments-card,
.map-card {
  padding: 20px;
}

/* Removed duplicate .shipments-header class */

.shipments-tabs {
  display: flex;
  gap: 8px;
}

/* Removed duplicate .shipments-controls class */

.shipment-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Duplicate .shipment-item class removed to avoid conflicts */

.shipment-info {
  flex: 1;
}

.shipment-id {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.route-badge {
  background: #10b981;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
}

.route-badge.custom {
  background: #f59e0b;
}

.shipment-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
  margin: 8px 0;
  font-size: 12px;
  color: #6b7280;
}

.shipment-progress {
  margin: 8px 0;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  margin: 4px 0;
}

.progress-fill {
  height: 100%;
  background: #10b981;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.progress-percent {
  font-size: 12px;
  color: #6b7280;
  font-weight: 600;
}

.shipment-status {
  display: flex;
  gap: 8px;
  font-size: 10px;
  color: #6b7280;
}

.status-item {
  padding: 2px 6px;
  background: #f3f4f6;
  border-radius: 4px;
}

.shipment-vehicle {
  color: #6b7280;
  margin-left: 16px;
}

/* Map Section */
.map-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.map-placeholder {
  background: #f9fafb;
  border: 2px dashed #e5e7eb;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  color: #9ca3af;
  font-size: 14px;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .new-topbar-responsive {
    padding: 12px 16px;
  }

  .topbar-center {
    margin: 0 12px;
  }

  /* Date display removed - no longer needed */

  .add-shipment-btn span,
  .filter-btn span,
  .export-btn span {
    display: none;
  }

  .filter-btn,
  .export-btn {
    padding: 10px;
  }

  .new-page-content-responsive {
    padding: 16px;
  }

  /* Dashboard Responsive */
  /* stats-cards responsive behavior now handled by Bootstrap grid (col-xl-3 col-md-6) */

  .analytics-section {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .bottom-section {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .analytics-stats {
    flex-direction: column;
    gap: 12px;
  }

  .shipments-tabs {
    flex-wrap: wrap;
  }

  /* Removed duplicate .shipment-item responsive override */

  .shipment-vehicle {
    margin-left: 0;
    align-self: center;
  }
}

/* ===== PAGE HEADER COMPONENT ===== */

.page-header-sticky {
  position: sticky;
  top: 0;
  z-index: 100;
  background: transparent;
  padding: 24px 0;
  margin-bottom: 20px;
  border-bottom: none;
  flex-shrink: 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.page-header-content {
  max-width: 100%;
}

.page-header-title {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 8px;
  line-height: 1.2;
}

.page-header-description {
  color: #94a3b8;
  font-size: 16px;
  margin: 0;
  line-height: 1.5;
}

/* Responsive */
@media (max-width: 768px) {
  .page-header-sticky {
    padding: 20px 0;
  }

  .page-header-title {
    font-size: 28px;
  }

  .page-header-description {
    font-size: 14px;
  }

  .new-page-content {
    height: calc(100vh - 50px);
    /* Adjust for smaller topbar on mobile */
  }
}

@media (max-width: 480px) {
  .page-header-sticky {
    padding: 16px 0;
  }

  .page-header-title {
    font-size: 24px;
  }
}

/* Scrollable Content Containers */
.dashboard-scrollable-content,
.shipments-scrollable-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 0 24px 0;
  height: 100%;
}

/* Ensure main containers have proper height */
.dashboard-enhanced,
.dashboard-simple {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* ===== SHIPMENTS MANAGEMENT STYLES ===== */

.shipments-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  gap: 20px;
}

.shipments-title h1 {
  background: linear-gradient(135deg, #60a5fa, #34d399, #fbbf24);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 8px;
}

.shipments-title p {
  color: #94a3b8;
  font-size: 16px;
  margin: 0;
}

.shipments-controls {
  display: flex;
  gap: 12px;
}

.control-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #e2e8f0;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.control-btn:hover {
  background: transparent;
  border-color: rgba(255, 255, 255, 0.3);
  color: #f1f5f9;
}

.search-btn {
  color: #60a5fa;
  border-color: rgba(96, 165, 250, 0.3);
}

.add-btn {
  color: #34d399;
  border-color: rgba(52, 211, 153, 0.3);
}

.filter-btn {
  color: #fbbf24;
  border-color: rgba(251, 191, 36, 0.3);
}

.shipments-container {
  width: 100%;
}

/* Shipments Cards Grid */
.shipments-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
  margin-top: 20px;
}

.shipment-card {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.shipment-card:hover {
  background: transparent;
  border-color: rgba(255, 255, 255, 0.2);
  transform: none;
}

.shipment-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.shipment-card-id {
  font-size: 18px;
  font-weight: 700;
  background: linear-gradient(135deg, #60a5fa, #34d399);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.shipment-card-status {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.shipment-card-body {
  margin-bottom: 20px;
}

.shipment-card-route {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
}

.route-icon {
  color: #60a5fa;
  font-size: 16px;
  flex-shrink: 0;
}

.route-text {
  color: #e2e8f0;
  font-size: 14px;
  font-weight: 500;
}

.shipment-card-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-label {
  color: #94a3b8;
  font-size: 13px;
  font-weight: 500;
  min-width: 50px;
}

.detail-value {
  color: #cbd5e1;
  font-size: 13px;
  font-weight: 500;
}

.truck-icon {
  color: #34d399;
  font-size: 14px;
}

.shipment-card-progress {
  margin-bottom: 16px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-label {
  color: #94a3b8;
  font-size: 12px;
  font-weight: 500;
}

.progress-percentage {
  color: #60a5fa;
  font-size: 12px;
  font-weight: 600;
}

.progress-bar-card {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  overflow: hidden;
}

.progress-fill-card {
  height: 100%;
  background: linear-gradient(135deg, #60a5fa, #34d399);
  border-radius: 10px;
  transition: width 0.3s ease;
}

.shipment-card-footer {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 16px;
}

.eta-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.eta-label {
  color: #94a3b8;
  font-size: 12px;
  font-weight: 500;
}

.eta-value {
  color: #fbbf24;
  font-size: 13px;
  font-weight: 600;
}

.shipment-details {
  display: flex;
  gap: 16px;
  margin-top: 4px;
}

.shipment-driver {
  color: #94a3b8;
  font-size: 12px;
}

.shipment-vehicle {
  color: #94a3b8;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Responsive */
@media (max-width: 768px) {
  .shipments-header {
    flex-direction: column;
    align-items: stretch;
  }

  .shipments-title h1 {
    font-size: 28px;
  }

  .shipments-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .shipment-card {
    padding: 20px;
  }

  .shipment-card-route {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .shipment-card-details {
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .shipments-grid {
    gap: 12px;
  }

  .shipment-card {
    padding: 16px;
  }

  .shipment-card-header {
    margin-bottom: 16px;
  }

  .shipment-card-id {
    font-size: 16px;
  }
}

/* ===== STEPPER DESIGN STYLES ===== */

.stepper-container {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  max-width: 600px;
  margin: 0;
  padding: 20px 0;
}

.stepper-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: auto;
  position: relative;
  gap: 15px;
}

.stepper-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  position: relative;
  z-index: 2;
  opacity: 0;
  animation: slideUpFade 0.8s ease forwards;
}

.stepper-step:nth-child(1) {
  animation-delay: 0.2s;
}

.stepper-step:nth-child(3) {
  animation-delay: 0.4s;
}

.stepper-step:nth-child(5) {
  animation-delay: 0.6s;
}

.stepper-connector {
  width: 40px;
  height: 2px;
  background: linear-gradient(
    90deg,
    rgba(255, 107, 107, 0.3),
    rgba(255, 142, 83, 0.3)
  );
  margin: 0;
  position: relative;
  border-radius: 2px;
  opacity: 0;
  animation: fadeInLine 1s ease forwards;
  animation-delay: 0.8s;
}

.stepper-connector::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 0;
  background: linear-gradient(90deg, #ff6b6b, #ff8e53);
  border-radius: 2px;
  animation: fillLine 1.5s ease forwards;
  animation-delay: 1s;
}

.step-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff6b6b, #ff8e53);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 700;
  margin-bottom: 8px;
  box-shadow: 0 3px 8px rgba(255, 107, 107, 0.3);
  position: relative;
  z-index: 3;
}

.step-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 10px;
  font-size: 18px;
  color: white;
  position: relative;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.step-icon::before {
  content: "";
  position: absolute;
  inset: -2px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff6b6b, #ff8e53);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.step-icon:hover::before {
  opacity: 1;
}

.step-icon:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow: 0 12px 30px rgba(255, 107, 107, 0.3);
  border-color: rgba(255, 255, 255, 0.4);
}

.step-content {
  max-width: 100px;
}

.step-title {
  font-size: 14px;
  font-weight: 700;
  color: #1f2937 !important;
  margin-bottom: 4px;
  letter-spacing: -0.2px;
  text-shadow: none;
}

.step-description {
  font-size: 11px;
  color: #4b5563 !important;
  line-height: 1.3;
  margin: 0;
  text-shadow: none;
}

/* ===== STEPPER ANIMATIONS ===== */
@keyframes slideUpFade {
  from {
    opacity: 0;
    transform: translateY(40px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLine {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes fillLine {
  from {
    width: 0;
  }

  to {
    width: 100%;
  }
}

/* ===== STEPPER RESPONSIVE DESIGN ===== */
@media (max-width: 992px) {
  .stepper-wrapper {
    flex-direction: column;
    gap: 30px;
  }

  .stepper-connector {
    width: 3px;
    height: 40px;
    margin: 15px 0;
    background: linear-gradient(
      180deg,
      rgba(255, 107, 107, 0.3),
      rgba(255, 142, 83, 0.3)
    );
  }

  .stepper-connector::after {
    width: 100%;
    height: 0;
    animation: fillLineVertical 1.5s ease forwards;
    animation-delay: 1s;
  }

  .step-content {
    max-width: 200px;
  }
}

@media (max-width: 768px) {
  .bottom-section {
    padding: 20px 0 0;
  }

  .stepper-container {
    padding: 15px 0;
    justify-content: center;
  }

  .stepper-wrapper {
    gap: 15px;
  }

  .stepper-connector {
    width: 25px;
    height: 2px;
  }

  .step-number {
    width: 20px;
    height: 20px;
    font-size: 10px;
    margin-bottom: 6px;
  }

  .step-icon {
    width: 40px;
    height: 40px;
    font-size: 16px;
    margin-bottom: 8px;
  }

  .step-title {
    font-size: 12px;
    margin-bottom: 3px;
  }

  .step-description {
    font-size: 9px;
  }

  .step-content {
    max-width: 70px;
  }
}

@keyframes fillLineVertical {
  from {
    height: 0;
  }

  to {
    height: 100%;
  }
}

/* ===== ENHANCED AUTH LAYOUT UI IMPROVEMENTS ===== */

/* 1. Text Readability Enhancement - Light dark overlay (already defined above) */
/* The overlay is now defined in the main .tritracz-container::before rule with rgba(0, 0, 0, 0.3) */

/* Enhanced text shadows for better readability */
.hero-title,
.hero-subtitle,
.logo {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

/* 2. Logo Enhancement - Compact and professional design */
.logo {
  font-size: 28px !important;
  font-weight: 800;
  color: white !important;
  margin-bottom: 60px !important;
  padding-top: 30px;
  padding-left: 20px;
  letter-spacing: 2px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.6);
  position: relative;
  display: flex;
  align-items: center;
  gap: 10px;
}

.logo::before {
  content: "△";
  font-size: 32px;
  color: #ff6b6b;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  display: inline-block;
  transform: rotate(0deg);
  transition: transform 0.3s ease;
}

.logo:hover::before {
  transform: rotate(180deg);
}

/* 3. Harmonized Color Scheme - Using White/Gray for Maximum Readability */
.submit-button {
  background: linear-gradient(135deg, #ffffff, #f8f9fa) !important;
  border: 2px solid #e9ecef !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  color: var(--primary-900) !important;
}

.submit-button:hover {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
  border-color: #dee2e6 !important;
  transform: translateY(-3px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

/* Icons now use unified glassmorphism design with hover effects */

/* 4. Enhanced Checkbox Styling */
.checkbox-container {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  margin-bottom: 35px;
  padding: 10px 0;
}

.checkbox-input {
  width: 22px !important;
  height: 22px !important;
  accent-color: #ff6b6b;
  cursor: pointer;
  border-radius: 4px;
  border: 2px solid rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  margin-top: 2px;
}

.checkbox-input:hover {
  border-color: #ff6b6b;
  background: rgba(255, 107, 107, 0.1);
  transform: scale(1.05);
}

.checkbox-input:focus {
  outline: 2px solid rgba(255, 107, 107, 0.5);
  outline-offset: 2px;
}

.checkbox-input:checked {
  background: #ff6b6b;
  border-color: #ff6b6b;
}

.checkbox-label {
  color: rgba(255, 255, 255, 0.9) !important;
  font-size: 15px !important;
  line-height: 1.5;
  cursor: pointer;
  transition: color 0.3s ease;
}

.checkbox-label:hover {
  color: white !important;
}

.text-link {
  color: #ff8e53 !important;
  font-weight: 600;
  text-decoration: underline;
  transition: all 0.3s ease;
}

.text-link:hover {
  color: #ff6b6b !important;
  text-shadow: 0 0 8px rgba(255, 107, 107, 0.5);
}

/* 5. Mobile Responsiveness - Enhanced Stacked Layout - CONSOLIDATED WITH EARLIER MEDIA QUERY */

/* 6. Additional Visual Enhancements */

/* Enhanced form input styling */
.form-input {
  border-radius: 12px !important;
  backdrop-filter: blur(15px) !important;
  -webkit-backdrop-filter: blur(15px) !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.form-input:focus {
  border-color: #adb5bd !important;
  box-shadow: 0 0 0 3px rgba(173, 181, 189, 0.3) !important;
  transform: translateY(-2px);
}

/* Enhanced form section background */
.form-section {
  background: var(--primary-900);
  backdrop-filter: blur(25px) !important;
  -webkit-backdrop-filter: blur(25px) !important;
}

/* ===== NAVY BLUE THEME GLOBAL OVERRIDES ===== */

/* Bootstrap overrides for theme */
.card {
  background: var(--bg-card) !important;
  border: 1px solid var(--border-primary) !important;
  color: var(--text-primary) !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.card:hover {
  background: var(--bg-card-hover) !important;
  border-color: var(--border-primary) !important;
}

.card-body {
  color: var(--text-primary) !important;
}

.card-title {
  color: var(--text-primary) !important;
  font-weight: 600;
}

.card-text {
  color: var(--text-secondary) !important;
}

.card-header {
  background: var(--bg-card) !important;
  border-bottom: 1px solid var(--border-primary) !important;
  color: var(--text-primary) !important;
}

/* Table Styles */
.table {
  color: var(--text-primary) !important;
  background: transparent !important;
}

.table th {
  color: var(--text-primary) !important;
  border-color: var(--border-primary) !important;
  background: var(--bg-secondary) !important;
  font-weight: 600;
}

.table td {
  color: var(--text-secondary) !important;
  border-color: var(--border-secondary) !important;
}

.table-striped > tbody > tr:nth-of-type(odd) > td,
.table-striped > tbody > tr:nth-of-type(odd) > th {
  background-color: var(--bg-tertiary) !important;
}

.table-hover > tbody > tr:hover > td,
.table-hover > tbody > tr:hover > th {
  background-color: var(--bg-card) !important;
}

/* Alert Styles */
.alert-info {
  background: linear-gradient(
    135deg,
    var(--primary-800),
    var(--primary-700)
  ) !important;
  border: 1px solid var(--primary-600) !important;
  color: var(--text-primary) !important;
}

.alert-success {
  background: linear-gradient(
    135deg,
    var(--analogous-teal-800),
    var(--analogous-teal-700)
  ) !important;
  border: 1px solid var(--analogous-teal-600) !important;
  color: var(--text-primary) !important;
}

.alert-warning {
  background: linear-gradient(
    135deg,
    var(--complementary-800),
    var(--complementary-700)
  ) !important;
  border: 1px solid var(--complementary-600) !important;
  color: var(--text-primary) !important;
}

.alert-danger {
  background: linear-gradient(
    135deg,
    var(--triadic-red-800),
    var(--triadic-red-700)
  ) !important;
  border: 1px solid var(--triadic-red-600) !important;
  color: var(--text-primary) !important;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--complementary-500);
  font-weight: 600;
  font-family: "IBM Plex Sans", sans-serif !important;
  font-optical-sizing: auto;
}

.text-muted {
  color: var(--text-muted) !important;
}

.text-primary {
  color: var(--primary-400) !important;
}

.text-secondary {
  color: var(--text-secondary) !important;
}

.text-success {
  color: var(--analogous-teal-400) !important;
}

.text-warning {
  color: var(--complementary-400) !important;
}

.text-danger {
  color: var(--triadic-red-400) !important;
}

.text-info {
  color: var(--primary-300) !important;
}

/* Small text elements */
small,
.small {
  color: var(--text-tertiary) !important;
}

/* Form elements */
.form-label {
  color: var(--text-secondary) !important;
  font-weight: 500;
}

.form-control {
  background: var(--bg-card) !important;
  border: 1px solid var(--border-primary) !important;
  color: var(--text-primary) !important;
}

.form-control:focus {
  border-color: var(--primary-400) !important;
  color: var(--text-primary) !important;
  box-shadow: 0 0 0 0.2rem rgba(74, 119, 176, 0.25) !important;
}

.form-control::placeholder {
  color: var(--text-muted) !important;
}

/* Buttons */
.btn-primary {
  background: var(--complementary-300) !important;
  border-color: var(--complementary-300) !important;
  color: var(--primary-900) !important;
}

/* .btn-primary:hover {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
  border-color: #dee2e6 !important;
} */

.btn-secondary {
  background: linear-gradient(
    135deg,
    var(--primary-800),
    var(--primary-700)
  ) !important;
  border-color: var(--primary-700) !important;
  color: var(--text-primary) !important;
}

.btn-outline-primary {
  border-color: var(--primary-400) !important;
  color: var(--primary-400) !important;
}

.btn-outline-primary:hover {
  background: var(--primary-400) !important;
  border-color: var(--primary-400) !important;
  color: var(--text-primary) !important;
}

/* Better visual hierarchy for form title */
.form-title {
  color: white !important;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  position: relative;
}

/* ===== NAVY BLUE THEME UTILITY CLASSES ===== */

/* Text Color Utilities */
.text-navy-primary {
  color: var(--text-primary) !important;
}

.text-navy-secondary {
  color: var(--text-secondary) !important;
}

.text-navy-tertiary {
  color: var(--text-tertiary) !important;
}

.text-navy-muted {
  color: var(--text-muted) !important;
}

.text-navy-disabled {
  color: var(--text-disabled) !important;
}

/* Background Color Utilities */
.bg-navy-primary {
  background: var(--bg-primary) !important;
}

.bg-navy-secondary {
  background: var(--bg-secondary) !important;
}

.bg-navy-tertiary {
  background: var(--bg-tertiary) !important;
}

.bg-navy-card {
  background: var(--bg-card) !important;
}

.bg-navy-card-hover {
  background: var(--bg-card-hover) !important;
}

/* Border Color Utilities */
.border-navy-primary {
  border-color: var(--border-primary) !important;
}

.border-navy-secondary {
  border-color: var(--border-secondary) !important;
}

.border-navy-tertiary {
  border-color: var(--border-tertiary) !important;
}

/* Navy Blue Shade Utilities */
.text-primary-900 {
  color: var(--primary-900) !important;
}

.text-primary-800 {
  color: var(--primary-800) !important;
}

.text-primary-700 {
  color: var(--primary-700) !important;
}

.text-primary-600 {
  color: var(--primary-600) !important;
}

.text-primary-500 {
  color: var(--primary-500) !important;
}

.text-primary-400 {
  color: var(--primary-400) !important;
}

.text-primary-300 {
  color: var(--primary-300) !important;
}

.text-primary-200 {
  color: var(--primary-200) !important;
}

.text-primary-100 {
  color: var(--primary-100) !important;
}

.text-primary-50 {
  color: var(--primary-50) !important;
}

.bg-primary-900 {
  background: var(--primary-900) !important;
}

.bg-primary-800 {
  background: var(--primary-800) !important;
}

.bg-primary-700 {
  background: var(--primary-700) !important;
}

.bg-primary-600 {
  background: var(--primary-600) !important;
}

.bg-primary-500 {
  background: var(--primary-500) !important;
}

.bg-primary-400 {
  background: var(--primary-400) !important;
}

.bg-primary-300 {
  background: var(--primary-300) !important;
}

.bg-primary-200 {
  background: var(--primary-200) !important;
}

.bg-primary-100 {
  background: var(--primary-100) !important;
}

.bg-primary-50 {
  background: var(--primary-50) !important;
}

/* Complementary Color Utilities */
.text-complementary-900 {
  color: var(--complementary-900) !important;
}

.text-complementary-800 {
  color: var(--complementary-800) !important;
}

.text-complementary-700 {
  color: var(--complementary-700) !important;
}

.text-complementary-600 {
  color: var(--complementary-600) !important;
}

.text-complementary-500 {
  color: var(--complementary-500) !important;
}

.bg-complementary-900 {
  background: var(--complementary-900) !important;
}

.bg-complementary-800 {
  background: var(--complementary-800) !important;
}

.bg-complementary-700 {
  background: var(--complementary-700) !important;
}

.bg-complementary-600 {
  background: var(--complementary-600) !important;
}

.bg-complementary-500 {
  background: var(--complementary-500) !important;
}

/* Analogous Color Utilities */
.text-analogous-teal-900 {
  color: var(--analogous-teal-900) !important;
}

.text-analogous-teal-800 {
  color: var(--analogous-teal-800) !important;
}

.text-analogous-teal-700 {
  color: var(--analogous-teal-700) !important;
}

.text-analogous-teal-600 {
  color: var(--analogous-teal-600) !important;
}

.text-analogous-teal-500 {
  color: var(--analogous-teal-500) !important;
}

.bg-analogous-teal-900 {
  background: var(--analogous-teal-900) !important;
}

.bg-analogous-teal-800 {
  background: var(--analogous-teal-800) !important;
}

.bg-analogous-teal-700 {
  background: var(--analogous-teal-700) !important;
}

.bg-analogous-teal-600 {
  background: var(--analogous-teal-600) !important;
}

.bg-analogous-teal-500 {
  background: var(--analogous-teal-500) !important;
}

.text-analogous-purple-900 {
  color: var(--analogous-purple-900) !important;
}

.text-analogous-purple-800 {
  color: var(--analogous-purple-800) !important;
}

.text-analogous-purple-700 {
  color: var(--analogous-purple-700) !important;
}

.text-analogous-purple-600 {
  color: var(--analogous-purple-600) !important;
}

.text-analogous-purple-500 {
  color: var(--analogous-purple-500) !important;
}

.bg-analogous-purple-900 {
  background: var(--analogous-purple-900) !important;
}

.bg-analogous-purple-800 {
  background: var(--analogous-purple-800) !important;
}

.bg-analogous-purple-700 {
  background: var(--analogous-purple-700) !important;
}

.bg-analogous-purple-600 {
  background: var(--analogous-purple-600) !important;
}

.bg-analogous-purple-500 {
  background: var(--analogous-purple-500) !important;
}

/* Triadic Color Utilities */
.text-triadic-purple-900 {
  color: var(--triadic-purple-900) !important;
}

.text-triadic-purple-800 {
  color: var(--triadic-purple-800) !important;
}

.text-triadic-purple-700 {
  color: var(--triadic-purple-700) !important;
}

.text-triadic-purple-600 {
  color: var(--triadic-purple-600) !important;
}

.text-triadic-purple-500 {
  color: var(--triadic-purple-500) !important;
}

.bg-triadic-purple-900 {
  background: var(--triadic-purple-900) !important;
}

.bg-triadic-purple-800 {
  background: var(--triadic-purple-800) !important;
}

.bg-triadic-purple-700 {
  background: var(--triadic-purple-700) !important;
}

.bg-triadic-purple-600 {
  background: var(--triadic-purple-600) !important;
}

.bg-triadic-purple-500 {
  background: var(--triadic-purple-500) !important;
}

.text-triadic-red-900 {
  color: var(--triadic-red-900) !important;
}

.text-triadic-red-800 {
  color: var(--triadic-red-800) !important;
}

.text-triadic-red-700 {
  color: var(--triadic-red-700) !important;
}

.text-triadic-red-600 {
  color: var(--triadic-red-600) !important;
}

.text-triadic-red-500 {
  color: var(--triadic-red-500) !important;
}

.bg-triadic-red-900 {
  background: var(--triadic-red-900) !important;
}

.bg-triadic-red-800 {
  background: var(--triadic-red-800) !important;
}

.bg-triadic-red-700 {
  background: var(--triadic-red-700) !important;
}

.bg-triadic-red-600 {
  background: var(--triadic-red-600) !important;
}

.bg-triadic-red-500 {
  background: var(--triadic-red-500) !important;
}

/* Gradient Utilities */
.bg-gradient-primary {
  background: linear-gradient(
    135deg,
    var(--primary-600),
    var(--primary-500)
  ) !important;
}

.bg-gradient-secondary {
  background: linear-gradient(
    135deg,
    var(--primary-800),
    var(--primary-700)
  ) !important;
}

.bg-gradient-teal {
  background: linear-gradient(
    135deg,
    var(--analogous-teal-600),
    var(--analogous-teal-500)
  ) !important;
}

.bg-gradient-purple {
  background: linear-gradient(
    135deg,
    var(--analogous-purple-600),
    var(--analogous-purple-500)
  ) !important;
}

.bg-gradient-complementary {
  background: linear-gradient(
    135deg,
    var(--complementary-600),
    var(--complementary-500)
  ) !important;
}

/* Text Gradient Utilities */
.text-gradient-primary {
  background: linear-gradient(
    135deg,
    var(--primary-300),
    var(--primary-200)
  ) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
}

.text-gradient-rainbow {
  background: linear-gradient(
    135deg,
    var(--primary-300),
    var(--analogous-teal-400),
    var(--complementary-400)
  ) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
}

/* Card and Component Utilities */
.navy-card {
  background: var(--bg-card) !important;
  border: 1px solid var(--border-primary) !important;
  color: var(--text-primary) !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.navy-card:hover {
  background: var(--bg-card-hover) !important;
  border-color: var(--border-primary) !important;
}

.navy-button-primary {
  background: linear-gradient(
    135deg,
    var(--primary-600),
    var(--primary-500)
  ) !important;
  border-color: var(--primary-600) !important;
  color: var(--text-primary) !important;
}

.navy-button-primary:hover {
  background: linear-gradient(
    135deg,
    var(--primary-500),
    var(--primary-400)
  ) !important;
  border-color: var(--primary-500) !important;
}

.form-title::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(135deg, #ff6b6b, #ff8e53);
  border-radius: 2px;
}

/* Enhanced step icons with better hover effects */
.step-icon:hover {
  transform: translateY(-12px) scale(1.1) !important;
  box-shadow: 0 20px 50px rgba(255, 107, 107, 0.4) !important;
}

/* Improved bottom section positioning */
.bottom-section {
  position: absolute;
  bottom: 190px;
  left: 50px;
  right: 0;
  z-index: 3;
}

/* Enhanced accessibility and focus states - Using White/Gray for Maximum Readability */
.form-input:focus,
.checkbox-input:focus,
.submit-button:focus {
  outline: 3px solid rgba(173, 181, 189, 0.6);
  outline-offset: 2px;
}

/* Smooth transitions for all interactive elements */
.step-icon,
.submit-button,
.checkbox-input,
.form-input,
.text-link {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== CUSTOM PHONE INPUT STYLES ===== */
/* Modern phone input design with Indian flag and +91 prefix */

.phone-input-container {
  position: relative;
  width: 100%;
}

.phone-input-wrapper {
  display: flex;
  align-items: center;
  background: transparent;
  border: 1px solid var(--border-secondary);
  border-radius: 8px;
  padding: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  min-height: 50px;
}

.phone-input-wrapper:hover {
  border-color: var(--primary-400);
}

.phone-input-wrapper:focus-within {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.1);
}

.phone-input-wrapper.error {
  border-color: #dc3545;
}

.country-code-display {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: rgba(var(--text-secondary), 0.05);
  border-right: 1px solid var(--border-secondary);
  min-width: 90px;
  gap: 10px;
}

.country-flag {
  width: 24px;
  height: 18px;
  border-radius: 3px;
  object-fit: cover;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.country-code {
  font-weight: 600;
  font-size: 16px;
  color: var(--text-primary);
  letter-spacing: 0.5px;
}

.phone-number-input {
  flex: 1;
  padding: 12px 16px;
  display: flex;
  align-items: center;
}

.phone-input {
  border: none !important;
  outline: none !important;
  background: transparent !important;
  color: var(--text-primary) !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  padding: 0 !important;
  width: 100% !important;
  box-shadow: none !important;
  letter-spacing: 0.5px;
}

.phone-input::placeholder {
  color: var(--text-secondary) !important;
  font-weight: 400 !important;
  opacity: 0.7;
}

.phone-input:focus {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* Error state styling */
.phone-input-wrapper.error .country-code-display {
  background: rgba(220, 53, 69, 0.05);
  border-right-color: #dc3545;
}

.phone-input-wrapper.error .country-code {
  color: #dc3545;
}

/* Dark mode adjustments */
[data-theme="dark"] .phone-input-wrapper {
  border-color: var(--border-secondary);
  background: transparent;
}

[data-theme="dark"] .country-code-display {
  background: rgba(var(--primary-rgb), 0.08);
  border-right-color: var(--border-secondary);
}

[data-theme="dark"] .phone-input {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .phone-input::placeholder {
  color: var(--text-secondary) !important;
}

/* Light mode adjustments */
[data-theme="light"] .phone-input-wrapper {
  border-color: var(--border-secondary);
  background: transparent;
}

[data-theme="light"] .country-code-display {
  background: rgba(var(--primary-rgb), 0.03);
  border-right-color: var(--border-secondary);
}

[data-theme="light"] .phone-input {
  color: var(--text-primary) !important;
}

[data-theme="light"] .phone-input::placeholder {
  color: var(--text-secondary) !important;
}

/* Responsive design for mobile */
@media (max-width: 576px) {
  .phone-input-wrapper {
    min-height: 52px;
  }

  .country-code-display {
    padding: 14px 16px;
    min-width: 90px;
  }

  .country-flag {
    width: 20px;
    height: 15px;
  }

  .country-code {
    font-size: 15px;
  }

  .phone-number-input {
    padding: 14px 16px;
  }

  .phone-input {
    font-size: 15px !important;
  }
}

/* Animation for better UX */
@keyframes phoneInputFocus {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

.phone-input-wrapper:focus-within {
  animation: phoneInputFocus 0.3s ease-out;
}

/* Phone input error message styling */
.phone-error-message {
  color: #dc3545 !important;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 8px;
}

.phone-error-message::before {
  content: "⚠";
  color: #dc3545;
  font-size: 16px;
}

/* ===== SIMPLE SELECTED ROLE DESIGN ===== */
/* Clean, minimal design without borders */

.selected-role-simple {
  padding: 18px 20px;
  margin-bottom: 1.5rem;
  position: relative;
  background: rgba(var(--primary-rgb), 0.04);
  border-left: 4px solid var(--primary-500);
  border-radius: 0 8px 8px 0;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.selected-role-simple:hover {
  background: rgba(var(--primary-rgb), 0.06);
  transform: translateX(2px);
}

.role-info-icon {
  width: 32px;
  height: 32px;
  background: var(--primary-500);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(var(--primary-rgb), 0.3);
  transition: all 0.3s ease;
}

.selected-role-simple:hover .role-info-icon {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.4);
}

.role-label-simple {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 1px;
  display: block;
  margin-bottom: 4px;
}

.role-value-simple {
  font-size: 18px;
  font-weight: 700;
  color: var(--text-primary);
  letter-spacing: 0.3px;
  line-height: 1.2;
}

.change-btn-simple {
  background: none;
  border: none;
  color: var(--primary-500);
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  text-decoration: underline;
  text-underline-offset: 3px;
  text-decoration-thickness: 2px;
  transition: all 0.2s ease;
  padding: 0;
}

.change-btn-simple:hover {
  color: var(--primary-600);
  text-decoration-color: var(--primary-600);
  transform: translateX(-2px);
}

.change-btn-simple:active {
  transform: translateX(0);
}

/* Dark mode adjustments */
[data-theme="dark"] .selected-role-simple {
  background: var(--primary-800);
  border-left-color: var(--primary-400);
}

[data-theme="dark"] .selected-role-simple:hover {
  background: rgba(var(--primary-rgb), 0.08);
}

[data-theme="dark"] .role-info-icon {
  background: var(--primary-400);
}

[data-theme="dark"] .role-value-simple {
  color: var(--text-primary);
}

[data-theme="dark"] .change-btn-simple {
  color: var(--primary-400);
}

[data-theme="dark"] .change-btn-simple:hover {
  color: var(--primary-300);
  text-decoration-color: var(--primary-300);
}

/* Light mode adjustments */
[data-theme="light"] .selected-role-simple {
  background: rgba(var(--primary-rgb), 0.03);
  border-left-color: var(--primary-600);
}

[data-theme="light"] .selected-role-simple:hover {
  background: rgba(var(--primary-rgb), 0.05);
}

[data-theme="light"] .role-info-icon {
  background: var(--primary-600);
}

[data-theme="light"] .role-value-simple {
  color: var(--text-primary);
}

[data-theme="light"] .change-btn-simple {
  color: var(--primary-600);
}

[data-theme="light"] .change-btn-simple:hover {
  color: var(--primary-700);
  text-decoration-color: var(--primary-700);
}

/* ===== PROFILE FORM ENHANCEMENTS ===== */
.profile-input {
  background: rgba(var(--surface-primary), 0.3) !important;
  border: 1px solid rgba(var(--border-secondary), 0.2);
  border-radius: 12px !important;
  padding: 16px 20px !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  color: var(--text-primary) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04) !important;
}

.profile-input:focus {
  background: rgba(var(--surface-primary), 0.5) !important;
  border-color: var(--primary-500) !important;
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1),
    0 4px 16px rgba(0, 0, 0, 0.08) !important;
  transform: translateY(-1px);
}

.profile-input:hover:not(:disabled) {
  border-color: rgba(var(--primary-rgb), 0.4) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06) !important;
  transform: translateY(-1px);
}

.profile-input::placeholder {
  color: var(--text-secondary) !important;
  opacity: 0.7;
  font-weight: 400 !important;
}

.profile-input-verified {
  background: rgba(71, 134, 98, 0.05) !important;
  border-color: rgba(71, 134, 98, 0.2) !important;
  color: var(--success-700) !important;
  cursor: not-allowed;
  border-radius: 12px !important;
  padding: 16px 20px !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04) !important;
}

.profile-error-message {
  margin-top: 8px;
}

.profile-submit-btn:active,
.auth-submit-btn:active {
  transform: translateY(0);
  background: var(--triadic-red-200) !important;
}

.auth-secondary-btn:active {
  transform: translateY(0);
  background: var(--triadic-red-200) !important;
}

/* Auth form link buttons */
.auth-link-btn:hover {
  transform: translateX(2px);
  color: var(--triadic-red-700) !important;
  text-decoration: underline !important;
}

/* Dark mode adjustments for profile form */
[data-theme="dark"] .profile-input {
  background: rgba(var(--surface-secondary), 0.4) !important;
}

[data-theme="dark"] .profile-input:focus {
  background: rgba(var(--surface-secondary), 0.6) !important;
}

[data-theme="dark"] .profile-input-verified {
  background: rgba(71, 134, 98, 0.08) !important;
  border-color: rgba(71, 134, 98, 0.25) !important;
}

.kyc-notification-close {
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.kyc-notification-close:hover {
  background: var(--bg-card-hover);
  color: var(--text-primary);
  transform: scale(1.1);
}

.kyc-notification-icon {
  text-align: center;
  margin-bottom: 24px;
}

.kyc-icon-wrapper {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--warning-500), var(--warning-400));
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 8px 24px rgba(var(--warning-rgb), 0.3);
  animation: kycIconPulse 2s ease-in-out infinite;
}

.kyc-notification-content {
  text-align: center;
  margin-bottom: 32px;
}

.kyc-notification-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 12px;
}

.kyc-notification-message {
  color: var(--text-secondary);
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 24px;
}

.kyc-features-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  text-align: left;
  max-width: 300px;
  margin: 0 auto;
}

.kyc-feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.kyc-feature-item svg {
  color: var(--success-500);
  flex-shrink: 0;
}

.kyc-notification-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.kyc-complete-btn {
  background: linear-gradient(
    135deg,
    var(--primary-500),
    var(--primary-400)
  ) !important;
  border: none !important;
  color: white !important;
  padding: 14px 24px !important;
  border-radius: 12px !important;
  font-weight: 600 !important;
  font-size: 1rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 16px rgba(var(--primary-rgb), 0.3) !important;
}

.kyc-complete-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(var(--primary-rgb), 0.4) !important;
  background: linear-gradient(
    135deg,
    var(--primary-400),
    var(--primary-300)
  ) !important;
}

.kyc-later-btn {
  background: transparent !important;
  border: 2px solid var(--border-secondary) !important;
  color: var(--text-secondary) !important;
  padding: 12px 24px !important;
  border-radius: 12px !important;
  font-weight: 500 !important;
  font-size: 0.875rem !important;
  transition: all 0.3s ease !important;
}

.kyc-later-btn:hover {
  background: var(--bg-card-hover) !important;
  border-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
  transform: translateY(-1px) !important;
}

/* KYC Badge in Topbar */
.kyc-badge {
  background: linear-gradient(135deg, var(--warning-500), var(--warning-400));
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  margin-left: 8px;
  animation: kycBadgePulse 2s ease-in-out infinite;
  box-shadow: 0 2px 8px rgba(var(--warning-rgb), 0.3);
}

/* Clickable KYC Badge */
.kyc-badge-clickable {
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  text-decoration: none;
}

.kyc-badge-clickable:hover {
  background: linear-gradient(
    135deg,
    var(--warning-400),
    var(--warning-300)
  ) !important;
  transform: translateY(-1px) scale(1.05);
  box-shadow: 0 4px 12px rgba(var(--warning-rgb), 0.4) !important;
  color: white !important;
}

.kyc-badge-clickable:active {
  transform: translateY(0) scale(1.02);
  box-shadow: 0 2px 8px rgba(var(--warning-rgb), 0.3) !important;
}

/* Animations */
@keyframes kycOverlayFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes kycSlideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes kycIconPulse {
  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 8px 24px rgba(var(--warning-rgb), 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 12px 32px rgba(var(--warning-rgb), 0.4);
  }
}

@keyframes kycBadgePulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* Responsive Design */
@media (max-width: 576px) {
  .kyc-notification-card {
    padding: 24px;
    margin: 16px;
  }

  .kyc-notification-title {
    font-size: 1.25rem;
  }

  .kyc-notification-message {
    font-size: 0.875rem;
  }

  .kyc-icon-wrapper {
    width: 64px;
    height: 64px;
  }

  .kyc-icon-wrapper svg {
    width: 24px;
    height: 24px;
  }
}

/* ===== GLOBAL OFFCANVAS STYLES ===== */
/* Blur only main content when offcanvas is active, keep sidebar and topbar visible */
body.offcanvas-active {
  overflow: hidden;
}

body.offcanvas-active .new-main-content {
  filter: blur(8px);
  -webkit-filter: blur(8px);
  transition: filter 0.3s ease-out;
}

/* Redesigned Global Offcanvas Styling */
.global-offcanvas-redesigned {
  width: 480px !important;
  max-width: 600px !important;
  background: var(--bg-card);
  border-left: 1px solid var(--border-light);
  box-shadow: -4px 0 24px rgba(0, 0, 0, 0.1);
}

.global-offcanvas-redesigned .offcanvas-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  position: relative;
}

/* Sticky Header */
.offcanvas-header-sticky {
  position: sticky;
  top: 0;
  z-index: 10;
  border-bottom: 1px solid var(--border-light);
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.offcanvas-title-wrapper {
  flex: 1;
}

.offcanvas-title {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1.25rem;
  margin: 0;
  line-height: 1.4;
}

.offcanvas-close-btn {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
}

.offcanvas-close-btn:hover {
  background: var(--bg-surface);
  color: var(--text-primary);
  transform: scale(1.05);
}

/* Scrollable Body */
.offcanvas-body-scrollable {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 1.5rem 1.5rem 100px 1.5rem;
  scrollbar-width: thin;
  scrollbar-color: var(--border-light) transparent;
  max-height: calc(100vh - 140px);
}

.offcanvas-body-scrollable::-webkit-scrollbar {
  width: 6px;
}

.offcanvas-body-scrollable::-webkit-scrollbar-track {
  background: transparent;
}

.offcanvas-body-scrollable::-webkit-scrollbar-thumb {
  background: var(--border-light);
  border-radius: 3px;
}

.offcanvas-body-scrollable::-webkit-scrollbar-thumb:hover {
  background: var(--border-primary);
}

/* Sticky Footer */
.offcanvas-footer-sticky {
  position: sticky;
  bottom: 0;
  z-index: 10;
  background: var(--bg-card);
  border-top: 1px solid var(--border-light);
  padding: 16px;
  margin-top: auto;
  flex-shrink: 0;
}

/* Redesigned KYC Form in Offcanvas */
.kyc-form-container {
  max-width: 100%;
  padding: 14px;
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 140px);
}

/* Section Styling */
.kyc-form-container .kyc-section {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
}

.kyc-form-container .kyc-section:last-of-type {
  border-bottom: none;
  margin-bottom: 1rem;
}

.kyc-form-container .section-title {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1rem;
  display: flex;
  align-items: center;
  margin-bottom: 1.25rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--border-light);
}

.kyc-form-container .section-title svg {
  color: var(--primary-500) !important;
  margin-right: 0.5rem;
}

/* Form Group Styling */
.kyc-form-container .form-group {
  margin-bottom: 0;
}

.kyc-form-container .row.g-3 {
  margin-bottom: 0;
}

.kyc-form-container .row.g-3 > * {
  margin-bottom: 1rem;
}

/* Form Label Styling */
.kyc-form-container .form-label {
  color: var(--text-primary);
  font-weight: 500;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
  display: block;
  line-height: 1.4;
}

/* Simple Input Styling */
.kyc-input {
  border: 1px solid var(--border-light) !important;
  border-radius: 6px !important;
  padding: 0.75rem !important;
  font-size: 0.875rem !important;
  color: var(--text-primary) !important;
  min-height: 44px !important;
  width: 100% !important;
}

/* Textarea Styling */
.kyc-form-container .kyc-input[rows] {
  min-height: 88px !important;
  resize: vertical;
  line-height: 1.5;
}

/* Select Styling */
.kyc-form-container .kyc-input.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") !important;
  background-repeat: no-repeat !important;
  background-position: right 0.75rem center !important;
  background-size: 16px 12px !important;
  padding-right: 2.5rem !important;
}

/* File Input Styling */
.kyc-form-container .kyc-file-input {
  padding: 0.5rem 0.75rem !important;
  min-height: 44px !important;
  display: flex !important;
  align-items: center !important;
}

.kyc-form-container .kyc-file-input::-webkit-file-upload-button {
  background: var(--primary-500);
  color: white;
  border: none;
  padding: 0.375rem 0.75rem;
  border-radius: 4px;
  margin-right: 0.75rem;
  cursor: pointer;
  font-size: 0.8125rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.kyc-form-container .kyc-file-input::-webkit-file-upload-button:hover {
  background: var(--primary-600);
}

/* Form Text Styling */
.kyc-form-container .form-text {
  font-size: 0.8125rem;
  color: var(--text-muted);
  margin-top: 0.375rem;
  display: block;
  line-height: 1.4;
}

/* Footer Action Buttons */
.kyc-actions-footer {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
  align-items: center;
}

/* Sticky Footer within Form */
.kyc-actions-footer-sticky {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: var(--bg-card);
  border-top: 1px solid var(--border-light);
  padding: 16px;
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
  align-items: center;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.kyc-actions-footer .btn,
.kyc-actions-footer-sticky .btn {
  border-radius: 8px;
  font-weight: 600;
  padding: 12px 24px;
  font-size: 14px;
  min-width: 120px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.kyc-actions-footer .btn-outline-secondary,
.kyc-actions-footer-sticky .btn-outline-secondary {
  border: 1px solid var(--border-primary);
  color: var(--text-secondary);
  background: transparent;
}

.kyc-actions-footer .btn-outline-secondary:hover,
.kyc-actions-footer-sticky .btn-outline-secondary:hover {
  background: var(--bg-surface);
  border-color: var(--border-primary);
  color: var(--text-primary);
}

.kyc-actions-footer .kyc-submit-btn,
.kyc-actions-footer-sticky .kyc-submit-btn {
  background: var(--primary-500) !important;
  border: 1px solid var(--primary-500) !important;
  color: white !important;
}

.kyc-actions-footer .kyc-submit-btn:hover:not(:disabled),
.kyc-actions-footer-sticky .kyc-submit-btn:hover:not(:disabled) {
  background: var(--primary-600) !important;
  border-color: var(--primary-600) !important;
  transform: translateY(-1px) !important;
}

.kyc-actions-footer .kyc-submit-btn:disabled,
.kyc-actions-footer-sticky .kyc-submit-btn:disabled {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
  transform: none !important;
}

/* Validation Styling */
.kyc-form-container .is-invalid.kyc-input {
  border-color: var(--danger-500) !important;
  box-shadow: 0 0 0 3px rgba(var(--danger-rgb), 0.1) !important;
}

.kyc-form-container .invalid-feedback {
  color: var(--danger-500);
  font-size: 0.8125rem;
  font-weight: 500;
  margin-top: 0.375rem;
  display: block;
}

/* Dark Mode Support for Redesigned Offcanvas */
[data-theme="dark"] .global-offcanvas-redesigned {
  background: var(--bg-card) !important;
  border-left-color: var(--border-primary) !important;
  box-shadow: -4px 0 24px rgba(0, 0, 0, 0.4) !important;
}

[data-theme="dark"] .offcanvas-header-sticky {
  background: var(--bg-card) !important;
  border-bottom-color: var(--border-primary) !important;
}

[data-theme="dark"] .offcanvas-body-scrollable {
  background: var(--bg-card) !important;
  scrollbar-color: var(--border-primary) transparent;
}

[data-theme="dark"] .offcanvas-body-scrollable::-webkit-scrollbar-thumb {
  background: var(--border-primary);
}

[data-theme="dark"] .offcanvas-body-scrollable::-webkit-scrollbar-thumb:hover {
  background: var(--border-primary-dark);
}

[data-theme="dark"] .offcanvas-footer-sticky {
  background: var(--bg-card) !important;
  border-top-color: var(--border-primary) !important;
}

[data-theme="dark"] .offcanvas-title {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .offcanvas-close-btn {
  color: var(--text-muted) !important;
}

[data-theme="dark"] .offcanvas-close-btn:hover {
  background: var(--bg-card-hover) !important;
  color: var(--text-primary) !important;
}

/* Dark Mode KYC Form */
[data-theme="dark"] .kyc-form-container {
  background: var(--bg-card) !important;
}

[data-theme="dark"] .kyc-form-container .section-title {
  color: var(--text-primary) !important;
  border-bottom-color: var(--border-primary) !important;
}

[data-theme="dark"] .kyc-form-container .form-label {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .kyc-input {
  background: var(--bg-card) !important;
  border-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .kyc-input:focus {
  background: var(--bg-card) !important;
  border-color: var(--primary-500) !important;
  color: var(--text-primary) !important;
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1) !important;
}

[data-theme="dark"] .kyc-form-container .form-text {
  color: var(--text-muted) !important;
}

[data-theme="dark"] .kyc-actions-footer-sticky {
  background: var(--bg-card) !important;
  border-top-color: var(--border-primary) !important;
}

[data-theme="dark"] .kyc-actions-footer .btn-outline-secondary,
[data-theme="dark"] .kyc-actions-footer-sticky .btn-outline-secondary {
  border-color: var(--border-primary) !important;
  color: var(--text-secondary) !important;
  background: transparent !important;
}

[data-theme="dark"] .kyc-actions-footer .btn-outline-secondary:hover,
[data-theme="dark"] .kyc-actions-footer-sticky .btn-outline-secondary:hover {
  background: var(--bg-card-hover) !important;
  border-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

/* Responsive Design for Redesigned Offcanvas */
@media (max-width: 768px) {
  .global-offcanvas-redesigned {
    width: 100vw !important;
    max-width: 100vw !important;
  }

  .offcanvas-header-sticky {
    padding: 1rem;
  }

  .offcanvas-body-scrollable {
    padding: 1rem;
  }

  .offcanvas-footer-sticky {
    padding: 1rem;
  }

  .kyc-actions-footer,
  .kyc-actions-footer-sticky {
    flex-direction: column;
    gap: 0.5rem;
  }

  .kyc-actions-footer .btn,
  .kyc-actions-footer-sticky .btn {
    width: 100%;
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .global-offcanvas-redesigned .offcanvas-container {
    padding: 1rem 0;
  }

  .offcanvas-header-sticky {
    padding: 0.75rem;
  }

  .offcanvas-body-scrollable {
    padding: 0.75rem;
  }

  .offcanvas-footer-sticky {
    padding: 0.75rem;
  }

  .kyc-form-container .kyc-input {
    font-size: 16px !important; /* Prevents zoom on iOS */
  }
}

/* ===== KYC FORM STYLES ===== */
.kyc-page {
  min-height: 100vh;
  background: var(--bg-primary);
}

/* KYC Page Background - Light Mode: White, Dark Mode: Secondary Color */
[data-theme="light"] .kyc-page {
  background: var(--white) !important;
}

[data-theme="dark"] .kyc-page {
  background: var(--bg-surface) !important;
}

.kyc-content .card {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.kyc-section {
  border-bottom: 1px solid var(--border-secondary);
  padding-bottom: 2.5rem;
  margin-bottom: 2.5rem;
}

.kyc-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
  margin-bottom: 0;
}

.section-title {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--primary-100);
}

.section-title svg {
  color: var(--primary-500) !important;
}

/* Form Group Styling */
.form-group {
  margin-bottom: 0;
}

.form-label {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 0.95rem;
  margin-bottom: 0.5rem;
  display: block;
}

/* Enhanced Input Styling */
.kyc-input {
  border: 2px solid var(--border-secondary) !important;
  border-radius: 12px !important;
  padding: 14px 18px !important;
  font-size: 15px !important;
  font-weight: 500 !important;
  color: var(--text-primary) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  height: auto !important;
  min-height: 50px !important;
}

/* Textarea specific styling */
.kyc-input[rows] {
  min-height: 100px !important;
  resize: vertical;
}

/* Select specific styling */
.kyc-input.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") !important;
  background-repeat: no-repeat !important;
  background-position: right 12px center !important;
  background-size: 16px 12px !important;
  padding-right: 40px !important;
}

/* File Input Styling */
.kyc-file-input {
  position: relative;
  overflow: hidden;
  padding: 12px 16px !important;
  min-height: 50px !important;
}

.kyc-file-input::-webkit-file-upload-button {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-400));
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  margin-right: 12px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.kyc-file-input::-webkit-file-upload-button:hover {
  background: linear-gradient(135deg, var(--primary-400), var(--primary-300));
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.3);
}

/* Form Text Styling */
.form-text {
  font-size: 0.875rem;
  color: var(--text-muted);
  margin-top: 0.5rem;
  display: block;
}

/* Submit Button Enhanced */
.kyc-submit-btn {
  background: linear-gradient(
    135deg,
    var(--primary-500),
    var(--primary-400)
  ) !important;
  border: none !important;
  border-radius: 12px !important;
  padding: 16px 32px !important;
  font-size: 1.1rem !important;
  font-weight: 600 !important;
  color: white !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 4px 16px rgba(var(--primary-rgb), 0.3) !important;
  min-width: 220px !important;
}

.kyc-submit-btn:hover:not(:disabled) {
  transform: translateY(-3px) !important;
  box-shadow: 0 8px 24px rgba(var(--primary-rgb), 0.4) !important;
  background: linear-gradient(
    135deg,
    var(--primary-400),
    var(--primary-300)
  ) !important;
}

.kyc-submit-btn:active {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 16px rgba(var(--primary-rgb), 0.3) !important;
}

.kyc-submit-btn:disabled {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
  transform: none !important;
}

/* Validation Styling */
.is-invalid.kyc-input {
  border-color: var(--danger-500) !important;
  box-shadow: 0 0 0 4px rgba(var(--danger-rgb), 0.1) !important;
}

.invalid-feedback {
  color: var(--danger-500);
  font-size: 0.875rem;
  font-weight: 500;
  margin-top: 0.5rem;
  display: block;
}

/* Dark mode adjustments */
[data-theme="dark"] .kyc-input {
  background: var(--bg-card) !important;
  border-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .kyc-input::placeholder {
  color: var(--text-muted) !important;
  opacity: 0.7;
}

[data-theme="dark"] .section-title {
  color: var(--text-primary) !important;
  border-bottom-color: var(--border-primary) !important;
}

[data-theme="dark"] .form-label {
  color: var(--text-primary) !important;
}

/* Light mode adjustments for KYC form */
[data-theme="light"] .global-offcanvas-redesigned {
  background: var(--bg-card) !important;
  border-left-color: var(--border-primary) !important;
  box-shadow: -4px 0 24px rgba(0, 0, 0, 0.08) !important;
}

[data-theme="light"] .offcanvas-header-sticky {
  background: var(--bg-card) !important;
  border-bottom-color: var(--border-primary) !important;
}

[data-theme="light"] .offcanvas-body-scrollable {
  background: var(--bg-card) !important;
}

[data-theme="light"] .kyc-form-container {
  background: var(--bg-card) !important;
}

[data-theme="light"] .kyc-input {
  background: var(--bg-card) !important;
  border-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

[data-theme="light"] .kyc-input:focus {
  background: var(--bg-card) !important;
  border-color: var(--primary-500) !important;
  color: var(--text-primary) !important;
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1) !important;
}

[data-theme="light"] .kyc-input::placeholder {
  color: var(--text-muted) !important;
  opacity: 0.7;
}

[data-theme="light"] .offcanvas-footer-sticky {
  background: var(--bg-card) !important;
  border-top-color: var(--border-primary) !important;
}

[data-theme="light"] .kyc-actions-footer-sticky {
  background: var(--bg-card) !important;
  border-top-color: var(--border-primary) !important;
}

/* File input styling for both themes */
[data-theme="dark"] .kyc-file-input {
  background: var(--bg-card) !important;
  border-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

[data-theme="light"] .kyc-file-input {
  background: var(--bg-card) !important;
  border-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

/* Select dropdown styling for both themes */
[data-theme="dark"] .kyc-input.form-select {
  background: var(--bg-card) !important;
  border-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

[data-theme="light"] .kyc-input.form-select {
  background: var(--bg-card) !important;
  border-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .kyc-content .card-body {
    padding: 2rem 1.5rem !important;
  }

  .section-title {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
  }

  .kyc-submit-btn {
    width: 100% !important;
    min-width: auto !important;
  }

  .kyc-input {
    font-size: 16px !important; /* Prevents zoom on iOS */
  }
}

@media (max-width: 576px) {
  .kyc-content .card-body {
    padding: 1.5rem 1rem !important;
  }

  .kyc-section {
    padding-bottom: 2rem;
    margin-bottom: 2rem;
  }
}

/* Responsive design */
@media (max-width: 576px) {
  .role-value-simple {
    font-size: 16px;
  }

  .change-btn-simple {
    font-size: 13px;
  }
}

/* ===== WHITE THEME ABOUT US SECTION STYLES ===== */

/* White Theme About Us Section */
.about-section-white {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #ffffff 100%);
  position: relative;
  overflow: hidden;
  min-height: 100vh;
}

.typewriter-text {
  border-right: 2px solid #3b82f6;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0;
  }
}

/* Automotive Hero Subtitle */
.automotive-hero-subtitle {
  font-size: 1.5rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  font-weight: 400;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

/* CTA Section */
.automotive-cta-section {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

/* Primary CTA Button */
.btn-automotive-primary {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 1rem;
  padding: 1.25rem 2.5rem;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 50px;
  font-size: 1.125rem;
  font-weight: 600;
  text-decoration: none;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.btn-automotive-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(59, 130, 246, 0.6);
  color: white;
}

.btn-arrow {
  transition: transform 0.3s ease;
}

.btn-automotive-primary:hover .btn-arrow {
  transform: translateX(5px);
}

.btn-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease;
}

.btn-automotive-primary:hover .btn-glow {
  left: 100%;
}

/* Scroll Indicator */
.automotive-scroll-indicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.7);
  animation: scrollBounce 2s ease-in-out infinite;
}

@keyframes scrollBounce {
  0%,
  100% {
    transform: translateX(-50%) translateY(0);
  }
  50% {
    transform: translateX(-50%) translateY(10px);
  }
}

.scroll-wheel {
  width: 24px;
  height: 40px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  position: relative;
}

.scroll-dot {
  width: 4px;
  height: 8px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 2px;
  position: absolute;
  top: 6px;
  left: 50%;
  transform: translateX(-50%);
  animation: scrollDot 1.5s ease-in-out infinite;
}

@keyframes scrollDot {
  0% {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
  100% {
    transform: translateX(-50%) translateY(20px);
    opacity: 0;
  }
}

.scroll-text {
  font-size: 0.75rem;
  font-weight: 500;
  letter-spacing: 1px;
  text-transform: uppercase;
}

/* About Section Styles */
.about-section-clean {
  padding: 5rem 0;
  position: relative;
}

.about-badge-clean {
  display: inline-flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 50px;
  color: #3b82f6 !important;
  font-size: 0.875rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.about-badge-clean:hover {
  background: rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
}

.about-main-title {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.2;
  margin-bottom: 1.5rem;
  color: #1f2937 !important;
}

.about-subtitle {
  font-size: 1.25rem;
  line-height: 1.6;
  color: #6b7280 !important;
  max-width: 600px;
}

.about-story-container {
  padding: 2rem 0;
}

.story-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937 !important;
  margin-bottom: 1rem;
}

.story-line {
  width: 60px;
  height: 4px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 2px;
  margin-bottom: 2rem;
}

.story-text {
  font-size: 1.125rem;
  line-height: 1.7;
  color: #4b5563 !important;
  margin-bottom: 1.5rem;
}

/* Mission & Vision Cards */
.mission-vision-cards {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.mv-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.mv-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.mv-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.mission-card .mv-icon {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.vision-card .mv-icon {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.mv-content h6 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937 !important;
  margin-bottom: 0.5rem;
}

.mv-content p {
  font-size: 0.875rem;
  color: #6b7280 !important;
  margin: 0;
  line-height: 1.5;
}

/* Features Section */
.features-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1f2937 !important;
  margin-bottom: 2rem;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.feature-item-clean {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.feature-item-clean:hover {
  background: rgba(59, 130, 246, 0.05);
  transform: translateX(10px);
}

.feature-icon-clean {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.feature-text h6 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937 !important;
  margin-bottom: 0.25rem;
}

.feature-text span {
  font-size: 0.875rem;
  color: #6b7280 !important;
  line-height: 1.4;
}

/* Hover effects for social links */
.hover-primary:hover {
  color: #3b82f6 !important;
  transition: color 0.3s ease;
}

/* Redesigned About Us Section Styles */
.about-section-modern {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  position: relative;
}

.about-badge-modern {
  display: inline-flex;
  align-items: center;
  padding: 1rem 2rem;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.1) 0%,
    rgba(59, 130, 246, 0.05) 100%
  );
  border: 2px solid rgba(59, 130, 246, 0.2);
  border-radius: 50px;
  color: #3b82f6 !important;
  font-size: 0.875rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
}

.about-badge-modern:hover {
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.15) 0%,
    rgba(59, 130, 246, 0.1) 100%
  );
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(59, 130, 246, 0.2);
}

.about-title-modern {
  font-size: 4rem;
  font-weight: 900;
  line-height: 1.1;
  color: #1f2937 !important;
  margin-bottom: 2rem;
}

.about-subtitle-modern {
  font-size: 1.375rem;
  line-height: 1.7;
  color: #6b7280 !important;
  max-width: 700px;
  font-weight: 400;
}

.story-card-modern {
  background: white;
  border-radius: 24px;
  padding: 3rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(59, 130, 246, 0.1);
  transition: all 0.3s ease;
  height: 100%;
}

.story-card-modern:hover {
  transform: translateY(-10px);
  box-shadow: 0 30px 80px rgba(0, 0, 0, 0.12);
  border-color: rgba(59, 130, 246, 0.2);
}

.story-header-modern {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.story-icon-modern {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.story-title-modern {
  font-size: 2rem;
  font-weight: 800;
  color: #1f2937 !important;
  margin: 0;
}

.story-text-modern {
  font-size: 1.125rem;
  line-height: 1.8;
  color: #4b5563 !important;
  margin-bottom: 2rem;
}

.story-highlights {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.highlight-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.highlight-icon {
  color: #10b981;
  font-size: 1rem;
  flex-shrink: 0;
}

.highlight-item span {
  color: #374151 !important;
  font-weight: 500;
}

.mission-vision-modern {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  height: 100%;
}

.mv-item-modern {
  background: white;
  border-radius: 20px;
  padding: 2.5rem;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  flex: 1;
}

.mv-item-modern:hover {
  transform: translateY(-5px);
  box-shadow: 0 25px 60px rgba(0, 0, 0, 0.1);
}

.mission-modern {
  border-left: 4px solid #3b82f6;
}

.vision-modern {
  border-left: 4px solid #10b981;
}

.mv-header-modern {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.mv-icon-modern {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.mission-icon {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.vision-icon {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.mv-item-modern h4 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937 !important;
  margin: 0;
}

.mv-item-modern p {
  font-size: 1rem;
  line-height: 1.7;
  color: #6b7280 !important;
  margin: 0;
}

.features-grid-modern {
  margin-top: 5rem;
  padding-top: 3rem;
  border-top: 1px solid rgba(59, 130, 246, 0.1);
}

.features-title-modern {
  font-size: 2.5rem;
  font-weight: 800;
  color: #1f2937 !important;
  margin-bottom: 1rem;
}

.features-subtitle-modern {
  font-size: 1.125rem;
  color: #6b7280 !important;
  max-width: 600px;
  margin: 0 auto;
}

.feature-card-modern {
  background: white;
  border-radius: 20px;
  padding: 2.5rem 2rem;
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
  height: 100%;
}

.feature-card-modern:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.1);
  border-color: rgba(59, 130, 246, 0.2);
}

.feature-icon-modern {
  width: 70px;
  height: 70px;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.75rem;
  margin: 0 auto 1.5rem;
  transition: all 0.3s ease;
}

.speed-icon {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.security-icon {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.analytics-icon {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

.support-icon {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
}

.feature-card-modern:hover .feature-icon-modern {
  transform: scale(1.1);
}

.feature-card-modern h5 {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937 !important;
  margin-bottom: 1rem;
}

.feature-card-modern p {
  font-size: 0.875rem;
  line-height: 1.6;
  color: #6b7280 !important;
  margin: 0;
}

.about-cta-modern {
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.05) 0%,
    rgba(59, 130, 246, 0.02) 100%
  );
  border: 2px solid rgba(59, 130, 246, 0.1);
  border-radius: 30px;
  padding: 4rem 3rem;
  margin-top: 5rem;
}

.cta-title-modern {
  font-size: 2.25rem;
  font-weight: 800;
  color: #1f2937 !important;
  margin-bottom: 1rem;
}

.cta-subtitle-modern {
  font-size: 1.125rem;
  color: #6b7280 !important;
  margin-bottom: 2.5rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-buttons-modern .btn {
  border-radius: 15px;
  padding: 1rem 2.5rem;
  font-weight: 600;
  font-size: 1.125rem;
  transition: all 0.3s ease;
}

.cta-buttons-modern .btn:hover {
  transform: translateY(-3px);
}

.cta-buttons-modern .btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: none;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.cta-buttons-modern .btn-primary:hover {
  box-shadow: 0 15px 40px rgba(59, 130, 246, 0.4);
}

.cta-buttons-modern .btn-outline-primary {
  border: 2px solid #3b82f6;
  color: #3b82f6;
}

.cta-buttons-modern .btn-outline-primary:hover {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

/* Redesigned Pricing Section Styles */
.pricing-section-modern {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  position: relative;
  overflow: hidden;
}

.pricing-section-modern::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(59,130,246,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.pricing-badge-modern {
  display: inline-flex;
  align-items: center;
  padding: 1rem 2rem;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.2) 0%,
    rgba(59, 130, 246, 0.1) 100%
  );
  border: 2px solid rgba(59, 130, 246, 0.3);
  border-radius: 50px;
  color: #3b82f6 !important;
  font-size: 0.875rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.pricing-badge-modern:hover {
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.3) 0%,
    rgba(59, 130, 246, 0.2) 100%
  );
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
}

.pricing-title-modern {
  font-size: 4rem;
  font-weight: 900;
  line-height: 1.1;
  color: white !important;
  margin-bottom: 2rem;
  position: relative;
  z-index: 2;
}

.pricing-subtitle-modern {
  font-size: 1.375rem;
  line-height: 1.7;
  color: rgba(255, 255, 255, 0.8) !important;
  max-width: 700px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.pricing-toggle-modern {
  position: relative;
  z-index: 2;
}

.toggle-container {
  display: inline-flex;
  align-items: center;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 50px;
  padding: 1rem 2rem;
  backdrop-filter: blur(20px);
}

.toggle-label {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 600;
  font-size: 1rem;
}

.save-badge {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 700;
  margin-left: 0.5rem;
}

.toggle-switch {
  position: relative;
  width: 60px;
  height: 30px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-switch label {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 30px;
  transition: 0.3s;
}

.toggle-switch label:before {
  position: absolute;
  content: "";
  height: 22px;
  width: 22px;
  left: 4px;
  bottom: 4px;
  background: white;
  border-radius: 50%;
  transition: 0.3s;
}

.toggle-switch input:checked + label {
  background: #3b82f6;
}

.toggle-switch input:checked + label:before {
  transform: translateX(30px);
}

.pricing-card-modern {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 3rem 2.5rem;
  backdrop-filter: blur(20px);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 2;
}

.pricing-card-modern:hover {
  transform: translateY(-10px);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 30px 80px rgba(0, 0, 0, 0.3);
}

.popular-modern {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.08);
  transform: scale(1.05);
}

.popular-modern:hover {
  transform: scale(1.05) translateY(-10px);
}

.popular-badge-modern {
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  padding: 0.75rem 2rem;
  border-radius: 25px;
  font-size: 0.875rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 10px 30px rgba(59, 130, 246, 0.4);
}

.pricing-header-modern {
  text-align: center;
  margin-bottom: 2rem;
}

.plan-icon-modern {
  width: 80px;
  height: 80px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  margin: 0 auto 1.5rem;
  transition: all 0.3s ease;
}

.starter-icon {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  color: white;
}

.professional-icon {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.enterprise-icon {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

.pricing-card-modern:hover .plan-icon-modern {
  transform: scale(1.1);
}

.plan-name-modern {
  font-size: 2rem;
  font-weight: 800;
  color: white !important;
  margin-bottom: 0.75rem;
}

.plan-desc-modern {
  color: rgba(255, 255, 255, 0.7) !important;
  font-size: 1rem;
  margin: 0;
}

.pricing-amount-modern {
  text-align: center;
  margin-bottom: 2.5rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.price-modern {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.currency-modern {
  font-size: 1.5rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
}

.amount-modern {
  font-size: 4rem;
  font-weight: 900;
  color: white;
  line-height: 1;
}

.period-modern {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.7);
}

.price-note-modern {
  color: rgba(255, 255, 255, 0.6) !important;
  font-size: 0.875rem;
  margin: 0;
}

.features-modern {
  flex-grow: 1;
  margin-bottom: 2.5rem;
}

.feature-modern {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.feature-modern:last-child {
  border-bottom: none;
}

.check-icon {
  color: #10b981;
  font-size: 1.125rem;
  flex-shrink: 0;
}

.feature-modern span {
  color: rgba(255, 255, 255, 0.9) !important;
  font-size: 0.95rem;
  line-height: 1.5;
}

.pricing-btn-modern {
  width: 100%;
  padding: 1.25rem 2rem;
  border: none;
  border-radius: 15px;
  font-size: 1.125rem;
  font-weight: 700;
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin-top: auto;
}

.starter-btn-modern {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.starter-btn-modern:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-3px);
  border-color: rgba(255, 255, 255, 0.3);
}

.professional-btn-modern {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.professional-btn-modern:hover {
  box-shadow: 0 15px 40px rgba(59, 130, 246, 0.5);
  transform: translateY(-3px);
}

.enterprise-btn-modern {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
}

.enterprise-btn-modern:hover {
  box-shadow: 0 15px 40px rgba(245, 158, 11, 0.5);
  transform: translateY(-3px);
}

.btn-arrow {
  transition: transform 0.3s ease;
}

.pricing-btn-modern:hover .btn-arrow {
  transform: translateX(5px);
}

.pricing-footer-modern {
  position: relative;
  z-index: 2;
}

.pricing-guarantee {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(20px);
  max-width: 600px;
  margin: 0 auto;
}

.guarantee-icon {
  font-size: 3rem;
  color: #10b981;
  flex-shrink: 0;
}

.guarantee-text h5 {
  color: white !important;
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.guarantee-text p {
  color: rgba(255, 255, 255, 0.7) !important;
  margin: 0;
  font-size: 0.95rem;
}

.pricing-features-grid {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(20px);
}

.pricing-feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  text-align: center;
}

.feature-icon-small {
  font-size: 1.5rem;
  color: #3b82f6;
}

.pricing-feature-item span {
  color: rgba(255, 255, 255, 0.8) !important;
  font-weight: 600;
  font-size: 0.875rem;
}

/* Responsive improvements for redesigned sections */
@media (max-width: 992px) {
  .about-title-modern {
    font-size: 3rem;
  }

  .pricing-title-modern {
    font-size: 3rem;
  }

  .story-card-modern {
    padding: 2rem;
  }

  .mv-item-modern {
    padding: 2rem;
  }

  .pricing-card-modern {
    padding: 2.5rem 2rem;
  }

  .popular-modern {
    transform: none;
    margin-bottom: 2rem;
  }

  .popular-modern:hover {
    transform: translateY(-10px);
  }
}

@media (max-width: 768px) {
  .about-title-modern {
    font-size: 2.5rem;
  }

  .pricing-title-modern {
    font-size: 2.5rem;
  }

  .about-subtitle-modern {
    font-size: 1.125rem;
  }

  .pricing-subtitle-modern {
    font-size: 1.125rem;
  }

  .story-card-modern {
    padding: 1.5rem;
    margin-bottom: 2rem;
  }

  .story-header-modern {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .mv-item-modern {
    padding: 1.5rem;
  }

  .mission-vision-modern {
    gap: 1.5rem;
  }

  .feature-card-modern {
    padding: 2rem 1.5rem;
    margin-bottom: 1.5rem;
  }

  .about-cta-modern {
    padding: 3rem 2rem;
  }

  .cta-buttons-modern {
    flex-direction: column;
    gap: 1rem;
  }

  .cta-buttons-modern .btn {
    width: 100%;
  }

  .pricing-card-modern {
    padding: 2rem 1.5rem;
    margin-bottom: 2rem;
  }

  .amount-modern {
    font-size: 3rem;
  }

  .pricing-guarantee {
    flex-direction: column;
    text-align: center;
    padding: 1.5rem;
  }

  .guarantee-icon {
    font-size: 2.5rem;
  }

  .toggle-container {
    flex-direction: column;
    gap: 0.75rem;
    padding: 1.5rem;
  }
}

@media (max-width: 576px) {
  .about-title-modern {
    font-size: 2rem;
  }

  .pricing-title-modern {
    font-size: 2rem;
  }

  .story-title-modern {
    font-size: 1.5rem;
  }

  .features-title-modern {
    font-size: 2rem;
  }

  .cta-title-modern {
    font-size: 1.75rem;
  }

  .plan-name-modern {
    font-size: 1.5rem;
  }

  .amount-modern {
    font-size: 2.5rem;
  }

  .story-card-modern {
    padding: 1.25rem;
  }

  .mv-item-modern {
    padding: 1.25rem;
  }

  .feature-card-modern {
    padding: 1.5rem 1.25rem;
  }

  .pricing-card-modern {
    padding: 1.5rem 1.25rem;
  }

  .about-cta-modern {
    padding: 2rem 1.5rem;
  }

  .pricing-guarantee {
    padding: 1.25rem;
  }

  .pricing-features-grid {
    padding: 1.5rem;
  }
}

/* Ensure navbar text is visible */
.navbar-dark .navbar-nav .nav-link {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* ===== CUSTOMERS PAGE ENHANCED STYLES ===== */

/* Enhanced Header Card */
.customers-header-card {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: 16px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.customers-header-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-500), var(--primary-300));
  border-radius: 16px 16px 0 0;
}

.customers-header-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.customers-header-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--primary-500), var(--primary-400));
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.3);
}

.customers-header-title {
  color: var(--text-primary);
  font-weight: 700;
  font-size: 1.5rem;
  margin: 0;
}

.customers-header-subtitle {
  color: var(--text-muted);
  font-size: 0.95rem;
  margin: 0;
}

.customers-header-actions {
  gap: 0.75rem;
}

.customers-action-btn {
  border: none;
  border-radius: 10px;
  padding: 0.75rem 1.25rem;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.customers-filter-btn {
  background: var(--bg-card);
  color: var(--text-primary);
  border: 1px solid var(--border-primary) !important;
}

.customers-filter-btn:hover {
  background: var(--bg-card-hover);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.customers-reset-btn {
  background: var(--bg-surface);
  color: var(--text-secondary);
  border: 1px solid var(--border-secondary) !important;
}

.customers-reset-btn:hover {
  background: var(--bg-card);
  color: var(--text-primary);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.customers-export-btn {
  background: linear-gradient(135deg, var(--success-500), var(--success-400));
  color: white;
  border: none !important;
}

.customers-export-btn:hover {
  background: linear-gradient(135deg, var(--success-600), var(--success-500));
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(var(--success-rgb), 0.3);
}

.customers-filter-badge {
  background: var(--danger-500) !important;
  font-size: 0.7rem;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Modern Tab System */
.customers-tabs-container {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: 16px;
  padding: 0.5rem;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.customers-tabs-wrapper {
  display: flex;
  gap: 0.25rem;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.customers-tabs-wrapper::-webkit-scrollbar {
  display: none;
}

.customers-tab-btn {
  background: transparent;
  border: none;
  border-radius: 12px;
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--text-muted);
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  cursor: pointer;
  white-space: nowrap;
  min-width: fit-content;
}

.customers-tab-btn:hover {
  color: var(--text-primary);
  background: var(--bg-surface);
}

.customers-tab-btn.active {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-400));
  color: white;
  
}

.customers-tab-icon {
  font-size: 1rem;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.customers-tab-btn.active .customers-tab-icon {
  transform: scale(1.1);
}

.customers-tab-label {
  font-weight: 600;
}

.customers-tab-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 2px;
  background: var(--primary-400);
  border-radius: 1px;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.customers-tab-btn.active .customers-tab-indicator {
  width: 80%;
}

/* Enhanced Statistics Cards */
.customers-stat-card {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: 16px;
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  height: 100%;
}



.customers-stat-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  position: relative;
  z-index: 2;
}

.customers-stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  position: relative;
  overflow: hidden;
}

.customers-stat-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  opacity: 0.1;
  border-radius: inherit;
}

.customers-stat-icon-primary {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-400));
  box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.3);
}

.customers-stat-icon-success {
  background: linear-gradient(135deg, var(--success-500), var(--success-400));
  box-shadow: 0 4px 12px rgba(var(--success-rgb), 0.3);
}

.customers-stat-icon-warning {
  background: linear-gradient(135deg, var(--warning-500), var(--warning-400));
  box-shadow: 0 4px 12px rgba(var(--warning-rgb), 0.3);
}

.customers-stat-icon-info {
  background: linear-gradient(135deg, var(--info-500), var(--info-400));
  box-shadow: 0 4px 12px rgba(var(--info-rgb), 0.3);
}

.customers-stat-details {
  flex: 1;
}

.customers-stat-label {
  color: var(--text-muted);
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.customers-stat-value {
  color: var(--text-primary);
  font-size: 1.75rem;
  font-weight: 700;
  line-height: 1;
}

.customers-stat-decoration {
  position: absolute;
  top: -20px;
  right: -20px;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-200), var(--primary-100));
  border-radius: 50%;
  opacity: 0.1;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.customers-stat-card:hover .customers-stat-decoration {
  transform: scale(1.2);
  opacity: 0.15;
}

/* Enhanced Search Container */
.customers-search-container {
  position: relative;
}

.customers-search-wrapper {
  position: relative;
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.customers-search-wrapper:focus-within {
  border-color: var(--primary-400);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
  background: var(--bg-card-hover);
}

.customers-search-icon {
  color: var(--text-muted);
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.customers-search-wrapper:focus-within .customers-search-icon {
  color: var(--primary-400);
}

.customers-search-input {
  background: transparent !important;
  border: none !important;
  color: var(--text-primary) !important;
  font-size: 0.95rem;
  padding: 0 !important;
  flex: 1;
  outline: none !important;
  box-shadow: none !important;
}

.customers-search-input::placeholder {
  color: var(--text-muted);
  opacity: 0.8;
}
.customers-search-input:focus{
  outline: none !important;
  border: none !important;
  
}

.customers-search-clear {
  background: var(--bg-surface);
  border: 1px solid var(--border-secondary);
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-muted);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.customers-search-clear:hover {
  background: var(--danger-500);
  border-color: var(--danger-500);
  color: white;
  transform: scale(1.05);
}

/* Enhanced Table Container */
.customers-table-container {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: 16px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.customers-table-wrapper {
  position: relative;
}

/* Responsive Design for Customers Page */
@media (max-width: 768px) {
  .customers-header-card {
    padding: 1.5rem;
  }

  .customers-header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .customers-header-actions {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .customers-action-btn {
    flex: 1;
    min-width: 120px;
  }

  .customers-tabs-wrapper {
    flex-direction: column;
    gap: 0.5rem;
  }

  .customers-tab-btn {
    justify-content: flex-start;
    padding: 1rem;
  }

  .customers-stat-card {
    margin-bottom: 1rem;
  }

  .customers-search-wrapper {
    padding: 1rem;
  }
}

@media (max-width: 576px) {
  .customers-header-card {
    padding: 1rem;
  }

  .customers-header-title {
    font-size: 1.25rem;
  }

  .customers-header-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .customers-action-btn {
    width: 100%;
  }

  .customers-stat-content {
    gap: 0.75rem;
  }

  .customers-stat-icon {
    width: 40px;
    height: 40px;
  }

  .customers-stat-value {
    font-size: 1.5rem;
  }
}

/* Light Theme Overrides for Customers Page */
[data-theme="light"] .customers-header-card,
[data-theme="light"] .customers-tabs-container,
[data-theme="light"] .customers-stat-card,
[data-theme="light"] .customers-search-wrapper,
[data-theme="light"] .customers-table-container {
  background: var(--white);
  border-color: var(--border-light);
  box-shadow: 0 2px 12px rgba(11, 31, 58, 0.08);
}

[data-theme="light"] .customers-header-card:hover,
[data-theme="light"] .customers-stat-card:hover {
  box-shadow: 0 8px 30px rgba(11, 31, 58, 0.12);
}

[data-theme="light"] .customers-search-wrapper:focus-within {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(11, 31, 58, 0.1);
}

/* ===== ENHANCED PAGE HEADER COMPONENT ===== */

/* Enhanced Header Card */
.enhanced-page-header-card {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: 16px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}



.enhanced-page-header-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.enhanced-page-header-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--primary-500), var(--primary-400));
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.3);
}

.enhanced-page-header-title {
  color: var(--text-primary);
  font-weight: 700;
  font-size: 1.5rem;
  margin: 0;
}

.enhanced-page-header-subtitle {
  color: var(--text-muted);
  font-size: 0.95rem;
  margin: 0;
}

.enhanced-page-header-actions {
  gap: 0.75rem;
}

.enhanced-action-btn {
  border: none;
  border-radius: 10px;
  padding: 0.75rem 1.25rem;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.enhanced-filter-btn {
  background: var(--bg-card);
  color: var(--text-primary);
  border: 1px solid var(--border-primary) !important;
}

.enhanced-filter-btn:hover {
  background: var(--bg-card-hover);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.enhanced-reset-btn {
  background: var(--bg-surface);
  color: var(--text-secondary);
  border: 1px solid var(--border-secondary) !important;
}

.enhanced-reset-btn:hover {
  background: var(--bg-card);
  color: var(--text-primary);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.enhanced-filter-badge {
  background: var(--danger-500) !important;
  font-size: 0.7rem;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Enhanced Filters Container */
.enhanced-filters-container {
  position: relative;
  z-index: 10;
}

.enhanced-filters-card {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.enhanced-filters-body {
  padding: 1.5rem;
}

.enhanced-filters-actions {
  border-top: 1px solid var(--border-primary);
  display: flex;
  justify-content: flex-end;
}

/* ===== FILTER DROPDOWN FIXES ===== */

/* Fix dropdown toggle styling for theme compatibility */
.dropdown-toggle.form-control {
  background: var(--bg-card) !important;
  border: 1px solid var(--border-primary) !important;
  color: var(--text-primary) !important;
}

.dropdown-toggle.form-control:hover {
  background: var(--bg-card-hover) !important;
  border-color: var(--border-primary) !important;
}

.dropdown-toggle.form-control:focus {
  background: var(--bg-card) !important;
  border-color: var(--primary-400) !important;
  box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.2) !important;
}

/* Fix dropdown menu styling */
.dropdown-menu {
  background: var(--bg-card) !important;
  border: 1px solid var(--border-primary) !important;
  border-radius: 8px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
  z-index: 1050 !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.dropdown-item {
  color: var(--text-primary) !important;
  padding: 0.75rem 1rem !important;
  transition: all 0.2s ease !important;
}

.dropdown-item:hover,
.dropdown-item:focus {
  background: var(--bg-surface) !important;
  color: var(--text-primary) !important;
}

.dropdown-item.active,
.dropdown-item:active {
  background: var(--primary-500) !important;
  color: white !important;
}

/* Fix form labels */
.form-label {
  color: var(--text-primary) !important;
  font-weight: 600 !important;
}

/* Fix form inputs */
.form-control {
  background: var(--bg-card) !important;
  border: 1px solid var(--border-primary) !important;
  color: var(--text-primary) !important;
}

.form-control:focus {
  background: var(--bg-card) !important;
  border-color: var(--primary-400) !important;
  box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.2) !important;
  color: var(--text-primary) !important;
}

.form-control::placeholder {
  color: var(--text-muted) !important;
  opacity: 0.8 !important;
}

/* Fix z-index issues - ensure dropdowns appear above tabs */
.dropdown-menu {
  z-index: 1060 !important;
}

.customers-tabs-container {
  z-index: 10 !important;
}

.enhanced-filters-container {
  z-index: 20 !important;
}

/* Light theme specific overrides */
[data-theme="light"] .dropdown-toggle.form-control {
  background: var(--white) !important;
  border-color: var(--border-light) !important;
  color: var(--primary-900) !important;
}

[data-theme="light"] .dropdown-menu {
  background: var(--white) !important;
  border-color: var(--border-light) !important;
  box-shadow: 0 4px 20px rgba(11, 31, 58, 0.1) !important;
}

[data-theme="light"] .dropdown-item {
  color: var(--primary-900) !important;
}

[data-theme="light"] .dropdown-item:hover,
[data-theme="light"] .dropdown-item:focus {
  background: var(--primary-50) !important;
  color: var(--primary-900) !important;
}

[data-theme="light"] .form-control {
  background: var(--white) !important;
  border-color: var(--border-light) !important;
  color: var(--primary-900) !important;
}

[data-theme="light"] .form-label {
  color: var(--primary-900) !important;
}

.navbar-dark .navbar-nav .nav-link:hover {
  color: #3b82f6 !important;
}

.navbar-brand {
  color: white !important;
}

/* Ensure button text is visible */
.btn {
  color: inherit !important;
}

.btn-primary {
  color: white !important;
}

.btn-outline-primary {
  color: #3b82f6 !important;
}

.btn-outline-primary:hover {
  color: white !important;
}

/* Enhanced PageHeader Button Text Color Fix */
.enhanced-action-btn {
  color: var(--text-white) !important;
  background: linear-gradient(135deg, var(--primary-color), var(--complementary-color));
  border: none;
  border-radius: 8px;
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
 
}

.enhanced-action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.4);
  color: var(--text-white) !important;
}

.enhanced-action-btn:focus,
.enhanced-action-btn:active {
  color: var(--text-white) !important;
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

/* Search Input Border Fix */
.search-input-container .form-control {
  background-color: var(--bg-input);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.search-input-container .form-control:focus {
  background-color: var(--bg-input);
  border-color: var(--border-primary);
  color: var(--text-primary);
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

.search-input-container .form-control::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
}

/* Light Mode Text Color Fixes */
[data-theme="light"] .enhanced-page-header-title,
[data-theme="light"] .enhanced-page-header-subtitle,
[data-theme="light"] .form-label {
  color: var(--text-primary) !important;
}

[data-theme="light"] .enhanced-action-btn {
  color: white !important;
}

[data-theme="light"] .enhanced-action-btn:hover,
[data-theme="light"] .enhanced-action-btn:focus,
[data-theme="light"] .enhanced-action-btn:active {
  color: white !important;
}

/* Enhanced Filter Button Styling */
.enhanced-filter-btn {
  background: linear-gradient(135deg, var(--primary-color), var(--complementary-color));
  color: var(--text-white) !important;
  border: none;
  border-radius: 8px;
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(var(--primary-rgb), 0.3);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.enhanced-filter-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.4);
  color: var(--text-white) !important;
  background: linear-gradient(135deg, var(--complementary-color), var(--primary-color));
}

.enhanced-filter-btn:focus,
.enhanced-filter-btn:active {
  color: var(--text-white) !important;
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

/* Filter button active state when filters are shown */
.enhanced-filter-btn.active {
  background: linear-gradient(135deg, var(--complementary-color), var(--accent-color));
  box-shadow: 0 2px 8px rgba(var(--complementary-rgb), 0.4);
}

.enhanced-filter-btn.active:hover {
  background: linear-gradient(135deg, var(--accent-color), var(--complementary-color));
}

/* Light mode filter button */
[data-theme="light"] .enhanced-filter-btn {
  color: white !important;
}

[data-theme="light"] .enhanced-filter-btn:hover,
[data-theme="light"] .enhanced-filter-btn:focus,
[data-theme="light"] .enhanced-filter-btn:active {
  color: white !important;
}

/* ===== GLASSMORPHISM TABLE BADGES ===== */

/* Base prompt-style badge styling with blue outer layer */
.badge {
  position: relative !important;
  padding: 0.5rem 0.875rem !important;
  font-size: 0.75rem !important;
  font-weight: 600 !important;
  letter-spacing: 0.5px !important;
  text-transform: uppercase !important;
  border-radius: 16px !important;
  border: 2px solid #3b82f6 !important;
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.1) 0%,
    rgba(59, 130, 246, 0.05) 50%,
    rgba(59, 130, 246, 0.1) 100%) !important;
  backdrop-filter: blur(10px) !important;
  box-shadow:
    0 4px 15px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  overflow: hidden !important;
  color: #3b82f6 !important;
}

/* Auto-switching background animation */
.badge::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(59, 130, 246, 0.3),
    transparent
  );
  animation: autoSwitchBadge 3s ease-in-out infinite;
}

@keyframes autoSwitchBadge {
  0%, 100% {
    left: -100%;
    opacity: 0;
  }
  50% {
    left: 100%;
    opacity: 1;
  }
}

.badge:hover::before {
  animation-duration: 1s;
}

/* Success badges (Active, Approved) - Blue outer, green inner */
.badge.bg-success {
  border-color: #3b82f6 !important;
  background: linear-gradient(135deg,
    rgba(16, 185, 129, 0.15) 0%,
    rgba(16, 185, 129, 0.08) 50%,
    rgba(16, 185, 129, 0.15) 100%) !important;
  color: #10b981 !important;
  box-shadow:
    0 4px 15px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(16, 185, 129, 0.2) !important;
}

.badge.bg-success::before {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(16, 185, 129, 0.3),
    transparent
  );
}

.badge.bg-success:hover {
  transform: translateY(-2px) scale(1.05) !important;
  border-color: #10b981 !important;
  box-shadow:
    0 6px 20px rgba(16, 185, 129, 0.3),
    0 0 0 1px rgba(16, 185, 129, 0.2) !important;
}

/* Warning badges (Pending, Submitted) - Blue outer, amber inner */
.badge.bg-warning {
  border-color: #3b82f6 !important;
  background: linear-gradient(135deg,
    rgba(245, 158, 11, 0.15) 0%,
    rgba(245, 158, 11, 0.08) 50%,
    rgba(245, 158, 11, 0.15) 100%) !important;
  color: #f59e0b !important;
  box-shadow:
    0 4px 15px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(245, 158, 11, 0.2) !important;
}

.badge.bg-warning::before {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(245, 158, 11, 0.3),
    transparent
  );
}

.badge.bg-warning:hover {
  transform: translateY(-2px) scale(1.05) !important;
  border-color: #f59e0b !important;
  box-shadow:
    0 6px 20px rgba(245, 158, 11, 0.3),
    0 0 0 1px rgba(245, 158, 11, 0.2) !important;
}

/* Danger badges (Rejected, Inactive) - Blue outer, red inner */
.badge.bg-danger {
  border-color: #3b82f6 !important;
  background: linear-gradient(135deg,
    rgba(239, 68, 68, 0.15) 0%,
    rgba(239, 68, 68, 0.08) 50%,
    rgba(239, 68, 68, 0.15) 100%) !important;
  color: #ef4444 !important;
  box-shadow:
    0 4px 15px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(239, 68, 68, 0.2) !important;
}

.badge.bg-danger::before {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(239, 68, 68, 0.3),
    transparent
  );
}

.badge.bg-danger:hover {
  transform: translateY(-2px) scale(1.05) !important;
  border-color: #ef4444 !important;
  box-shadow:
    0 6px 20px rgba(239, 68, 68, 0.3),
    0 0 0 1px rgba(239, 68, 68, 0.2) !important;
}

/* Info badges (User Type) - Blue outer, blue inner */
.badge.bg-info {
  border-color: #3b82f6 !important;
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.15) 0%,
    rgba(59, 130, 246, 0.08) 50%,
    rgba(59, 130, 246, 0.15) 100%) !important;
  color: #3b82f6 !important;
  box-shadow:
    0 4px 15px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(59, 130, 246, 0.2) !important;
}

.badge.bg-info:hover {
  transform: translateY(-2px) scale(1.05) !important;
  border-color: #1d4ed8 !important;
  box-shadow:
    0 6px 20px rgba(59, 130, 246, 0.4),
    0 0 0 1px rgba(59, 130, 246, 0.3) !important;
}

/* Secondary badges (Unknown, Default) - Blue outer, gray inner */
.badge.bg-secondary {
  border-color: #3b82f6 !important;
  background: linear-gradient(135deg,
    rgba(107, 114, 128, 0.15) 0%,
    rgba(107, 114, 128, 0.08) 50%,
    rgba(107, 114, 128, 0.15) 100%) !important;
  color: #6b7280 !important;
  box-shadow:
    0 4px 15px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(107, 114, 128, 0.2) !important;
}

.badge.bg-secondary::before {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(107, 114, 128, 0.3),
    transparent
  );
}

.badge.bg-secondary:hover {
  transform: translateY(-2px) scale(1.05) !important;
  border-color: #6b7280 !important;
  box-shadow:
    0 6px 20px rgba(107, 114, 128, 0.3),
    0 0 0 1px rgba(107, 114, 128, 0.2) !important;
}

/* ===== SEARCH INPUT SPECIFIC STYLING ===== */

/* Override form-control specifically for search input */
.customers-search-container .customers-search-input.form-control {
  background-color: var(--bg-input) !important;
  border: 1px solid var(--border-color) !important;
  color: var(--text-primary) !important;
  border-radius: 12px !important;
  padding: 0.75rem 3rem 0.75rem 3rem !important;
  font-size: 0.95rem !important;
  transition: all 0.3s ease !important;
  backdrop-filter: blur(10px) !important;
  
}

.customers-search-container .customers-search-input.form-control:focus {
  background-color: var(--bg-input) !important;
  border-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
  
  outline: none !important;
}

.customers-search-container .customers-search-input.form-control::placeholder {
  color: var(--text-secondary) !important;
  opacity: 0.7 !important;
}

/* Enhanced search container styling */
.customers-search-container {
  position: relative;
  backdrop-filter: blur(10px);
  border-radius: 12px;
  background: rgba(var(--bg-card-rgb), 0.5);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.customers-search-container:hover {
  border-color: var(--border-primary);
  box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.1);
}

.customers-search-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.customers-search-icon {
  position: absolute;
  left: 1rem;
  z-index: 10;
  color: var(--text-secondary);
  pointer-events: none;
  transition: color 0.3s ease;
}

.customers-search-container:focus-within .customers-search-icon {
  color: var(--primary-color);
}

.customers-search-clear {
  position: absolute;
  right: 1rem;
  z-index: 10;
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.customers-search-clear:hover {
  color: var(--danger-color);
  background: rgba(var(--danger-rgb), 0.1);
}

/* Light mode specific overrides for search */
[data-theme="light"] .customers-search-container .customers-search-input.form-control {
  background-color: var(--white) !important;
  border-color: var(--border-light) !important;
  color: var(--primary-900) !important;
}

[data-theme="light"] .customers-search-container .customers-search-input.form-control:focus {
  background-color: var(--white) !important;
  border-color: var(--primary-color) !important;
  color: var(--primary-900) !important;
}

[data-theme="light"] .customers-search-container .customers-search-input.form-control::placeholder {
  color: var(--text-muted) !important;
}

/* ===== THEME-SPECIFIC BADGE ENHANCEMENTS ===== */

/* Dark theme badge enhancements - maintain blue outer layer */
[data-theme="dark"] .badge {
  border-color: #3b82f6 !important;
  backdrop-filter: blur(15px) !important;
  box-shadow:
    0 4px 15px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

[data-theme="dark"] .badge.bg-success {
  background: linear-gradient(135deg,
    rgba(16, 185, 129, 0.2) 0%,
    rgba(16, 185, 129, 0.1) 50%,
    rgba(16, 185, 129, 0.2) 100%) !important;
  color: #34d399 !important;
}

[data-theme="dark"] .badge.bg-warning {
  background: linear-gradient(135deg,
    rgba(245, 158, 11, 0.2) 0%,
    rgba(245, 158, 11, 0.1) 50%,
    rgba(245, 158, 11, 0.2) 100%) !important;
  color: #fbbf24 !important;
}

[data-theme="dark"] .badge.bg-danger {
  background: linear-gradient(135deg,
    rgba(239, 68, 68, 0.2) 0%,
    rgba(239, 68, 68, 0.1) 50%,
    rgba(239, 68, 68, 0.2) 100%) !important;
  color: #f87171 !important;
}

[data-theme="dark"] .badge.bg-info {
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.2) 0%,
    rgba(59, 130, 246, 0.1) 50%,
    rgba(59, 130, 246, 0.2) 100%) !important;
  color: #60a5fa !important;
}

[data-theme="dark"] .badge.bg-secondary {
  background: linear-gradient(135deg,
    rgba(107, 114, 128, 0.2) 0%,
    rgba(107, 114, 128, 0.1) 50%,
    rgba(107, 114, 128, 0.2) 100%) !important;
  color: #9ca3af !important;
}

/* Light theme badge enhancements - maintain blue outer layer */
[data-theme="light"] .badge {
  border-color: #3b82f6 !important;
  backdrop-filter: blur(10px) !important;
  box-shadow:
    0 4px 15px rgba(59, 130, 246, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

[data-theme="light"] .badge.bg-success {
  background: linear-gradient(135deg,
    rgba(16, 185, 129, 0.12) 0%,
    rgba(16, 185, 129, 0.06) 50%,
    rgba(16, 185, 129, 0.12) 100%) !important;
  color: #059669 !important;
}

[data-theme="light"] .badge.bg-warning {
  background: linear-gradient(135deg,
    rgba(245, 158, 11, 0.12) 0%,
    rgba(245, 158, 11, 0.06) 50%,
    rgba(245, 158, 11, 0.12) 100%) !important;
  color: #d97706 !important;
}

[data-theme="light"] .badge.bg-danger {
  background: linear-gradient(135deg,
    rgba(239, 68, 68, 0.12) 0%,
    rgba(239, 68, 68, 0.06) 50%,
    rgba(239, 68, 68, 0.12) 100%) !important;
  color: #dc2626 !important;
}

[data-theme="light"] .badge.bg-info {
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.12) 0%,
    rgba(59, 130, 246, 0.06) 50%,
    rgba(59, 130, 246, 0.12) 100%) !important;
  color: #2563eb !important;
}

[data-theme="light"] .badge.bg-secondary {
  background: linear-gradient(135deg,
    rgba(107, 114, 128, 0.12) 0%,
    rgba(107, 114, 128, 0.06) 50%,
    rgba(107, 114, 128, 0.12) 100%) !important;
  color: #4b5563 !important;
}

/* White Theme About Us Section Styles */
.about-section-white {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #ffffff 100%);
  position: relative;
  overflow: hidden;
  min-height: 100vh;
}

.about-bg-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="aboutGrid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(59,130,246,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23aboutGrid)"/></svg>');
  opacity: 0.4;
}

.about-badge-dark {
  display: inline-flex;
  align-items: center;
  padding: 1rem 2rem;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.2) 0%,
    rgba(59, 130, 246, 0.1) 100%
  );
  border: 2px solid rgba(59, 130, 246, 0.3);
  border-radius: 50px;
  color: #3b82f6 !important;
  font-size: 0.875rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  backdrop-filter: blur(20px);
}

.about-badge-dark:hover {
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.3) 0%,
    rgba(59, 130, 246, 0.2) 100%
  );
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
}

.about-title-dark {
  font-size: 4rem;
  font-weight: 900;
  line-height: 1.1;
  color: white !important;
  margin-bottom: 2rem;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.text-gradient {
  background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 50%, #10b981 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.about-subtitle-dark {
  font-size: 1.375rem;
  line-height: 1.7;
  color: rgba(255, 255, 255, 0.8) !important;
  max-width: 800px;
  font-weight: 400;
}

/* About Stats Grid */
.about-stats-grid {
  margin: 4rem 0;
}

.about-stat-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(20px);
  height: 100%;
}

.about-stat-card:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-10px);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.stat-icon-about {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  margin: 0 auto 1.5rem;
  transition: all 0.3s ease;
}

.about-stat-card:hover .stat-icon-about {
  transform: scale(1.1);
  box-shadow: 0 10px 30px rgba(59, 130, 246, 0.4);
}

.stat-number-about {
  font-size: 2.5rem;
  font-weight: 800;
  color: white !important;
  line-height: 1;
  margin-bottom: 0.5rem;
}

.stat-label-about {
  font-size: 1rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.7) !important;
}

/* Story Card Dark */
.story-card-dark {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 3rem;
  backdrop-filter: blur(20px);
  transition: all 0.3s ease;
  height: 100%;
}

.story-card-dark:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-5px);
  border-color: rgba(59, 130, 246, 0.2);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
}

.story-header-dark {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.story-icon-dark {
  width: 70px;
  height: 70px;
  border-radius: 18px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.75rem;
  flex-shrink: 0;
}

.story-title-dark {
  font-size: 2.25rem;
  font-weight: 800;
  color: white !important;
  margin: 0;
  line-height: 1.2;
}

.story-subtitle-dark {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.6) !important;
  margin: 0.5rem 0 0 0;
}

.story-text-dark {
  font-size: 1.125rem;
  line-height: 1.8;
  color: rgba(255, 255, 255, 0.8) !important;
  margin-bottom: 2rem;
}

/* Timeline Styles */
.story-timeline {
  margin-top: 2rem;
}

.timeline-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.5rem;
  position: relative;
}

.timeline-item:not(:last-child)::after {
  content: "";
  position: absolute;
  left: 12px;
  top: 30px;
  width: 2px;
  height: calc(100% + 1rem);
  background: linear-gradient(180deg, #3b82f6 0%, rgba(59, 130, 246, 0.3) 100%);
}

.timeline-dot {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: 3px solid rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
  position: relative;
  z-index: 2;
}

.timeline-content h6 {
  font-size: 1rem;
  font-weight: 700;
  color: white !important;
  margin-bottom: 0.5rem;
}

.timeline-content p {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7) !important;
  margin: 0;
  line-height: 1.5;
}

/* Mission Vision Dark */
.mission-vision-dark {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  height: 100%;
}

.mv-card-dark {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(20px);
  transition: all 0.3s ease;
  flex: 1;
}

.mv-card-dark:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.mission-card-dark {
  border-left: 4px solid #3b82f6;
}

.vision-card-dark {
  border-left: 4px solid #10b981;
}

.values-card-dark {
  border-left: 4px solid #f59e0b;
}

.mv-icon-dark {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  margin-bottom: 1rem;
}

.mission-icon-dark {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

.vision-icon-dark {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

.values-icon-dark {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
}

.mv-title-dark {
  font-size: 1.25rem;
  font-weight: 700;
  color: white !important;
  margin-bottom: 1rem;
}

.mv-text-dark {
  font-size: 0.95rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8) !important;
  margin: 0;
}

.values-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 1rem;
}

.value-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.value-check {
  color: #f59e0b !important;
  font-size: 1rem;
  flex-shrink: 0;
}

.value-item span {
  color: rgba(255, 255, 255, 0.8) !important;
  font-weight: 500;
}

/* Features Grid Dark */
.features-grid-dark {
  margin-top: 5rem;
  padding-top: 3rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.features-header-dark {
  margin-bottom: 3rem;
}

.features-title-dark {
  font-size: 2.5rem;
  font-weight: 800;
  color: white !important;
  margin-bottom: 1rem;
}

.features-subtitle-dark {
  font-size: 1.125rem;
  color: rgba(255, 255, 255, 0.7) !important;
  max-width: 600px;
  margin: 0 auto;
}

.feature-card-dark {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2.5rem 2rem;
  text-align: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(20px);
  height: 100%;
}

.feature-card-dark:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-8px);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.feature-icon-dark {
  width: 70px;
  height: 70px;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.75rem;
  margin: 0 auto 1.5rem;
  transition: all 0.3s ease;
}

.speed-icon-dark {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.security-icon-dark {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.analytics-icon-dark {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

.support-icon-dark {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
}

.feature-card-dark:hover .feature-icon-dark {
  transform: scale(1.1);
}

.feature-title-dark {
  font-size: 1.25rem;
  font-weight: 700;
  color: white !important;
  margin-bottom: 1rem;
}

.feature-desc-dark {
  font-size: 0.95rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.7) !important;
  margin: 0;
}

/* CTA Section Dark */
.about-cta-dark {
  margin-top: 5rem;
}

.cta-content-dark {
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.1) 0%,
    rgba(16, 185, 129, 0.05) 100%
  );
  border: 2px solid rgba(59, 130, 246, 0.2);
  border-radius: 30px;
  padding: 4rem 3rem;
  backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
}

.cta-content-dark::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(59, 130, 246, 0.1) 50%,
    transparent 70%
  );
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%,
  100% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(100%);
  }
}

.cta-title-dark {
  font-size: 2.5rem;
  font-weight: 800;
  color: white !important;
  margin-bottom: 1rem;
  position: relative;
  z-index: 2;
}

.cta-subtitle-dark {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.8) !important;
  margin-bottom: 2.5rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  z-index: 2;
}

.cta-buttons-dark {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
  position: relative;
  z-index: 2;
}

.btn-cta-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white !important;
  border: none;
  border-radius: 15px;
  padding: 1.25rem 2.5rem;
  font-size: 1.125rem;
  font-weight: 700;
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.btn-cta-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(59, 130, 246, 0.5);
  background: linear-gradient(135deg, #1d4ed8 0%, #3b82f6 100%);
}

.btn-cta-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white !important;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  padding: 1.25rem 2.5rem;
  font-size: 1.125rem;
  font-weight: 700;
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  backdrop-filter: blur(20px);
}

.btn-cta-secondary:hover {
  transform: translateY(-3px);
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 15px 40px rgba(255, 255, 255, 0.1);
}

/* Responsive Design for Dark About Us */
@media (max-width: 992px) {
  .about-title-dark {
    font-size: 3rem;
  }

  .story-card-dark {
    padding: 2rem;
    margin-bottom: 2rem;
  }

  .mv-card-dark {
    padding: 1.5rem;
  }

  .cta-content-dark {
    padding: 3rem 2rem;
  }

  .story-header-dark {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .timeline-item {
    flex-direction: column;
    text-align: center;
  }

  .timeline-item::after {
    display: none;
  }
}

@media (max-width: 768px) {
  .about-title-dark {
    font-size: 2.5rem;
  }

  .about-subtitle-dark {
    font-size: 1.125rem;
  }

  .story-title-dark {
    font-size: 1.75rem;
  }

  .features-title-dark {
    font-size: 2rem;
  }

  .cta-title-dark {
    font-size: 2rem;
  }

  .story-card-dark {
    padding: 1.5rem;
  }

  .mv-card-dark {
    padding: 1.25rem;
    margin-bottom: 1rem;
  }

  .feature-card-dark {
    padding: 2rem 1.5rem;
    margin-bottom: 1.5rem;
  }

  .cta-content-dark {
    padding: 2.5rem 1.5rem;
  }

  .cta-buttons-dark {
    flex-direction: column;
    align-items: center;
  }

  .btn-cta-primary,
  .btn-cta-secondary {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }

  .about-stats-grid .row {
    gap: 1rem;
  }

  .about-stat-card {
    padding: 1.5rem;
  }

  .stat-number-about {
    font-size: 2rem;
  }
}

@media (max-width: 576px) {
  .about-title-dark {
    font-size: 2rem;
  }

  .story-title-dark {
    font-size: 1.5rem;
  }

  .features-title-dark {
    font-size: 1.75rem;
  }

  .cta-title-dark {
    font-size: 1.75rem;
  }

  .story-card-dark {
    padding: 1.25rem;
  }

  .mv-card-dark {
    padding: 1rem;
  }

  .feature-card-dark {
    padding: 1.5rem 1.25rem;
  }

  .cta-content-dark {
    padding: 2rem 1.25rem;
  }

  .about-stat-card {
    padding: 1.25rem;
  }

  .stat-number-about {
    font-size: 1.75rem;
  }

  .btn-cta-primary,
  .btn-cta-secondary {
    padding: 1rem 2rem;
    font-size: 1rem;
  }
}

/* ===== WHITE THEME ABOUT US SECTION STYLES ===== */

/* White Theme About Us Section */
.about-section-white {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #ffffff 100%);
  position: relative;
  overflow: hidden;
  min-height: 100vh;
}

.about-bg-pattern-white {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(
      circle at 20% 20%,
      rgba(59, 130, 246, 0.02) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 80%,
      rgba(16, 185, 129, 0.02) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 60%,
      rgba(139, 92, 246, 0.02) 0%,
      transparent 50%
    );
  opacity: 1;
}

.about-badge-white {
  display: inline-flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.08) 0%,
    rgba(59, 130, 246, 0.12) 100%
  );
  border: 2px solid rgba(59, 130, 246, 0.15);
  border-radius: 50px;
  color: #3b82f6 !important;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.about-badge-white:hover {
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.12) 0%,
    rgba(59, 130, 246, 0.18) 100%
  );
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.about-title-white {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.2;
  color: #1f2937 !important;
  margin-bottom: 1.5rem;
  position: relative;
}

.text-gradient-white {
  background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 50%, #10b981 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.about-subtitle-white {
  font-size: 1.25rem;
  line-height: 1.6;
  color: #6b7280 !important;
  max-width: 700px;
  font-weight: 400;
}

/* About Stats Grid - White Theme */
.about-stats-grid-white {
  margin: 3rem 0;
}

.about-stat-card-white {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  height: 100%;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.about-stat-card-white:hover {
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-8px);
  border-color: rgba(59, 130, 246, 0.2);
  box-shadow: 0 15px 40px rgba(59, 130, 246, 0.1);
}

.stat-icon-about-white {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  margin: 0 auto 1.5rem;
  transition: all 0.3s ease;
}

.about-stat-card-white:hover .stat-icon-about-white {
  transform: scale(1.1);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.stat-number-about-white {
  font-size: 2.5rem;
  font-weight: 800;
  color: #1f2937 !important;
  margin-bottom: 0.5rem;
  line-height: 1;
}

.stat-label-about-white {
  font-size: 0.875rem;
  color: #6b7280 !important;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Story Card - White Theme */
.story-card-white {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 24px;
  padding: 2.5rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.05);
  height: 100%;
}

.story-card-white:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(59, 130, 246, 0.2);
  box-shadow: 0 15px 50px rgba(59, 130, 246, 0.1);
  transform: translateY(-5px);
}

.story-header-white {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
}

.story-icon-white {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  margin-right: 1rem;
  transition: all 0.3s ease;
}

.story-card-white:hover .story-icon-white {
  transform: scale(1.1);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.story-title-white {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937 !important;
  margin-bottom: 0.5rem;
}

.story-subtitle-white {
  font-size: 1rem;
  color: #6b7280 !important;
  font-weight: 500;
}

.story-content-white {
  margin-top: 1.5rem;
}

.story-text-white {
  font-size: 1rem;
  line-height: 1.7;
  color: #4b5563 !important;
  margin-bottom: 1.5rem;
}

/* Timeline - White Theme */
.story-timeline-white {
  margin-top: 2rem;
}

.timeline-item-white {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  position: relative;
}

.timeline-item-white:not(:last-child)::after {
  content: "";
  position: absolute;
  left: 11px;
  top: 24px;
  width: 2px;
  height: calc(100% + 0.5rem);
  background: linear-gradient(
    to bottom,
    rgba(59, 130, 246, 0.3),
    rgba(59, 130, 246, 0.1)
  );
}

.timeline-dot-white {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  margin-right: 1rem;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.timeline-content-white h6 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937 !important;
  margin-bottom: 0.25rem;
}

.timeline-content-white p {
  font-size: 0.875rem;
  color: #6b7280 !important;
  margin: 0;
  line-height: 1.5;
}

/* Mission & Vision Cards - White Theme */
.mission-vision-white {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.mv-card-white {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 20px;
  padding: 2rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.mv-card-white:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(59, 130, 246, 0.2);
  transform: translateY(-5px);
  box-shadow: 0 12px 35px rgba(59, 130, 246, 0.1);
}

.mv-icon-white {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.mv-card-white:hover .mv-icon-white {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3);
}

.mv-title-white {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937 !important;
  margin-bottom: 1rem;
}

.mv-text-white {
  font-size: 0.95rem;
  line-height: 1.6;
  color: #4b5563 !important;
  margin: 0;
}

/* Values List - White Theme */
.values-list-white {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.value-item-white {
  display: flex;
  align-items: center;
  font-size: 0.95rem;
  color: #4b5563 !important;
}

.value-check-white {
  color: #10b981 !important;
  margin-right: 0.75rem;
  font-size: 1rem;
}

/* Features Grid - White Theme */
.features-grid-white {
  margin: 4rem 0;
}

.features-header-white {
  margin-bottom: 3rem;
}

.features-title-white {
  font-size: 2.5rem;
  font-weight: 800;
  color: #1f2937 !important;
  margin-bottom: 1rem;
}

.features-subtitle-white {
  font-size: 1.125rem;
  color: #6b7280 !important;
  max-width: 600px;
  margin: 0 auto;
}

.feature-card-white {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 20px;
  padding: 2.5rem 2rem;
  text-align: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  height: 100%;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.feature-card-white:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(59, 130, 246, 0.2);
  transform: translateY(-8px);
  box-shadow: 0 15px 40px rgba(59, 130, 246, 0.1);
}

.feature-icon-white {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.75rem;
  margin: 0 auto 1.5rem;
  transition: all 0.3s ease;
}

.feature-card-white:hover .feature-icon-white {
  transform: scale(1.1);
  box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
}

.feature-title-white {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937 !important;
  margin-bottom: 1rem;
}

.feature-desc-white {
  font-size: 0.95rem;
  line-height: 1.6;
  color: #6b7280 !important;
  margin: 0;
}

/* Call to Action - White Theme */
.about-cta-white {
  margin-top: 4rem;
}

.cta-content-white {
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.05) 0%,
    rgba(59, 130, 246, 0.08) 100%
  );
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 24px;
  padding: 3rem 2rem;
  text-align: center;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.05);
}

.cta-title-white {
  font-size: 2.25rem;
  font-weight: 800;
  color: #1f2937 !important;
  margin-bottom: 1rem;
}

.cta-subtitle-white {
  font-size: 1.125rem;
  color: #6b7280 !important;
  margin-bottom: 2rem;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.cta-buttons-white {
  display: flex;
  gap: 1rem;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.btn-cta-primary-white {
  display: inline-flex;
  align-items: center;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white !important;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.btn-cta-primary-white:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
  color: white !important;
}

.btn-cta-secondary-white {
  display: inline-flex;
  align-items: center;
  padding: 1rem 2rem;
  background: rgba(255, 255, 255, 0.9);
  color: #3b82f6 !important;
  border: 2px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.btn-cta-secondary-white:hover {
  background: rgba(59, 130, 246, 0.05);
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
  color: #1d4ed8 !important;
}

/* Responsive Styles - White Theme */
@media (max-width: 768px) {
  .about-title-white {
    font-size: 2.5rem;
  }

  .about-subtitle-white {
    font-size: 1.125rem;
  }

  .story-title-white {
    font-size: 1.75rem;
  }

  .features-title-white {
    font-size: 2rem;
  }

  .cta-title-white {
    font-size: 2rem;
  }

  .story-card-white {
    padding: 1.5rem;
  }

  .mv-card-white {
    padding: 1.25rem;
    margin-bottom: 1rem;
  }

  .feature-card-white {
    padding: 2rem 1.5rem;
    margin-bottom: 1.5rem;
  }

  .cta-content-white {
    padding: 2.5rem 1.5rem;
  }

  .cta-buttons-white {
    flex-direction: column;
    align-items: center;
  }

  .btn-cta-primary-white,
  .btn-cta-secondary-white {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }

  .about-stats-grid-white .row {
    gap: 1rem;
  }

  .about-stat-card-white {
    padding: 1.5rem;
  }

  .stat-number-about-white {
    font-size: 2rem;
  }
}

@media (max-width: 576px) {
  .about-title-white {
    font-size: 2rem;
  }

  .story-title-white {
    font-size: 1.5rem;
  }

  .features-title-white {
    font-size: 1.75rem;
  }

  .cta-title-white {
    font-size: 1.75rem;
  }

  .story-card-white {
    padding: 1.25rem;
  }

  .mv-card-white {
    padding: 1rem;
  }

  .feature-card-white {
    padding: 1.5rem 1.25rem;
  }

  .cta-content-white {
    padding: 2rem 1.25rem;
  }

  .about-stat-card-white {
    padding: 1.25rem;
  }

  .stat-number-about-white {
    font-size: 1.75rem;
  }

  .btn-cta-primary-white,
  .btn-cta-secondary-white {
    padding: 1rem 2rem;
    font-size: 1rem;
  }
}

/* ===== SIMPLE ABOUT SECTION STYLES ===== */

/* Simple About Section */
.about-section-simple {
  background: #ffffff;
  position: relative;
}

/* About Content */
.about-content-simple {
  padding: 2rem 0;
}

.about-title-simple {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937 !important;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.about-subtitle-simple {
  font-size: 1.125rem;
  color: #6b7280 !important;
  max-width: 600px;
  line-height: 1.6;
}

.about-text-simple {
  font-size: 1rem;
  line-height: 1.7;
  color: #4b5563 !important;
  margin-bottom: 1.5rem;
}

/* Simple Stats */
.about-stats-simple {
  padding: 2rem 0;
}

.stat-item-simple {
  padding: 1.5rem;
  background: rgba(59, 130, 246, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.1);
  transition: all 0.3s ease;
}

.stat-item-simple:hover {
  background: rgba(59, 130, 246, 0.08);
  border-color: rgba(59, 130, 246, 0.2);
  transform: translateY(-2px);
}

.stat-number-simple {
  font-size: 2rem;
  font-weight: 800;
  color: #3b82f6 !important;
  margin-bottom: 0.5rem;
  line-height: 1;
}

.stat-label-simple {
  font-size: 0.875rem;
  color: #6b7280 !important;
  font-weight: 500;
}

/* Why Choose Us Section */
.why-choose-section-simple {
  margin-top: 4rem;
  padding-top: 3rem;
  border-top: 1px solid rgba(59, 130, 246, 0.1);
}

/* Feature Cards */
.feature-card-simple {
  padding: 2rem 1.5rem;
  background: #ffffff;
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
  height: 100%;
}

.feature-card-simple:hover {
  border-color: rgba(59, 130, 246, 0.2);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.1);
  transform: translateY(-3px);
}

.feature-icon-simple {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  margin: 0 auto;
  transition: all 0.3s ease;
}

.feature-card-simple:hover .feature-icon-simple {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3);
}

.feature-title-simple {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937 !important;
}

.feature-desc-simple {
  font-size: 0.95rem;
  line-height: 1.6;
  color: #6b7280 !important;
  margin: 0;
}

/* Responsive Styles - Simple About Section */
@media (max-width: 768px) {
  .about-title-simple {
    font-size: 2rem;
  }

  .about-subtitle-simple {
    font-size: 1rem;
  }

  .about-content-simple {
    padding: 1rem 0;
    margin-bottom: 2rem;
  }

  .about-stats-simple {
    padding: 1rem 0;
  }

  .stat-item-simple {
    padding: 1.25rem;
    margin-bottom: 1rem;
  }

  .stat-number-simple {
    font-size: 1.75rem;
  }

  .feature-card-simple {
    padding: 1.5rem 1.25rem;
    margin-bottom: 1.5rem;
  }

  .why-choose-section-simple {
    margin-top: 2rem;
    padding-top: 2rem;
  }
}

@media (max-width: 576px) {
  .about-title-simple {
    font-size: 1.75rem;
  }

  .about-text-simple {
    font-size: 0.95rem;
  }

  .stat-item-simple {
    padding: 1rem;
  }

  .stat-number-simple {
    font-size: 1.5rem;
  }

  .feature-card-simple {
    padding: 1.25rem 1rem;
  }

  .feature-icon-simple {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }

  .feature-title-simple {
    font-size: 1.125rem;
  }

  .feature-desc-simple {
    font-size: 0.9rem;
  }
}

/* ===== DETAILED FOOTER STYLES ===== */

/* Footer Main Container */
.footer-detailed {
  position: relative;
  overflow: hidden;
}

/* Footer Background with Truck on Highway Image */
.footer-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("https://images.unsplash.com/photo-1601584115197-04ecc0da31d7?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 1;
}

/* Footer Overlay */
.footer-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(15, 23, 42, 0.95) 0%,
    rgba(30, 41, 59, 0.9) 100%
  );
  z-index: 2;
}

/* Footer Content */
.footer-main {
  position: relative;
  z-index: 3;
}

/* Footer Brand */
.footer-brand {
  display: flex;
  align-items: center;
}

.footer-brand-text {
  font-size: 1.75rem;
  font-weight: 800;
  color: white;
}

/* Footer Sections */
.footer-section {
  height: 100%;
}

.footer-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: white;
  margin-bottom: 1.5rem;
  position: relative;
}

.footer-title::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 40px;
  height: 2px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.footer-description {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  font-size: 0.95rem;
}

/* Contact Info */
.footer-contact {
  margin-top: 1.5rem;
}

.contact-item {
  display: flex;
  align-items: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.contact-item:hover {
  color: #3b82f6;
}

.contact-icon {
  color: #3b82f6;
  width: 16px;
  flex-shrink: 0;
}

/* Footer Links */
.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 0.75rem;
}

.footer-links a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  position: relative;
}

.footer-links a:hover {
  color: #3b82f6;
  padding-left: 8px;
}

.footer-links a::before {
  content: "";
  position: absolute;
  left: -8px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 2px;
  background: #3b82f6;
  transition: width 0.3s ease;
}

.footer-links a:hover::before {
  width: 4px;
}

/* Newsletter Section */
.footer-newsletter-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  line-height: 1.5;
}

.newsletter-input-group {
  display: flex;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.newsletter-input-group:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.newsletter-input {
  flex: 1;
  background: transparent;
  border: none;
  padding: 0.75rem 1rem;
  color: white;
  font-size: 0.9rem;
  outline: none;
}

.newsletter-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.newsletter-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: none;
  padding: 0.75rem 1rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.newsletter-btn:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
  transform: translateX(-2px);
}

/* Social Links */
.footer-social {
  display: flex;
  gap: 1rem;
}

.social-link {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.social-link:hover {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* Footer Bottom */
.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  z-index: 3;
}

.footer-copyright {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

.footer-legal {
  display: flex;
  gap: 2rem;
  justify-content: flex-end;
}

.legal-link {
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.legal-link:hover {
  color: #3b82f6;
}

/* Footer Responsive Styles */
@media (max-width: 768px) {
  .footer-brand-text {
    font-size: 1.5rem;
  }

  .footer-title {
    font-size: 1.125rem;
    margin-bottom: 1rem;
  }

  .footer-section {
    margin-bottom: 2rem;
  }

  .footer-legal {
    justify-content: flex-start;
    gap: 1rem;
    margin-top: 1rem;
  }

  .footer-social {
    justify-content: flex-start;
  }

  .newsletter-input-group {
    flex-direction: column;
  }

  .newsletter-btn {
    border-radius: 0 0 8px 8px;
  }

  .contact-item {
    font-size: 0.85rem;
    margin-bottom: 0.75rem;
  }
}

@media (max-width: 576px) {
  .footer-main {
    padding: 3rem 0;
  }

  .footer-brand {
    justify-content: center;
    text-align: center;
    margin-bottom: 1rem;
  }

  .footer-description {
    text-align: center;
    font-size: 0.9rem;
  }

  .footer-contact {
    text-align: center;
  }

  .footer-title {
    text-align: center;
    font-size: 1rem;
  }

  .footer-title::after {
    left: 50%;
    transform: translateX(-50%);
  }

  .footer-links {
    text-align: center;
  }

  .footer-newsletter-text {
    text-align: center;
    font-size: 0.85rem;
  }

  .footer-social {
    justify-content: center;
  }

  .social-link {
    width: 36px;
    height: 36px;
    font-size: 0.9rem;
  }

  .footer-legal {
    justify-content: center;
    flex-wrap: wrap;
    text-align: center;
  }

  .footer-copyright {
    text-align: center;
    font-size: 0.85rem;
  }
}

/* ===== TRENDY CONTACT SECTION STYLES ===== */

/* Contact Section Main */
.contact-section-trendy {
  background: #ffffff;
  position: relative;
}

/* Contact Badge */
.contact-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.08) 0%,
    rgba(59, 130, 246, 0.12) 100%
  );
  border: 2px solid rgba(59, 130, 246, 0.15);
  border-radius: 50px;
  color: #3b82f6;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.contact-badge:hover {
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.12) 0%,
    rgba(59, 130, 246, 0.18) 100%
  );
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

/* Contact Title */
.contact-title {
  font-size: 3rem;
  font-weight: 800;
  line-height: 1.2;
  color: #1f2937 !important;
  margin-bottom: 1.5rem;
}

.text-gradient-contact {
  background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 50%, #10b981 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.contact-subtitle {
  font-size: 1.125rem;
  line-height: 1.6;
  color: #6b7280;
  max-width: 600px;
}

/* Contact Form Card */
.contact-form-card {
  background: #ffffff;
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 20px;
  padding: 2.5rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.contact-form-card:hover {
  border-color: rgba(59, 130, 246, 0.2);
  box-shadow: 0 15px 50px rgba(59, 130, 246, 0.08);
}

.form-header {
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  padding-bottom: 1.5rem;
}

.landig-form-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937 !important;
  margin-bottom: 0.5rem;
}

.form-subtitle {
  color: #6b7280;
  font-size: 0.95rem;
  margin: 0;
}

/* Form Groups */
.form-group-trendy {
  margin-bottom: 1.5rem;
}

.form-label-trendy {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.label-icon {
  color: #3b82f6;
  margin-right: 0.5rem;
  font-size: 0.875rem;
}

.form-input-trendy,
.form-textarea-trendy {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 2px solid rgba(59, 130, 246, 0.1);
  border-radius: 12px;
  font-size: 0.95rem;
  color: #1f2937;
  background: #ffffff;
  transition: all 0.3s ease;
  outline: none;
}

.form-input-trendy:focus,
.form-textarea-trendy:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input-trendy::placeholder,
.form-textarea-trendy::placeholder {
  color: #9ca3af;
}

.form-textarea-trendy {
  resize: vertical;
  min-height: 120px;
}

/* Form Actions */
.form-actions {
  border-top: 1px solid rgba(59, 130, 246, 0.1);
  padding-top: 1.5rem;
}

.btn-contact-primary {
  display: inline-flex;
  align-items: center;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
  cursor: pointer;
}

.btn-contact-primary:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
  color: white;
}

.form-note {
  font-size: 0.8rem;
  color: #9ca3af;
  margin: 0;
}

/* Contact Info Card */
.contact-info-card {
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.03) 0%,
    rgba(59, 130, 246, 0.06) 100%
  );
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 20px;
  padding: 2rem;
  height: fit-content;
  position: sticky;
  top: 2rem;
}

.info-header {
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  padding-bottom: 1.5rem;
}

.info-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937 !important;
  margin-bottom: 0.5rem;
}

.info-subtitle {
  color: #6b7280;
  font-size: 0.9rem;
  margin: 0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.125rem;
  margin-right: 1rem;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.info-item:hover .info-icon {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3);
}

.info-content {
  flex: 1;
}

.info-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937 !important;
  margin-bottom: 0.25rem;
}

.info-value {
  font-size: 0.95rem;
  color: #374151;
  margin-bottom: 0.25rem;
  line-height: 1.4;
}

.info-note {
  font-size: 0.8rem;
  color: #6b7280;
}

.quick-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937 !important;
  margin-bottom: 1rem;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.btn-action-secondary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 0.9rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
}

.btn-action-secondary:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
  transform: translateY(-1px);
  color: white;
}

.btn-action-outline {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  background: transparent;
  color: #3b82f6;
  border: 2px solid rgba(59, 130, 246, 0.2);
  border-radius: 10px;
  font-size: 0.9rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
}

.btn-action-outline:hover {
  background: rgba(59, 130, 246, 0.05);
  border-color: rgba(59, 130, 246, 0.3);
  color: #1d4ed8;
  transform: translateY(-1px);
}

/* Contact Section Responsive Styles */
@media (max-width: 992px) {
  .contact-info-card {
    position: static;
    margin-top: 2rem;
  }
}

@media (max-width: 768px) {
  .contact-title {
    font-size: 2.25rem;
  }

  .contact-subtitle {
    font-size: 1rem;
  }

  .contact-form-card {
    padding: 2rem 1.5rem;
  }

  .contact-info-card {
    padding: 1.5rem;
  }

  .info-item {
    padding: 1.25rem 0;
  }

  .info-icon {
    width: 45px;
    height: 45px;
    font-size: 1rem;
  }

  .form-input-trendy,
  .form-textarea-trendy {
    padding: 0.75rem;
  }

  .btn-contact-primary {
    width: 100%;
    justify-content: center;
  }

  .action-buttons {
    gap: 0.5rem;
  }
}

@media (max-width: 576px) {
  .contact-title {
    font-size: 2rem;
  }

  .contact-form-card {
    padding: 1.5rem 1rem;
  }

  .contact-info-card {
    padding: 1.25rem;
  }

  .form-title {
    font-size: 1.25rem;
  }

  .info-title {
    font-size: 1.125rem;
  }

  .info-item {
    padding: 1rem 0;
  }

  .info-icon {
    width: 40px;
    height: 40px;
    font-size: 0.9rem;
    margin-right: 0.75rem;
  }

  .btn-contact-primary {
    padding: 0.875rem 1.5rem;
    font-size: 0.95rem;
  }

  .btn-action-secondary,
  .btn-action-outline {
    padding: 0.625rem 1.25rem;
    font-size: 0.85rem;
  }
}

/* Responsive Styles - White Theme */
@media (max-width: 768px) {
  .about-title-white {
    font-size: 2.5rem;
  }

  .about-subtitle-white {
    font-size: 1.125rem;
  }

  .story-title-white {
    font-size: 1.75rem;
  }

  .features-title-white {
    font-size: 2rem;
  }

  .cta-title-white {
    font-size: 2rem;
  }

  .story-card-white {
    padding: 1.5rem;
  }

  .mv-card-white {
    padding: 1.25rem;
    margin-bottom: 1rem;
  }

  .feature-card-white {
    padding: 2rem 1.5rem;
    margin-bottom: 1.5rem;
  }

  .cta-content-white {
    padding: 2.5rem 1.5rem;
  }

  .cta-buttons-white {
    flex-direction: column;
    align-items: center;
  }

  .btn-cta-primary-white,
  .btn-cta-secondary-white {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }

  .about-stats-grid-white .row {
    gap: 1rem;
  }

  .about-stat-card-white {
    padding: 1.5rem;
  }

  .stat-number-about-white {
    font-size: 2rem;
  }
}

@media (max-width: 576px) {
  .about-title-white {
    font-size: 2rem;
  }

  .story-title-white {
    font-size: 1.5rem;
  }

  .features-title-white {
    font-size: 1.75rem;
  }

  .cta-title-white {
    font-size: 1.75rem;
  }

  .story-card-white {
    padding: 1.25rem;
  }

  .mv-card-white {
    padding: 1rem;
  }

  .feature-card-white {
    padding: 1.5rem 1.25rem;
  }

  .cta-content-white {
    padding: 2rem 1.25rem;
  }

  .about-stat-card-white {
    padding: 1.25rem;
  }

  .stat-number-about-white {
    font-size: 1.75rem;
  }

  .btn-cta-primary-white,
  .btn-cta-secondary-white {
    padding: 1rem 2rem;
    font-size: 1rem;
  }
}

/* Responsive Styles */
@media (max-width: 992px) {
  .automotive-hero-title {
    font-size: 3.5rem;
  }

  .about-main-title {
    font-size: 2.5rem;
  }

  .mission-vision-cards {
    gap: 0.75rem;
  }

  .mv-card {
    padding: 1.25rem;
  }
}

@media (max-width: 768px) {
  .automotive-hero-title {
    font-size: 2.8rem;
  }

  .automotive-hero-subtitle {
    font-size: 1.25rem;
  }

  .automotive-badge {
    padding: 0.75rem 1.5rem;
    font-size: 0.75rem;
  }

  .about-main-title {
    font-size: 2rem;
  }

  .about-subtitle {
    font-size: 1.125rem;
  }

  .story-title {
    font-size: 1.5rem;
  }

  .features-title {
    font-size: 1.5rem;
  }

  .mission-vision-cards {
    gap: 0.5rem;
  }

  .mv-card {
    padding: 1rem;
    flex-direction: column;
    text-align: center;
  }

  .feature-item-clean {
    flex-direction: column;
    text-align: center;
  }

  .feature-item-clean:hover {
    transform: translateY(-5px);
  }
}

@media (max-width: 576px) {
  .automotive-hero-title {
    font-size: 2.2rem;
  }

  .automotive-hero-subtitle {
    font-size: 1.125rem;
  }

  .automotive-badge {
    padding: 0.5rem 1rem;
    font-size: 0.7rem;
  }

  .btn-automotive-primary {
    padding: 1rem 2rem;
    font-size: 1rem;
  }

  .about-main-title {
    font-size: 1.75rem;
  }

  .story-title {
    font-size: 1.25rem;
  }
}

/* Subscription Section Styles */
.subscription-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 50px;
  color: #3b82f6;
  font-size: 0.875rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.subscription-badge:hover {
  background: rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
}

.subscription-card {
  background: white;
  border-radius: 24px;
  padding: 2rem;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 2px solid transparent;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.subscription-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.popular-card {
  border-color: #3b82f6;
  transform: scale(1.05);
}

.popular-card:hover {
  transform: scale(1.05) translateY(-10px);
}

.popular-badge {
  position: absolute;
  top: -1px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  padding: 0.5rem 1.5rem;
  border-radius: 0 0 12px 12px;
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.card-header {
  text-align: center;
  margin-bottom: 2rem;
}

.plan-icon {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.professional-icon {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.enterprise-icon {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.plan-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.plan-description {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0;
}

.card-pricing {
  text-align: center;
  margin-bottom: 2rem;
}

.price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 0.25rem;
  margin-bottom: 0.5rem;
}

.currency {
  font-size: 1.25rem;
  font-weight: 600;
  color: #6b7280;
}

.amount {
  font-size: 3rem;
  font-weight: 800;
  color: #1f2937;
}

.period {
  font-size: 1rem;
  color: #6b7280;
}

.price-note {
  font-size: 0.75rem;
  color: #9ca3af;
}

.card-features {
  flex-grow: 1;
  margin-bottom: 2rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.feature-item:last-child {
  border-bottom: none;
}

.feature-icon {
  color: #10b981;
  font-size: 1rem;
  flex-shrink: 0;
}

.feature-item span {
  color: #4b5563;
  font-size: 0.875rem;
}

.subscription-btn {
  width: 100%;
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  cursor: pointer;
  margin-top: auto;
}

.starter-btn {
  background: #f3f4f6;
  color: #374151;
}

.starter-btn:hover {
  background: #e5e7eb;
  transform: translateY(-2px);
}

.professional-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.professional-btn:hover {
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
  transform: translateY(-2px);
}

.enterprise-btn {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.enterprise-btn:hover {
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
  transform: translateY(-2px);
}

/* Testimonials Section Styles */
.testimonial-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 50px;
  color: #3b82f6 !important;
  font-size: 0.875rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.testimonial-badge:hover {
  background: rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
}

/* Small Testimonial Cards Styles */
.testimonial-card-small {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(20px);
  transition: all 0.3s ease;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.testimonial-card-small:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-8px);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.testimonial-card-small::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;

  opacity: 0;
  transition: opacity 0.3s ease;
}

.testimonial-card-small:hover::before {
  opacity: 1;
}

.quote-icon-small {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(59, 130, 246, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #3b82f6;
  font-size: 1rem;
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
}

.testimonial-card-small:hover .quote-icon-small {
  background: rgba(59, 130, 246, 0.25);
  transform: scale(1.1);
}

.testimonial-quote-small {
  font-size: 1rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9) !important;
  font-style: italic;
  margin-bottom: 1.5rem;
  font-weight: 300;
  min-height: 80px;
}

.rating-small {
  display: flex;
  gap: 0.25rem;
  justify-content: flex-start;
}

.star-small {
  color: #fbbf24 !important;
  font-size: 0.875rem;
}

.author-section-small {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.author-image-small {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid rgba(59, 130, 246, 0.2);
  flex-shrink: 0;
}

.author-avatar-small {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.author-details-small {
  flex: 1;
}

.author-name-small {
  font-size: 1rem;
  font-weight: 600;
  color: white !important;
  margin-bottom: 0.25rem;
}

.author-title-small {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7) !important;
  margin-bottom: 0.25rem;
}

.author-company-small {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5) !important;
  margin-bottom: 0;
}

.stats-small {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  margin-bottom: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-small {
  text-align: center;
  flex: 1;
}

.stat-value-small {
  font-size: 1rem;
  font-weight: 700;
  color: #3b82f6 !important;
  display: block;
  margin-bottom: 0.25rem;
}

.stat-label-small {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6) !important;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.business-type-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6 !important;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.testimonial-carousel {
  position: relative;
}

.testimonial-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 3rem;
  backdrop-filter: blur(20px);
  transition: all 0.3s ease;
}

.testimonial-card:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-5px);
}

.quote-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(59, 130, 246, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #3b82f6;
  font-size: 1.5rem;
  margin: 0 auto 2rem;
}

.testimonial-quote {
  font-size: 1.5rem;
  line-height: 1.6;
  color: white !important;
  text-align: center;
  font-style: italic;
  margin-bottom: 2rem;
  font-weight: 300;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
  justify-content: center;
}

.author-image {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid rgba(59, 130, 246, 0.3);
}

.author-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.author-info {
  text-align: left;
}

.author-name {
  font-size: 1.25rem;
  font-weight: 700;
  color: white !important;
  margin-bottom: 0.25rem;
}

.author-title {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8) !important;
  margin-bottom: 0.25rem;
}

.author-company {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6) !important;
  margin-bottom: 0.5rem;
}

.author-rating {
  display: flex;
  gap: 0.25rem;
}

.star-filled {
  color: #fbbf24 !important;
  font-size: 1rem;
}

.testimonial-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1.5rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: #3b82f6 !important;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6) !important;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.testimonial-navigation {
  display: flex;
  justify-content: center;
  gap: 0.75rem;
  margin-top: 2rem;
}

.nav-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-dot.active {
  background: #3b82f6;
  transform: scale(1.2);
}

.nav-dot:hover {
  background: rgba(59, 130, 246, 0.7);
}

/* Client Logos */
.client-logos {
  margin-top: 4rem;
}

.logos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  align-items: center;
}

.logo-item {
  display: flex;
  justify-content: center;
}

.logo-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.4);
  transition: all 0.3s ease;
}

.logo-placeholder:hover {
  color: rgba(255, 255, 255, 0.7);
  transform: translateY(-2px);
}

.logo-placeholder span {
  font-size: 0.875rem;
  font-weight: 500;
}

/* FAQ Section Styles */
.faq-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 50px;
  color: #3b82f6 !important;
  font-size: 0.875rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.faq-badge:hover {
  background: rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
}

.simple-faq-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.simple-faq-item {
  border: 1px solid #e5e7eb;
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.simple-faq-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1);
}

.faq-question-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.faq-question-header:hover {
  background: rgba(59, 130, 246, 0.02);
}

.faq-question-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.faq-icon-simple {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: rgba(59, 130, 246, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #3b82f6;
  font-size: 1rem;
  flex-shrink: 0;
}

.faq-question-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  line-height: 1.4;
}

.faq-toggle-simple {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(59, 130, 246, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #3b82f6;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.faq-toggle-simple:hover {
  background: rgba(59, 130, 246, 0.2);
  transform: scale(1.1);
}

.faq-answer-simple {
  border-top: 1px solid #f3f4f6;
  animation: fadeInDown 0.3s ease;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.faq-answer-content-simple {
  padding: 1.5rem;
  padding-top: 1rem;
}

.faq-answer-content-simple p {
  color: #4b5563 !important;
  line-height: 1.6;
  margin: 0;
  font-size: 1rem;
}

.simple-faq-cta {
  background: rgba(59, 130, 246, 0.05);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 20px;
  padding: 2.5rem;
  text-align: center;
}

.simple-faq-cta h5 {
  font-size: 1.5rem;
}

.simple-faq-cta .btn {
  border-radius: 12px;
  padding: 0.75rem 2rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.simple-faq-cta .btn:hover {
  transform: translateY(-2px);
}

/* Enhanced Services Section Styles */
.services-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 50px;
  color: #3b82f6 !important;
  font-size: 0.875rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.services-badge:hover {
  background: rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
}

/* Simple Services Section Styles */
.services-badge-simple {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1.25rem;
  background: rgba(59, 130, 246, 0.08);
  border-radius: 25px;
  color: #3b82f6 !important;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.services-badge-simple:hover {
  background: rgba(59, 130, 246, 0.12);
  transform: translateY(-1px);
}

.service-card-simple {
  background: white;
  border-radius: 16px;
  padding: 2rem 1.5rem;
  height: 100%;
  position: relative;
  overflow: hidden;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  text-align: center;
}

.service-card-simple:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border-color: #d1d5db;
}

.service-card-simple:hover .service-hover-effect {
  opacity: 1;
  visibility: visible;
}

.service-icon-simple {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #f8fafc;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  transition: all 0.3s ease;
  border: 2px solid #f1f5f9;
}

.service-card-simple:hover .service-icon-simple {
  transform: scale(1.1);
  background: #f1f5f9;
  border-color: #e2e8f0;
}

.service-content-simple {
  flex-grow: 1;
  margin-bottom: 1.5rem;
}

.service-title-simple {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937 !important;
  margin-bottom: 0.75rem;
  line-height: 1.4;
}

.service-description-simple {
  color: #6b7280 !important;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 0;
}

.service-features-simple {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: auto;
}

.feature-item-simple {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: #4b5563 !important;
  text-align: left;
}

.feature-dot {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  flex-shrink: 0;
}

.feature-text-simple {
  color: #4b5563 !important;
  font-weight: 500;
}

.service-hover-effect {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(8px);
  border-radius: 16px;
}

.hover-content-simple {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #1f2937 !important;
  font-weight: 600;
  font-size: 0.875rem;
}

.services-cta-simple {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 20px;
  padding: 2.5rem;
  margin-top: 3rem;
}

.btn-cta-primary-simple {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 0.75rem 2rem;
  font-weight: 600;
  font-size: 0.875rem;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  text-decoration: none;
}

.btn-cta-primary-simple:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
  color: white;
}

.btn-cta-secondary-simple {
  background: transparent;
  color: #3b82f6;
  border: 2px solid #3b82f6;
  border-radius: 12px;
  padding: 0.75rem 2rem;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  text-decoration: none;
}

.btn-cta-secondary-simple:hover {
  background: #3b82f6;
  color: white;
  transform: translateY(-2px);
}

/* ===== COMPLETELY REDESIGNED SERVICES SECTION ===== */

.services-section-redesigned {
  padding: 6rem 0;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  position: relative;
  overflow: hidden;
}

.services-section-redesigned::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 20% 20%,
      rgba(59, 130, 246, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 80%,
      rgba(16, 185, 129, 0.1) 0%,
      transparent 50%
    );
  pointer-events: none;
}

/* Services Header */
.services-header {
  margin-bottom: 4rem;
}

.services-intro {
  padding-right: 2rem;
}

.services-label {
  display: inline-block;
  padding: 0.5rem 1rem;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 20px;
  color: #3b82f6;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 1rem;
}

.services-main-title {
  font-size: 3.5rem;
  font-weight: 800;
  color: white;
  line-height: 1.1;
  margin-bottom: 1.5rem;
}

.title-highlight {
  background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.services-subtitle {
  font-size: 1.125rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.6;
  margin: 0;
}

/* Services Stats */
.services-stats {
  display: flex;
  gap: 2rem;
  justify-content: flex-end;
  align-items: center;
}

.stat-item-new {
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: white;
  line-height: 1;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Services Grid */
.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 4rem;
}

.service-item-new {
  position: relative;
}

.service-card-new {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2rem;
  height: 100%;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(20px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.service-card-new:hover {
  transform: translateY(-4px);
  border-color: rgba(255, 255, 255, 0.15);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

/* Service Number */
.service-number {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.4);
  font-family: "Courier New", monospace;
}

/* Service Icon */
.service-icon-new {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  color: var(--service-color);
  transition: transform 0.3s ease;
}

.service-card-new:hover .service-icon-new {
  transform: scale(1.05);
}

/* Service Content */
.service-info {
  margin-bottom: 2rem;
}

.service-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
  margin-bottom: 0.75rem;
  line-height: 1.3;
}

.service-desc {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
  margin-bottom: 1rem;
}

.service-features-new {
  list-style: none;
  padding: 0;
  margin: 0;
}

.service-features-new li {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 0.5rem;
  position: relative;
  padding-left: 1rem;
}

.service-features-new li::before {
  content: "→";
  position: absolute;
  left: 0;
  color: var(--service-color, #3b82f6);
  font-weight: bold;
}

/* Service Action */
.service-action {
  margin-top: auto;
}

.service-btn {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  color: white;
  font-size: 0.875rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  cursor: pointer;
  width: 100%;
  justify-content: center;
}

.service-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

/* Background Pattern - Simplified */
.service-pattern {
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, var(--service-color) 0%, transparent 70%);
  opacity: 0.02;
  pointer-events: none;
}

/* Bottom CTA */
.services-bottom-cta {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 3rem;
  backdrop-filter: blur(20px);
}

.cta-content-new {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2rem;
}

.cta-text h3 {
  font-size: 1.75rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.5rem;
}

.cta-text p {
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  font-size: 1rem;
}

.cta-actions {
  display: flex;
  gap: 1rem;
  flex-shrink: 0;
}

.cta-btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 1rem 2rem;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  cursor: pointer;
}

.cta-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.cta-btn-secondary {
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 1rem 2rem;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  cursor: pointer;
}

.cta-btn-secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .services-main-title {
    font-size: 2.5rem;
  }

  .services-stats {
    justify-content: center;
    margin-top: 2rem;
  }

  .services-intro {
    padding-right: 0;
    text-align: center;
  }
}

@media (max-width: 768px) {
  .services-section-redesigned {
    padding: 4rem 0;
  }

  .services-main-title {
    font-size: 2rem;
  }

  .services-stats {
    gap: 1rem;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .services-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .service-card-new {
    padding: 1.5rem;
  }

  .cta-content-new {
    flex-direction: column;
    text-align: center;
  }

  .cta-actions {
    flex-direction: column;
    width: 100%;
  }

  .cta-btn-primary,
  .cta-btn-secondary {
    width: 100%;
    max-width: 300px;
  }

  .services-bottom-cta {
    padding: 2rem;
  }
}

@media (max-width: 576px) {
  .services-header {
    margin-bottom: 2rem;
  }

  .services-main-title {
    font-size: 1.75rem;
  }

  .services-subtitle {
    font-size: 1rem;
  }

  .service-card-new {
    padding: 1.25rem;
  }

  .service-icon-new {
    width: 50px;
    height: 50px;
  }

  .service-name {
    font-size: 1.125rem;
  }
}

.service-card-enhanced {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  height: 100%;
  position: relative;
  overflow: hidden;
  border: 1px solid #e5e7eb;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  color: #1f2937 !important;
  transform: translateY(0);
}

.service-card-enhanced:hover {
  transform: translateY(-15px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  border-color: transparent;
}

.service-card-enhanced:hover .service-overlay {
  opacity: 1;
  visibility: visible;
}

/* Ensure all text in service cards is dark */
.service-card-enhanced * {
  color: inherit !important;
}

/* Enhanced smooth transitions for service elements */
.service-title {
  transition: color 0.3s ease;
}

.service-description {
  transition: color 0.3s ease;
}

.feature-tag {
  transition: all 0.3s ease;
}

.service-card-enhanced:hover .service-title {
  color: #1f2937 !important;
}

.service-card-enhanced:hover .service-description {
  color: #374151 !important;
}

.service-icon-wrapper {
  margin-bottom: 1.5rem;
}

.service-icon {
  width: 70px;
  height: 70px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 1.75rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transform: scale(1);
}

.service-card-enhanced:hover .service-icon {
  transform: scale(1.15) rotate(5deg);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
}

.service-content {
  flex-grow: 1;
  text-align: center;
}

.service-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937 !important;
  margin-bottom: 1rem;
}

.service-description {
  color: #4b5563 !important;
  font-size: 0.875rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.service-features {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: auto;
}

.feature-tag {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: #374151 !important;
  text-align: left;
}

.feature-tag span {
  color: #374151 !important;
}

.feature-check {
  color: #10b981 !important;
  font-size: 0.75rem;
  flex-shrink: 0;
}

.service-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.overlay-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: #1f2937 !important;
  font-weight: 600;
}

.overlay-content h6 {
  margin: 0;
  font-size: 1rem;
  color: #1f2937 !important;
}

.overlay-content * {
  color: #1f2937 !important;
}

.services-cta {
  background: rgba(59, 130, 246, 0.05);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 24px;
  padding: 3rem;
  margin-top: 3rem;
}

.services-cta h4 {
  font-size: 1.75rem;
}

.services-cta .btn {
  font-weight: 600;
  transition: all 0.3s ease;
}

.services-cta .btn:hover {
  transform: translateY(-3px);
}

.services-cta .btn-primary:hover {
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.services-cta .btn-outline-primary:hover {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

/* Additional Services Section Text Color Fixes */
#services .service-card-enhanced h5 {
  color: #1f2937 !important;
}

#services .service-card-enhanced p {
  color: #4b5563 !important;
}

#services .service-card-enhanced span {
  color: #374151 !important;
}

#services .service-card-enhanced .feature-tag {
  color: #374151 !important;
}

#services .service-card-enhanced .feature-tag span {
  color: #374151 !important;
}

/* Services section heading colors */
#services h2 {
  color: white !important;
}

#services .lead {
  color: rgba(255, 255, 255, 0.8) !important;
}

#services .services-cta p {
  color: #6b7280 !important;
}

/* Enhanced Service Icon Styles for Better Clarity */
#services .service-icon {
  border: 2px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

#services .service-card-enhanced:hover .service-icon {
  transform: scale(1.15) rotate(5deg);
}

#services .service-card-enhanced:hover .service-icon[style*="#3b82f6"] {
  background: rgba(59, 130, 246, 0.2) !important;
  border-color: rgba(59, 130, 246, 0.4) !important;
}

#services .service-card-enhanced:hover .service-icon[style*="#10b981"] {
  background: rgba(16, 185, 129, 0.2) !important;
  border-color: rgba(16, 185, 129, 0.4) !important;
}

#services .service-card-enhanced:hover .service-icon[style*="#f59e0b"] {
  background: rgba(245, 158, 11, 0.2) !important;
  border-color: rgba(245, 158, 11, 0.4) !important;
}

#services .service-card-enhanced:hover .service-icon[style*="#ef4444"] {
  background: rgba(239, 68, 68, 0.2) !important;
  border-color: rgba(239, 68, 68, 0.4) !important;
}

#services .service-card-enhanced:hover .service-icon[style*="#8b5cf6"] {
  background: rgba(139, 92, 246, 0.2) !important;
  border-color: rgba(139, 92, 246, 0.4) !important;
}

#services .service-card-enhanced:hover .service-icon[style*="#06b6d4"] {
  background: rgba(6, 182, 212, 0.2) !important;
  border-color: rgba(6, 182, 212, 0.4) !important;
}

#services .service-card-enhanced:hover .service-icon[style*="#f97316"] {
  background: rgba(249, 115, 22, 0.2) !important;
  border-color: rgba(249, 115, 22, 0.4) !important;
}

#services .service-card-enhanced:hover .service-icon[style*="#84cc16"] {
  background: rgba(132, 204, 22, 0.2) !important;
  border-color: rgba(132, 204, 22, 0.4) !important;
}

/* Make icons dark for better visibility on white cards */
#services .service-icon[style*="#3b82f6"] {
  background: rgba(59, 130, 246, 0.1) !important;
  color: #1d4ed8 !important;
  border: 2px solid rgba(59, 130, 246, 0.2) !important;
}

#services .service-icon[style*="#10b981"] {
  background: rgba(16, 185, 129, 0.1) !important;
  color: #059669 !important;
  border: 2px solid rgba(16, 185, 129, 0.2) !important;
}

#services .service-icon[style*="#f59e0b"] {
  background: rgba(245, 158, 11, 0.1) !important;
  color: #d97706 !important;
  border: 2px solid rgba(245, 158, 11, 0.2) !important;
}

#services .service-icon[style*="#ef4444"] {
  background: rgba(239, 68, 68, 0.1) !important;
  color: #dc2626 !important;
  border: 2px solid rgba(239, 68, 68, 0.2) !important;
}

#services .service-icon[style*="#8b5cf6"] {
  background: rgba(139, 92, 246, 0.1) !important;
  color: #7c3aed !important;
  border: 2px solid rgba(139, 92, 246, 0.2) !important;
}

#services .service-icon[style*="#06b6d4"] {
  background: rgba(6, 182, 212, 0.1) !important;
  color: #0891b2 !important;
  border: 2px solid rgba(6, 182, 212, 0.2) !important;
}

#services .service-icon[style*="#f97316"] {
  background: rgba(249, 115, 22, 0.1) !important;
  color: #ea580c !important;
  border: 2px solid rgba(249, 115, 22, 0.2) !important;
}

#services .service-icon[style*="#84cc16"] {
  background: rgba(132, 204, 22, 0.1) !important;
  color: #65a30d !important;
  border: 2px solid rgba(132, 204, 22, 0.2) !important;
}

/* Additional icon clarity enhancements */
#services .service-icon svg,
#services .service-icon i {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  font-weight: 600;
}

#services .service-card-enhanced:hover .service-icon svg,
#services .service-card-enhanced:hover .service-icon i {
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

/* Ensure icon wrapper has proper styling */
.service-icon-wrapper {
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: center;
}

/* Make sure React Icons are properly sized */
#services .service-icon > * {
  width: 28px !important;
  height: 28px !important;
  font-size: 28px !important;
}

/* How It Works Section Text Color Fixes */
#how-it-works {
  background: white;
}

#how-it-works h2 {
  color: #1f2937 !important;
}

#how-it-works .lead {
  color: #4b5563 !important;
}

#how-it-works .step-title {
  color: #1f2937 !important;
  text-shadow: none !important;
}

#how-it-works .step-description {
  color: #4b5563 !important;
  text-shadow: none !important;
}

#how-it-works .step-number {
  background: #3b82f6 !important;
  color: white !important;
}

/* Ensure all text in How It Works is dark */
#how-it-works * {
  text-shadow: none !important;
}

#how-it-works h1,
#how-it-works h2,
#how-it-works h3,
#how-it-works h4,
#how-it-works h5,
#how-it-works h6 {
  color: #1f2937 !important;
}

/* FAQ Section Text Color Fixes */
#faq {
  background: white;
}

#faq h2 {
  color: #1f2937 !important;
}

#faq .lead {
  color: #4b5563 !important;
}

#faq .faq-badge {
  color: #3b82f6 !important;
}

#faq .faq-question-title {
  color: #1f2937 !important;
}

#faq .faq-answer-content-simple p {
  color: #4b5563 !important;
}

/* Ensure all FAQ text is properly colored */
#faq h1,
#faq h2,
#faq h3,
#faq h4,
#faq h5,
#faq h6 {
  color: #1f2937 !important;
}

#faq p,
#faq span,
#faq div:not(.faq-toggle-simple):not(.faq-icon-simple) {
  color: #4b5563 !important;
}

#faq .accordion-button {
  color: #1f2937 !important;
}

#faq .accordion-body {
  color: #4b5563 !important;
}

#faq .card-header {
  color: #1f2937 !important;
}

#faq .card-body {
  color: #4b5563 !important;
}

/* Services Section Intro Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Services Section Animation Classes */
#services {
  overflow: hidden;
}

#services h2 {
  animation: fadeInUp 0.8s ease-out;
  animation-fill-mode: both;
}

#services .lead {
  animation: fadeInUp 0.8s ease-out 0.2s;
  animation-fill-mode: both;
}

#services .service-card-enhanced {
  animation: slideInFromBottom 0.6s ease-out;
  animation-fill-mode: both;
}

#services .service-card-enhanced:nth-child(1) {
  animation-delay: 0.1s;
}

#services .service-card-enhanced:nth-child(2) {
  animation-delay: 0.2s;
}

#services .service-card-enhanced:nth-child(3) {
  animation-delay: 0.3s;
}

#services .service-card-enhanced:nth-child(4) {
  animation-delay: 0.4s;
}

#services .service-card-enhanced:nth-child(5) {
  animation-delay: 0.5s;
}

#services .service-card-enhanced:nth-child(6) {
  animation-delay: 0.6s;
}

#services .service-card-enhanced:nth-child(7) {
  animation-delay: 0.7s;
}

#services .service-card-enhanced:nth-child(8) {
  animation-delay: 0.8s;
}

#services .service-icon {
  animation: scaleIn 0.5s ease-out;
  animation-fill-mode: both;
}

#services .service-card-enhanced:nth-child(1) .service-icon {
  animation-delay: 0.3s;
}

#services .service-card-enhanced:nth-child(2) .service-icon {
  animation-delay: 0.4s;
}

#services .service-card-enhanced:nth-child(3) .service-icon {
  animation-delay: 0.5s;
}

#services .service-card-enhanced:nth-child(4) .service-icon {
  animation-delay: 0.6s;
}

#services .service-card-enhanced:nth-child(5) .service-icon {
  animation-delay: 0.7s;
}

#services .service-card-enhanced:nth-child(6) .service-icon {
  animation-delay: 0.8s;
}

#services .service-card-enhanced:nth-child(7) .service-icon {
  animation-delay: 0.9s;
}

#services .service-card-enhanced:nth-child(8) .service-icon {
  animation-delay: 1s;
}

#services .services-cta {
  animation: fadeInUp 0.8s ease-out 1.2s;
  animation-fill-mode: both;
}

/* Statistics Section Styles */
.stats-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 50px;
  color: #3b82f6 !important;
  font-size: 0.875rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.stats-badge:hover {
  background: rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
}

.stat-card-modern {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(20px);
  height: 100%;
}

.stat-card-modern:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-10px);
  border-color: rgba(59, 130, 246, 0.3);
}

.stat-icon-modern {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(59, 130, 246, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #3b82f6;
  font-size: 1.5rem;
  margin: 0 auto 1.5rem;
  transition: all 0.3s ease;
}

.stat-card-modern:hover .stat-icon-modern {
  background: rgba(59, 130, 246, 0.3);
  transform: scale(1.1);
}

.stat-number-modern {
  font-size: 3rem;
  font-weight: 800;
  color: white !important;
  line-height: 1;
  margin-bottom: 0.5rem;
}

.stat-label-modern {
  font-size: 1.125rem;
  font-weight: 600;
  color: white !important;
  margin-bottom: 0.5rem;
}

.stat-description {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7) !important;
  line-height: 1.5;
}

/* Company Values Styles */
.values-section {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.value-card-modern {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  height: 100%;
}

.value-card-modern:hover {
  background: rgba(255, 255, 255, 0.06);
  transform: translateY(-5px);
  border-color: rgba(59, 130, 246, 0.2);
}

.value-icon-modern {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  background: rgba(59, 130, 246, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #3b82f6;
  font-size: 1.25rem;
  margin: 0 auto 1rem;
  transition: all 0.3s ease;
}

.value-card-modern:hover .value-icon-modern {
  background: rgba(59, 130, 246, 0.25);
  transform: scale(1.1);
}

.value-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: white !important;
  margin-bottom: 1rem;
}

.value-description {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7) !important;
  line-height: 1.6;
  margin: 0;
}

/* Responsive adjustments for stats and values */
@media (max-width: 768px) {
  .stat-number-modern {
    font-size: 2.5rem;
  }

  .stat-card-modern {
    padding: 1.5rem;
  }

  .value-card-modern {
    padding: 1.5rem;
  }

  .values-section {
    margin-top: 3rem;
    padding-top: 3rem;
  }
}

@media (max-width: 576px) {
  .stat-number-modern {
    font-size: 2rem;
  }

  .stat-label-modern {
    font-size: 1rem;
  }

  .value-title {
    font-size: 1.125rem;
  }

  .subscription-card {
    margin-bottom: 2rem;
  }

  .popular-card {
    transform: none;
  }

  .popular-card:hover {
    transform: translateY(-10px);
  }

  .testimonial-author {
    flex-direction: column;
    text-align: center;
  }

  .author-info {
    text-align: center;
  }

  .testimonial-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .service-card-enhanced {
    margin-bottom: 1.5rem;
  }

  .feature-tag {
    justify-content: center;
    text-align: center;
  }

  /* Small Testimonial Cards - Mobile Responsive */
  .testimonial-card-small {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .author-section-small {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .author-details-small {
    text-align: center;
  }

  .stats-small {
    flex-direction: column;
    gap: 0.75rem;
  }

  .stat-small {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
  }

  .business-type-badge {
    position: static;
    margin-top: 1rem;
    text-align: center;
    display: block;
  }

  .testimonial-quote-small {
    min-height: auto;
    font-size: 0.9rem;
  }

  /* Simple Service Cards - Mobile Responsive */
  .service-card-simple {
    padding: 1.5rem 1.25rem;
    margin-bottom: 1.5rem;
  }

  .service-icon-simple {
    width: 50px;
    height: 50px;
    margin-bottom: 1rem;
  }

  .service-title-simple {
    font-size: 1rem;
  }

  .service-description-simple {
    font-size: 0.8rem;
  }

  .feature-item-simple {
    font-size: 0.7rem;
  }

  .services-cta-simple {
    padding: 2rem 1.5rem;
  }

  .btn-cta-primary-simple,
  .btn-cta-secondary-simple {
    width: 100%;
    max-width: 280px;
    justify-content: center;
    margin-bottom: 0.75rem;
  }
}

/* ===== KYC STEPPER MODAL STYLES ===== */

/* Modal Overlay - Positioned to cover only main content area, leaving sidebar and topbar visible */
.kyc-modal-overlay {
  position: fixed;
  top: 64px; /* Height of topbar (min-height: 64px) */
  left: 240px; /* Width of expanded sidebar */
  right: 0;
  bottom: 0;
  display: flex; /* Restore flex for centering */
  align-items: center; /* Center vertically */
  justify-content: center; /* Center horizontally */
  z-index: 9999;
  background: transparent; /* Remove background */
  animation: fadeInOverlay 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Adjust for collapsed sidebar - using body class to detect collapsed state */
body.sidebar-collapsed .kyc-modal-overlay {
  left: 70px; /* Width of collapsed sidebar */
}

/* Mobile responsive - full screen on mobile */
@media (max-width: 768px) {
  .kyc-modal-overlay {
    top: 60px; /* Mobile topbar height */
    left: 0;
    right: 0;
  }
}

@keyframes fadeInOverlay {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(15px);
  }
}

/* Modal Container */
.kyc-modal-container {
  background: linear-gradient(145deg, #1a2332, #0f1419);
  border-radius: 30px 0 0 0;
  width: 95%; /* Restore original width */
  max-width: 1200px; /* Restore original max-width */
  max-height: 90vh; /* Restore original max-height */
  overflow: hidden;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.6), 0 0 0 1px rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-bottom: none !important;
  border-right: none !important;
  animation: slideInModal 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

@keyframes slideInModal {
  from {
    opacity: 0;
    transform: translateY(-60px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Modal Header */
.kyc-modal-header {
  background: linear-gradient(135deg, #2a3441, #1e2832);
  padding: 2rem 2.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.kyc-modal-header::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
}

.kyc-modal-title {
  color: #ffffff;
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  background: linear-gradient(135deg, #ffffff, #e2e8f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.kyc-modal-close {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #ffffff;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  font-weight: 300;
}

.kyc-modal-close:hover {
  background: rgba(255, 107, 107, 0.2);
  border-color: rgba(255, 107, 107, 0.4);
  transform: rotate(90deg) scale(1.1);
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
}

/* Cards Wrapper */
.kyc-cards-wrapper {
  display: flex;
  height: 500px;
  overflow: hidden;
}

/* Individual KYC Card */
.kyc-card {
  display: flex;
  height: 100%;
  transition: all 0.8s cubic-bezier(0.23, 1, 0.32, 1);
  position: relative;
  overflow: hidden;
  will-change: flex;
}

/* Default card state - collapsed */
.kyc-card:not(.active) {
  flex: 0 0 80px;
  cursor: pointer;
  transform: translateZ(0);
}

/* Active card state - expanded */
.kyc-card.active {
  flex: 1;
  transform: translateZ(0);
  animation: cardActivate 0.8s cubic-bezier(0.23, 1, 0.32, 1);
}

@keyframes cardActivate {
  0% {
    flex: 0 0 80px;
  }
  70% {
    flex: 1.05;
  }
  100% {
    flex: 1;
  }
}

/* Card Sidebar (Visible when collapsed) */
.kyc-card-sidebar {
  width: 80px;
  background: linear-gradient(180deg, #2a3441, #1e2832);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem 0;
  transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
  position: relative;
  will-change: background, border-color;
}

.kyc-card.completed .kyc-card-sidebar {
  background: linear-gradient(180deg, #1e3a1e, #0f2a0f);
  border-right-color: rgba(40, 167, 69, 0.3);
}

.kyc-card.active .kyc-card-sidebar {
  background: linear-gradient(180deg, #3a2a1e, #2a1e0f);
  border-right-color: rgba(255, 142, 83, 0.3);
}

.kyc-card-sidebar-icon {
  font-size: 1.5rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0.5rem;
  transition: all 0.3s ease;
}

.kyc-card.completed .kyc-card-sidebar-icon {
  color: #28a745;
}

.kyc-card.active .kyc-card-sidebar-icon {
  color: #ff8e53;
}

.kyc-card-sidebar-title {
  writing-mode: vertical-rl;
  text-orientation: mixed;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.8rem;
  font-weight: 500;
  text-align: center;
  letter-spacing: 0.1em;
  transition: all 0.3s ease;
}

.kyc-card.completed .kyc-card-sidebar-title {
  color: #20c997;
}

.kyc-card.active .kyc-card-sidebar-title {
  color: #ff8e53;
}

/* Hover effect for inactive cards */
.kyc-card:not(.active):hover .kyc-card-sidebar {
  background: linear-gradient(180deg, #3a3441, #2e2832);
}

.kyc-card:not(.active):hover .kyc-card-sidebar-icon {
  color: #ff6b6b;
  transform: scale(1.1);
}

.kyc-card:not(.active):hover .kyc-card-sidebar-title {
  color: #ff6b6b;
}

/* Main Content Panel for Active Card */
.kyc-card-main {
  flex: 1;
  background: linear-gradient(145deg, #2a3441, #1e2832);
  display: flex;
  flex-direction: column;
  opacity: 0;
  transform: translateX(40px) scale(0.95);
  transition: all 0.7s cubic-bezier(0.23, 1, 0.32, 1);
  overflow: hidden;
  will-change: opacity, transform;
}

.kyc-card-main.show {
  opacity: 1;
  transform: translateX(0) scale(1);
  transition-delay: 0.1s;
}

.kyc-card-main-header {
  padding: 2rem 2.5rem 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(0, 0, 0, 0.2);
}

.kyc-card-main-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #ff6b6b, #ff8e53);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
}

.kyc-card-main-title {
  color: #ffffff;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.kyc-card-main-content {
  flex: 1;
  padding: 2rem 2.5rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

/* Step Fields */
.kyc-step-fields {
  flex: 1;
  margin-bottom: 2rem;
}

.kyc-step-fields .mb-3 {
  opacity: 0;
  transform: translateY(20px);
  animation: fieldSlideIn 0.6s cubic-bezier(0.23, 1, 0.32, 1) forwards;
}

.kyc-step-fields .mb-3:nth-child(1) {
  animation-delay: 0.1s;
}
.kyc-step-fields .mb-3:nth-child(2) {
  animation-delay: 0.2s;
}
.kyc-step-fields .mb-3:nth-child(3) {
  animation-delay: 0.3s;
}
.kyc-step-fields .mb-3:nth-child(4) {
  animation-delay: 0.4s;
}
.kyc-step-fields .mb-3:nth-child(5) {
  animation-delay: 0.5s;
}
.kyc-step-fields .mb-3:nth-child(6) {
  animation-delay: 0.6s;
}

@keyframes fieldSlideIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.kyc-step-fields .form-label {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  letter-spacing: 0.5px;
}

.kyc-step-fields .form-control,
.kyc-step-fields .form-select {
  background: rgba(255, 255, 255, 0.08);
  border: 2px solid rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  color: #ffffff;
  padding: 0.875rem 1rem;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  font-size: 0.95rem;
}

.kyc-step-fields .form-control:focus,
.kyc-step-fields .form-select:focus {
  background: rgba(255, 255, 255, 0.12);
  border-color: #ff8e53;
  box-shadow: 0 0 0 3px rgba(255, 142, 83, 0.2),
    0 8px 25px rgba(255, 107, 107, 0.15);
  color: #ffffff;
  outline: none;
  transform: translateY(-1px);
}

.kyc-step-fields .form-control::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.kyc-step-fields textarea.form-control {
  resize: vertical;
  min-height: 100px;
}

/* File Input Styling */
.kyc-step-fields input[type="file"] {
  background: rgba(255, 255, 255, 0.05);
  border: 2px dashed rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.kyc-step-fields input[type="file"]:hover {
  border-color: #ff8e53;
  background: rgba(255, 142, 83, 0.1);
}

.kyc-step-fields input[type="file"]:focus {
  border-color: #ff6b6b;
  box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.2);
  outline: none;
}

/* Error Messages */
.kyc-step-fields .text-danger {
  color: #ff6b6b !important;
  font-size: 0.8rem;
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  animation: errorSlideIn 0.3s ease-out;
}

@keyframes errorSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Card Actions */
.kyc-card-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: auto;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.kyc-card-actions .btn {
  border-radius: 12px;
  padding: 0.875rem 2rem;
  font-weight: 600;
  font-size: 0.95rem;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border-width: 2px;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
}

.kyc-card-actions .btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.kyc-card-actions .btn:hover::before {
  left: 100%;
}

.kyc-btn-next {
  background: linear-gradient(135deg, #ff6b6b, #ff8e53);
  border-color: transparent;
  color: white;
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
}

.kyc-btn-next:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(255, 107, 107, 0.5);
  background: linear-gradient(135deg, #ff5252, #ff7043);
}

.kyc-btn-next:disabled {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.kyc-btn-previous {
  background: transparent;
  border-color: rgba(255, 255, 255, 0.3);
  color: rgba(255, 255, 255, 0.8);
}

.kyc-btn-previous:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  color: white;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .kyc-modal-container {
    width: 98%;
    max-height: 95vh;
    border-radius: 20px 0 0 0;
  }

  .kyc-modal-header {
    padding: 1.5rem 1.5rem;
  }

  .kyc-modal-title {
    font-size: 1.5rem;
  }

  .kyc-cards-wrapper {
    flex-direction: column;
    height: auto;
    max-height: 60vh;
  }

  .kyc-card:not(.active) {
    flex: none;
    height: 60px;
    width: 100%;
  }

  .kyc-card.active {
    flex: 1;
    min-height: 400px;
  }

  .kyc-card-sidebar {
    width: 100%;
    height: 60px;
    flex-direction: row;
    justify-content: flex-start;
    padding: 0 1.5rem;
    border-right: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .kyc-card-sidebar-icon {
    margin-bottom: 0;
    margin-right: 1rem;
    font-size: 1.3rem;
  }

  .kyc-card-sidebar-title {
    writing-mode: initial;
    text-orientation: initial;
    font-size: 0.9rem;
  }

  .kyc-card-main-header {
    padding: 1.5rem 1.5rem 1rem;
  }

  .kyc-card-main-content {
    padding: 1.5rem;
  }

  .kyc-card-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .kyc-card-actions .btn {
    width: 100%;
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .kyc-modal-header {
    padding: 1rem;
  }

  .kyc-modal-title {
    font-size: 1.25rem;
  }

  .kyc-card-main-header {
    padding: 1rem;
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .kyc-card-main-icon {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }

  .kyc-card-main-title {
    font-size: 1.25rem;
  }

  .kyc-card-main-content {
    padding: 1rem;
  }

  .kyc-step-fields .form-control,
  .kyc-step-fields .form-select {
    padding: 0.75rem;
    font-size: 0.9rem;
  }
}

/* Loading and Animation States */
.kyc-modal-container.loading {
  pointer-events: none;
}

.kyc-modal-container.loading::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(2px);
  z-index: 10;
}

/* Success State */
.kyc-card.success .kyc-card-main {
  background: linear-gradient(145deg, #1e3a1e, #0f2a0f);
}

.kyc-card.success .kyc-card-main-icon {
  background: linear-gradient(135deg, #28a745, #20c997);
}

/* Accessibility Improvements */
.kyc-modal-overlay:focus-within .kyc-modal-container {
  outline: 2px solid rgba(255, 142, 83, 0.5);
  outline-offset: 4px;
}

.kyc-card-actions .btn:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

.kyc-step-fields .form-control:focus,
.kyc-step-fields .form-select:focus {
  outline: none;
}

/* Smooth scrolling for modal content */
.kyc-card-main-content {
  scroll-behavior: smooth;
}

.kyc-card-main-content::-webkit-scrollbar {
  width: 6px;
}

.kyc-card-main-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.kyc-card-main-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.kyc-card-main-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* ===== MODERN ACTION POPUP STYLES ===== */
/* Modern Action Popup Styles */
.modern-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 20px;
  animation: fadeIn 0.3s ease-out;
}

/* Light Theme */
.modern-popup-container {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 24px;
  max-width: 500px;
  width: 100%;
  position: relative;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  animation: slideUp 0.4s ease-out;
}

/* Dark Theme */
[data-theme="dark"] .modern-popup-container {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
}

.modern-popup-close {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: all 0.2s ease;
  color: #6c757d;
}

.modern-popup-close:hover {
  background: rgba(255, 255, 255, 1);
  color: #495057;
  transform: scale(1.1);
}

/* Dark Theme Close Button */
[data-theme="dark"] .modern-popup-close {
  background: rgba(52, 73, 94, 0.9);
  color: #bdc3c7;
}

[data-theme="dark"] .modern-popup-close:hover {
  background: rgba(52, 73, 94, 1);
  color: #ecf0f1;
}

.modern-popup-content {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 350px;
  padding: 50px 30px 30px;
}

.modern-popup-text {
  text-align: center;
  max-width: 400px;
  width: 100%;
}

.modern-popup-title {
  font-size: 2.5rem;
  font-weight: 900;
  color: #212529;
  margin: 0 0 16px 0;
  line-height: 1.1;
  letter-spacing: -0.02em;
}

.modern-popup-subtitle {
  font-size: 1.25rem;
  color: #495057;
  margin: 0 0 24px 0;
  font-weight: 500;
}

.modern-popup-description {
  font-size: 1rem;
  color: #6c757d;
  line-height: 1.6;
  margin: 0 0 32px 0;
}

/* Dark Theme Text */
[data-theme="dark"] .modern-popup-title {
  color: #ecf0f1;
}

[data-theme="dark"] .modern-popup-subtitle {
  color: #bdc3c7;
}

[data-theme="dark"] .modern-popup-description {
  color: #95a5a6;
}

.modern-popup-button {
  background: #212529;
  color: white;
  border: none;
  border-radius: 12px;
  padding: 16px 32px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  text-decoration: none;
}

.modern-popup-button:hover {
  background: #495057;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(33, 37, 41, 0.3);
}

/* Dark Theme Button */
[data-theme="dark"] .modern-popup-button {
  background: #3498db;
  color: #ffffff;
}

[data-theme="dark"] .modern-popup-button:hover {
  background: #2980b9;
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-popup-container {
    margin: 20px;
    border-radius: 16px;
    max-width: 400px;
  }

  .modern-popup-content {
    padding: 30px 20px 20px;
    min-height: 300px;
  }

  .modern-popup-title {
    font-size: 2rem;
  }
}

/* ===== PROFILE COMPLETION PAGE STYLES ===== */
.profile-completion-page {
  min-height: 100%;
  background: var(--bg-secondary);
}

/* Ensure proper spacing within main layout */
.profile-completion-page .container-fluid {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

/* Dark theme support for profile completion */
[data-theme="dark"] .profile-completion-page {
  background: var(--bg-secondary);
}

/* ===== PROFILE KYC GUARD STYLES ===== */
.profile-kyc-guard-restriction {
  min-height: 100%;
  background: var(--bg-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 0;
}

/* Dark theme support for ProfileKYCGuard */
[data-theme="dark"] .profile-kyc-guard-restriction {
  background: var(--bg-secondary);
}

/* ===== CLEAN PROFILE KYC STYLES ===== */
.clean-profile-kyc-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 2rem 0 0 0;
}

/* Clean Header Section */
.clean-kyc-header {
  padding: 0 0 2rem 0;
  border-bottom: 1px solid var(--border-light);
  margin-bottom: 2rem;
}

.clean-kyc-title {
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  text-align: left;
}

.clean-kyc-subtitle {
  font-size: 1rem;
  color: var(--text-secondary);
  margin-bottom: 0;
  text-align: left;
}

/* Clean Content Layout */
.clean-kyc-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0;
  overflow: hidden;
}

/* Clean Stepper Navigation */
.clean-stepper-nav {
  padding: 0 1rem 0 0;
}

.clean-stepper-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.clean-stepper-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  cursor: default;
  transition: all 0.2s ease;
  border-radius: 8px;
  border: 1px solid var(--border-light);
}

.clean-stepper-item.clickable {
  cursor: pointer;
}

.clean-stepper-item.clickable:hover {
  border-color: var(--primary);
  transform: translateX(2px);
}

.clean-stepper-item.active {
  border-color: var(--primary);
  background: rgba(var(--primary-rgb), 0.05);
}

.clean-stepper-item.completed {
  border-color: #28a745;
  background: rgba(40, 167, 69, 0.08);
  border-left: 5px solid #28a745;
  padding-left: 0.75rem;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.15);
}

.clean-stepper-item.valid {
  border-color: #28a745;
  background: rgba(40, 167, 69, 0.05);
  border-left: 5px solid #28a745;
  padding-left: 0.75rem;
  box-shadow: 0 1px 4px rgba(40, 167, 69, 0.1);
}

.clean-stepper-item.invalid {
  border-color: #dc3545;
  background: rgba(220, 53, 69, 0.05);
  border-left: 5px solid #dc3545;
  padding-left: 0.75rem;
  box-shadow: 0 1px 4px rgba(220, 53, 69, 0.1);
}

.clean-stepper-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
  margin-right: 1rem;
  flex-shrink: 0;
  border: 2px solid var(--border-light);
  color: var(--text-secondary);
  transition: all 0.2s ease;
}

.clean-stepper-item.active .clean-stepper-number {
  background: var(--primary);
  border-color: var(--primary);
  color: white;
}

.clean-stepper-item.completed .clean-stepper-number {
  background: #28a745;
  border-color: #28a745;
  color: white;
}

.clean-stepper-item.invalid .clean-stepper-number {
  background: #dc3545;
  border-color: #dc3545;
  color: white;
  font-weight: 700;
}

.clean-stepper-text {
  flex: 1;
  min-width: 0;
}

.clean-stepper-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.25rem;
}

.clean-stepper-title {
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.3;
}

.clean-stepper-item.active .clean-stepper-title {
  color: var(--primary);
}

.clean-stepper-item.completed .clean-stepper-title {
  color: var(--text-primary);
}

/* Clean Form Container */
.clean-form-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.clean-form {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  padding: 0 2rem;
  max-width: none;
  width: 100%;
}

/* Clean Form Sections */
.clean-form-section {
  margin-bottom: 2rem;
  padding: 2rem 0;
  min-height: 400px; /* Minimum height to prevent layout shift */
}

.clean-form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 1rem;
}

.clean-section-header {
  margin-bottom: 2rem;
}

.clean-section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  text-align: left;
}

.clean-section-subtitle {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 0;
  text-align: left;
}

.clean-form-fields {
  padding: 0;
}

/* Clean Navigation - Fixed Bottom */
.clean-form-navigation {
  border-top: 1px solid var(--border-light);
  padding: 1rem 2rem;
  margin-top: auto;
  flex-shrink: 0;
  background: var(--bg-primary);
  position: sticky;
  bottom: 0;
  z-index: 10;
}

.clean-step-indicator {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Clean Form Button Styles */
.clean-nav-btn {
  padding: 0.625rem 1.5rem;
  font-weight: 600;
  border-radius: 6px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid transparent;
  font-size: 0.875rem;
  letter-spacing: 0.025em;
  position: relative;
  overflow: hidden;
  min-width: 100px;
}

.clean-nav-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.clean-nav-btn:active {
  transform: translateY(0);
}

.clean-nav-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Previous Button */
.clean-nav-btn.btn-outline-secondary {
  background: transparent;
  border-color: var(--border-light);
  color: var(--text-secondary);
}

.clean-nav-btn.btn-outline-secondary:hover:not(:disabled) {
  background: var(--text-secondary);
  border-color: var(--text-secondary);
  color: white;
}

/* Next Button */
.clean-nav-btn.btn-primary {
  background: linear-gradient(135deg, var(--primary) 0%, #0056b3 100%);
  border-color: var(--primary);
  color: white;
}

.clean-nav-btn.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #0056b3 0%, var(--primary) 100%);
  border-color: #0056b3;
  box-shadow: 0 8px 25px rgba(var(--primary-rgb), 0.3);
}

/* Submit Button */
.clean-submit-btn {
  padding: 0.75rem 2rem;
  font-weight: 700;
  border-radius: 6px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid transparent;
  font-size: 1rem;
  letter-spacing: 0.025em;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  border-color: #28a745;
  color: white;
  position: relative;
  overflow: hidden;
  min-width: 180px;
}

.clean-submit-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #20c997 0%, #28a745 100%);
  border-color: #20c997;
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(40, 167, 69, 0.3);
}

.clean-submit-btn:active {
  transform: translateY(0);
}

.clean-submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
  background: #6c757d;
  border-color: #6c757d;
}

/* Button loading state */
.clean-nav-btn .spinner-border-sm,
.clean-submit-btn .spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 0.125em;
}

/* Clean Form Input Styling */
.clean-form-control {
  border: 1px solid var(--border-light);
  border-radius: 6px;
  padding: 0.875rem 1rem;
  font-size: 1rem;
  transition: all 0.2s ease;
  background: transparent;
  color: var(--text-primary);
  width: 100%;
  box-shadow: none;
  height: 48px; /* Fixed height for consistency */
  line-height: 1.5;
}

/* Textarea specific styling */
.clean-form-control[rows] {
  height: auto;
  min-height: 96px; /* 2x the standard height */
  resize: vertical;
}

.clean-form-control:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.1);
  background: transparent;
  outline: none;
}

.clean-form-control.is-invalid {
  border-color: #dc3545;
}

.clean-form-label {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  display: block;
}

.clean-form-error {
  color: #dc3545;
  font-size: 0.75rem;
  margin-top: 0.25rem;
  display: block;
  font-weight: 500;
  line-height: 1.4;
  padding: 0.25rem 0;
  animation: slideInError 0.3s ease-out;
}

/* Unified error message animation */
@keyframes slideInError {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Error state for form controls */
.clean-form-control.is-invalid {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.1);
}

.clean-form-control.is-invalid:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.15);
}

/* Success state for form controls */
.clean-form-control.is-valid {
  border-color: #28a745;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.1);
}

.clean-form-control.is-valid:focus {
  border-color: #28a745;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.15);
}

/* Form text helper styling */
.clean-form-text {
  color: var(--text-secondary);
  font-size: 0.75rem;
  margin-top: 0.25rem;
  display: block;
  line-height: 1.4;
}

/* Error message styling */
.clean-form-error {
  color: #dc3545;
  font-size: 0.75rem;
  margin-top: 0.25rem;
  display: block;
  line-height: 1.4;
  font-weight: 500;
}

.clean-form-error:not(:empty) {
  animation: fadeInError 0.3s ease-in-out;
}

@keyframes fadeInError {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Clean Form Groups */
.clean-form-fields .mb-3 {
  margin-bottom: 1.5rem;
}

.clean-form-fields .row .col-md-6 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

/* Map section styling */
.map-section {
  background: var(--card-bg);
  border: 1px solid var(--border-light);
  border-radius: 8px;
  padding: 1.5rem;
  height: fit-content;
  position: sticky;
  top: 2rem;
}

.map-title {
  color: var(--text-primary);
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.map-subtitle {
  font-size: 0.875rem;
  margin-bottom: 0;
}

/* Dark theme support for map section */
[data-theme="dark"] .map-section {
  background: var(--card-bg-dark);
  border-color: var(--border-dark);
}

[data-theme="dark"] .map-title {
  color: var(--text-primary-dark);
}

/* Location map container styling */
.location-map-container {
  background: transparent;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  height: fit-content;
}

/* Address form container styling */
.address-form-container {
  background: transparent;
  padding: 0;
  height: fit-content;
}

/* Remove borders from map controls */
.location-map-container .map-controls {
  border: none !important;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px);
}

/* Dark theme support */
[data-theme="dark"] .location-map-container {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .location-map-container .map-controls {
  background: rgba(33, 37, 41, 0.95) !important;
}

/* Address search styling */
.address-search {
  position: relative;
}

.address-search .search-results {
  border: 1px solid #dee2e6 !important;
  border-top: none !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
  background: white !important;
  border-radius: 0 0 8px 8px !important;
  overflow: hidden;
  z-index: 1100 !important;
}

.search-result-item {
  transition: all 0.2s ease;
  border-bottom: 1px solid #f1f3f4 !important;
}

.search-result-item:hover {
  background-color: #f8f9fa !important;
  transform: translateX(2px);
}

.search-result-item:last-child {
  border-bottom: none !important;
}

.search-result-item .fw-bold {
  color: #0d6efd !important;
}

/* Dark theme support for search */
[data-theme="dark"] .address-search .search-results {
  background-color: #2d3748 !important;
  border-color: #4a5568 !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .search-result-item {
  border-bottom-color: #4a5568 !important;
}

[data-theme="dark"] .search-result-item:hover {
  background-color: #4a5568 !important;
}

[data-theme="dark"] .search-result-item .fw-bold {
  color: #63b3ed !important;
}

[data-theme="dark"] .search-result-item .text-muted {
  color: #a0aec0 !important;
}

/* Enhanced form field styling */
.address-form-container .clean-form-fields {
  padding: 0;
}

.address-form-container .clean-form-control {
  border: 1px solid var(--border-light);
  border-radius: 8px;
  padding: 0.75rem 1rem;
  font-size: 0.95rem;
  transition: all 0.2s ease;
}

.address-form-container .clean-form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

.address-form-container .clean-form-label {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

/* Dark theme support for form fields */
[data-theme="dark"] .address-form-container .clean-form-control {
  background: var(--input-bg-dark);
  color: var(--text-primary-dark);
}

[data-theme="dark"] .address-form-container .clean-form-label {
  color: var(--text-primary-dark);
}

/* Responsive layout */
@media (max-width: 991.98px) {
  .location-map-container {
    margin-bottom: 2rem;
  }

  .address-form-container {
    margin-top: 0;
  }

  .address-search .search-results {
    max-height: 150px !important;
  }

  .location-map-container .map-controls {
    padding: 1rem !important;
  }
}

/* Dark Theme Support for Clean Design */
[data-theme="dark"] .clean-kyc-header {
  border-bottom-color: var(--border-primary);
}

[data-theme="dark"] .clean-stepper-item {
  border-color: var(--border-primary);
}

[data-theme="dark"] .clean-stepper-item.active {
  border-color: var(--primary);
  background: rgba(var(--primary-rgb), 0.1);
}

[data-theme="dark"] .clean-stepper-item.completed {
  border-color: #28a745;
  background: rgba(40, 167, 69, 0.12);
  border-left: 5px solid #28a745;
  padding-left: 0.75rem;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
}

[data-theme="dark"] .clean-stepper-item.valid {
  border-color: #28a745;
  background: rgba(40, 167, 69, 0.08);
  border-left: 5px solid #28a745;
  padding-left: 0.75rem;
  box-shadow: 0 1px 4px rgba(40, 167, 69, 0.15);
}

[data-theme="dark"] .clean-stepper-item.invalid {
  border-color: #dc3545;
  background: rgba(220, 53, 69, 0.08);
  border-left: 5px solid #dc3545;
  padding-left: 0.75rem;
  box-shadow: 0 1px 4px rgba(220, 53, 69, 0.15);
}

[data-theme="dark"] .clean-stepper-number {
  border-color: var(--border-primary);
}

[data-theme="dark"] .clean-form-section {
  border-bottom-color: var(--border-primary);
}

[data-theme="dark"] .clean-form-navigation {
  border-top-color: var(--border-primary);
  background: var(--bg-primary);
}

[data-theme="dark"] .clean-form-control {
  background: transparent;
  border-color: var(--border-primary);
  color: var(--text-primary);
}

[data-theme="dark"] .clean-form-control:focus {
  background: transparent;
  border-color: var(--primary);
}

/* Responsive Design for Clean Layout */
@media (max-width: 991px) {
  .clean-kyc-content .row {
    flex-direction: column;
  }

  .clean-stepper-nav {
    padding: 0 0 2rem 0;
    margin-bottom: 2rem;
    border-bottom: 1px solid var(--border-light);
  }

  .clean-stepper-list {
    display: flex;
    flex-direction: row;
    gap: 0.5rem;
    overflow-x: auto;
    padding-bottom: 0.5rem;
  }

  .clean-stepper-item {
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-width: 120px;
    padding: 1rem 0.5rem;
    flex-shrink: 0;
  }

  .clean-stepper-item.clickable:hover {
    transform: translateY(-2px);
  }

  .clean-stepper-number {
    margin-right: 0;
    margin-bottom: 0.5rem;
    width: 28px;
    height: 28px;
    font-size: 0.75rem;
  }

  .clean-stepper-label {
    font-size: 0.625rem;
  }

  .clean-stepper-title {
    font-size: 0.8rem;
  }

  .clean-form-container {
    padding: 0 1rem;
  }

  .clean-form-navigation {
    padding: 1rem 1rem 0.75rem;
  }
}

@media (max-width: 576px) {
  .clean-kyc-header {
    padding: 0 0 1.5rem 0;
    margin-bottom: 1.5rem;
  }

  .clean-kyc-title {
    font-size: 1.5rem;
  }

  .clean-stepper-item {
    min-width: 100px;
    padding: 0.75rem 0.5rem;
  }

  .clean-stepper-number {
    width: 24px;
    height: 24px;
    font-size: 0.75rem;
  }

  .clean-form-container {
    padding: 0 0.5rem;
  }

  .clean-form-navigation {
    padding: 0.75rem 0.5rem 0.75rem;
  }

  .clean-nav-btn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
    min-width: 80px;
  }

  .clean-submit-btn {
    padding: 0.625rem 1.5rem;
    font-size: 0.8rem;
    min-width: 140px;
  }

  .clean-form-fields .row .col-md-6 {
    padding-left: 0;
    padding-right: 0;
  }

  .clean-section-title {
    font-size: 1.25rem;
  }
}

/* Phone Input Styling */
.phone-input-wrapper {
  position: relative;
}

.phone-input-container {
  width: 100% !important;
}

.phone-input-field {
  width: 100% !important;
  border: 1px solid var(--border-light) !important;
  border-radius: 6px !important;
  padding: 0.875rem 1rem 0.875rem 60px !important;
  font-size: 1rem !important;
  height: 48px !important;
  line-height: 1.5 !important;
  background: transparent !important;
  color: var(--text-primary) !important;
  transition: all 0.2s ease !important;
}

.phone-input-field:focus {
  border-color: var(--primary) !important;
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.1) !important;
  outline: none !important;
}

.phone-input-field.is-invalid {
  border-color: #dc3545 !important;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.1) !important;
}

.phone-input-field.is-invalid:focus {
  border-color: #dc3545 !important;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.15) !important;
}

.phone-input-field.is-valid {
  border-color: #28a745 !important;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.1) !important;
}

.phone-input-field.is-valid:focus {
  border-color: #28a745 !important;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.15) !important;
}

.phone-input-button {
  background: transparent !important;
  border: none !important;
  border-right: 1px solid var(--border-light) !important;
  border-radius: 6px 0 0 6px !important;
  height: 46px !important;
  width: 58px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  position: absolute !important;
  left: 1px !important;
  top: 1px !important;
  z-index: 2 !important;
}

.phone-input-button:hover {
  background: rgba(var(--primary-rgb), 0.05) !important;
}

.phone-input-dropdown {
  background: var(--bg-primary) !important;
  border: 1px solid var(--border-light) !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  z-index: 9999 !important;
  max-height: 200px !important;
  overflow-y: auto !important;
}

.phone-input-search {
  padding: 0.5rem !important;
  border: none !important;
  border-bottom: 1px solid var(--border-light) !important;
  background: transparent !important;
  color: var(--text-primary) !important;
  font-size: 0.875rem !important;
}

.phone-input-search:focus {
  outline: none !important;
  border-bottom-color: var(--primary) !important;
}

/* Select Input Styling */
.select-input-wrapper {
  position: relative;
}

.select-input-wrapper .react-select-container {
  font-size: 1rem;
}

.select-input-wrapper .react-select__control {
  min-height: 48px !important;
}

.select-input-wrapper .react-select__value-container {
  padding: 0.375rem 0.75rem !important;
}

.select-input-wrapper .react-select__input-container {
  margin: 0 !important;
  padding: 0 !important;
}

.select-input-wrapper .react-select__placeholder {
  margin: 0 !important;
}

.select-input-wrapper .react-select__single-value {
  margin: 0 !important;
}

/* Dark theme support for phone input */
[data-theme="dark"] .phone-input-field {
  background: transparent !important;
  border-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .phone-input-button {
  border-right-color: var(--border-primary) !important;
}

[data-theme="dark"] .phone-input-dropdown {
  background: var(--bg-primary) !important;
  border-color: var(--border-primary) !important;
}

[data-theme="dark"] .phone-input-search {
  background: transparent !important;
  border-bottom-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

/* ===== VIEWUSER COMPACT DESIGN STYLES - MATCHES TRITRACKZ-FE PROFILESETTINGS ===== */

/* ViewUser Compact Design Styles - Matches TriTrackz-FE ProfileSettings */
.compact-profile-settings-full {
  min-height: 100vh;
}

/* Compact Profile Card */
.compact-profile-card {
  border-radius: 12px;
  transition: all 0.3s ease;
}

.compact-profile-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
}

.profile-avatar-compact {
  font-weight: 600;
  letter-spacing: 1px;
}

/* Compact Details Cards */
.compact-details-card {
  border-radius: 12px;
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
}

.compact-details-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08) !important;
}

.compact-details-card .card-header {
  border-radius: 12px 12px 0 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

/* Compact Field Groups */
.compact-field-group {
  margin-bottom: 1rem;
}

.compact-field-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.25rem;
  display: block;
}

.compact-field-value {
  font-size: 0.875rem;
  color: #495057;
  font-weight: 500;
}

.compact-field-value span {
  display: block;
  padding: 0.375rem 0;
  border-bottom: 1px solid #f1f3f4;
  transition: all 0.2s ease;
}

.compact-field-value:hover span {
  color: #007bff;
  border-bottom-color: #007bff;
}

/* Physical Card Styles */
.compact-physical-card {
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 1rem;
  height: 100%;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.compact-physical-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #007bff, #0056b3);
}

.compact-physical-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(0, 123, 255, 0.15);
  border-color: #007bff;
}

.compact-card-header {
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e9ecef;
}

.card-title {
  font-size: 0.7rem;
  font-weight: 700;
  color: #007bff;
  letter-spacing: 1px;
  text-transform: uppercase;
}

.compact-card-content {
  flex: 1;
}

.compact-card-field {
  margin-bottom: 0.75rem;
}

.compact-card-field .field-label {
  font-size: 0.65rem;
  font-weight: 600;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  margin-bottom: 0.25rem;
  display: block;
}

.compact-card-field .field-value {
  font-size: 0.8rem;
  color: #495057;
  font-weight: 500;
  word-break: break-all;
}

/* PAN Card Specific Styling */
.pan-card::before {
  background: linear-gradient(90deg, #28a745, #20c997);
}

.pan-card:hover {
  box-shadow: 0 10px 30px rgba(40, 167, 69, 0.15);
  border-color: #28a745;
}

.pan-card .card-title {
  color: #28a745;
}

/* Aadhaar Card Specific Styling */
.aadhaar-card::before {
  background: linear-gradient(90deg, #fd7e14, #e63946);
}

.aadhaar-card:hover {
  box-shadow: 0 10px 30px rgba(253, 126, 20, 0.15);
  border-color: #fd7e14;
}

.aadhaar-card .card-title {
  color: #fd7e14;
}

/* Bank Card Specific Styling */
.bank-card::before {
  background: linear-gradient(90deg, #6f42c1, #e83e8c);
}

.bank-card:hover {
  box-shadow: 0 10px 30px rgba(111, 66, 193, 0.15);
  border-color: #6f42c1;
}

.bank-card .card-title {
  color: #6f42c1;
}

/* Badge Styling */
.badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .compact-profile-settings-full {
    padding: 1rem !important;
  }

  .compact-physical-card {
    margin-bottom: 1rem;
  }

  .compact-field-value span {
    font-size: 0.8rem;
  }

  .compact-card-field .field-value {
    font-size: 0.75rem;
  }
}

@media (max-width: 576px) {
  .compact-details-card .card-body {
    padding: 1.5rem !important;
  }

  .compact-physical-card {
    padding: 0.75rem;
  }

  .profile-avatar-compact {
    width: 50px !important;
    height: 50px !important;
    font-size: 20px !important;
  }
}

/* Animation for loading states */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.compact-details-card,
.compact-physical-card,
.compact-profile-card {
  animation: fadeInUp 0.5s ease-out;
}

/* Hover effects for interactive elements */
.compact-field-value:hover,
.compact-card-field:hover {
  cursor: default;
}

/* Address Format Container */
.address-format-container {
  width: 100%;
  text-align: center;
}

.formatted-address {
  display: inline-block;
  text-align: left;
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid #007bff;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  max-width: 300px;
}

.address-line {
  margin-bottom: 0.5rem;
  color: #495057;
  font-weight: 500;
}

.address-line:last-child {
  margin-bottom: 0;
  font-weight: 600;
  color: #007bff;
}

/* Documents List Styling */
.documents-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.document-row {
  display: flex;
  align-items: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.document-row:hover {
  background: #e9ecef;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.document-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  border-radius: 8px;
  border: 2px solid #28a745;
  margin-right: 1rem;
}

.document-icon.missing {
  border-color: #ffc107;
  background: #fff3cd;
}

.document-name {
  flex: 1;
  margin-right: 1rem;
}

.document-name h6 {
  margin: 0;
  font-weight: 600;
  color: #495057;
}

.document-status {
  margin-right: 1rem;
}

.document-actions {
  flex-shrink: 0;
}

.download-btn {
  min-width: 120px;
}

/* ===== PROFILE KYC FORM DARK THEME STYLES ===== */

/* Main Container */
.profile-kyc-form-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header */
.profile-kyc-header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Content Layout */
.profile-kyc-content {
  display: flex;
  flex: 1;
  min-height: 0;
}

/* Left Sidebar */
.profile-kyc-sidebar {
  width: 280px;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  padding: 2rem 0;
  overflow-y: auto;
}

/* Steps Navigation */
.steps-navigation {
  padding: 0 1rem;
}

.step-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  margin-bottom: 0.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.step-item.clickable:hover {
  background: rgba(255, 255, 255, 0.1);
}

.step-item.active {
  background: rgba(34, 197, 94, 0.2);
  border: 1px solid #22c55e;
}

.step-item.completed {
  background: rgba(34, 197, 94, 0.1);
}

.step-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 14px;
  margin-right: 1rem;
  flex-shrink: 0;
}

.step-item.active .step-number {
  background: #22c55e;
  border-color: #22c55e;
  color: white;
}

.step-item.completed .step-number {
  background: #22c55e;
  border-color: #22c55e;
  color: white;
}

.step-content {
  flex: 1;
}

.step-label {
  font-size: 11px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 2px;
}

.step-title {
  font-size: 14px;
  font-weight: 500;
  color: white;
  line-height: 1.3;
}

/* Main Content Area */
.profile-kyc-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.form-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 2rem;
}

.form-header {
  margin-bottom: 2rem;
}

.form-body {
  flex: 1;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.form-footer {
  padding: 1.5rem 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Form Input Styles for Dark Theme */
.profile-kyc-form-container .form-control {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 8px;
  padding: 0.75rem 1rem;
}

.profile-kyc-form-container .form-control:focus {
  background: rgba(255, 255, 255, 0.15);
  border-color: #22c55e;
  box-shadow: 0 0 0 0.2rem rgba(34, 197, 94, 0.25);
  color: white;
}

.profile-kyc-form-container .form-control::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.profile-kyc-form-container .form-label {
  color: white;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.profile-kyc-form-container .text-muted {
  color: rgba(255, 255, 255, 0.6) !important;
}

/* Input Group Styles */
.profile-kyc-form-container .input-group-text {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  border-right: none;
}

.profile-kyc-form-container .input-group .form-control {
  border-left: none;
}

.profile-kyc-form-container .input-group .form-control:focus {
  border-left: none;
}

.profile-kyc-form-container .flag-icon {
  width: 20px;
  height: 14px;
  margin-right: 0.5rem;
}

/* Badge Styles */
.profile-kyc-form-container .badge {
  font-size: 0.7rem;
  padding: 0.25rem 0.5rem;
}

/* Button Styles */
.profile-kyc-form-container .btn-outline-light {
  border-color: rgba(255, 255, 255, 0.3);
  color: white;
}

.profile-kyc-form-container .btn-outline-light:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  color: white;
}

/* Invalid Feedback for Dark Theme */
.profile-kyc-form-container .invalid-feedback {
  color: #ff6b6b;
  font-size: 0.875rem;
}

.profile-kyc-form-container .is-invalid {
  border-color: #ff6b6b;
}

.profile-kyc-form-container .is-valid {
  border-color: #22c55e;
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-kyc-content {
    flex-direction: column;
  }

  .profile-kyc-sidebar {
    width: 100%;
    max-height: 200px;
    overflow-x: auto;
    padding: 1rem 0;
  }

  .steps-navigation {
    display: flex;
    gap: 1rem;
    padding: 0 1rem;
  }

  .step-item {
    flex-direction: column;
    min-width: 120px;
    text-align: center;
    margin-bottom: 0;
  }

  .step-number {
    margin-right: 0;
    margin-bottom: 0.5rem;
  }

  .form-content {
    padding: 1rem;
  }

  .form-body {
    padding: 1.5rem;
  }
}

/* Eye button styling */
.btn-outline-secondary {
  border-color: #dee2e6;
  color: #6c757d;
}

.btn-outline-secondary:hover {
  background-color: #6c757d;
  border-color: #6c757d;
  color: white;
}

/* ProfileKYC Form Styles */
.profile-kyc-form-container {
  .steps-navigation {
    .steps-list {
      .step-item {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        margin-bottom: 8px;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
        border: 1px solid transparent;

        &.clickable:hover {
          background-color: var(--bs-light);
          border-color: var(--bs-primary);
        }

        &.disabled {
          cursor: not-allowed;
          opacity: 0.5;
        }

        &.active {
          background-color: var(--bs-primary);
          color: white;
          border-color: var(--bs-primary);
        }

        &.completed {
          background-color: var(--bs-success);
          color: white;
          border-color: var(--bs-success);
        }

        .step-number {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          font-weight: 600;
          margin-right: 12px;
          background-color: rgba(255, 255, 255, 0.2);

          .step-item:not(.active):not(.completed) & {
            background-color: var(--bs-secondary);
            color: white;
          }
        }

        .step-title {
          font-size: 14px;
          font-weight: 500;
          line-height: 1.2;
        }
      }
    }
  }
}

/* Clean Form Styles */
.clean-form-section {
  .clean-section-header {
    margin-bottom: 24px;

    .clean-section-title {
      font-size: 24px;
      font-weight: 600;
      color: var(--bs-primary);
      margin-bottom: 8px;
    }

    .clean-section-subtitle {
      color: var(--bs-secondary);
      margin-bottom: 0;
      font-size: 14px;
    }
  }

  .clean-form-fields {
    .clean-form-label {
      font-weight: 500;
      color: var(--bs-dark);
      margin-bottom: 6px;
      font-size: 14px;

      .text-danger {
        color: var(--bs-danger) !important;
      }
    }

    .clean-form-control {
      border: 1px solid var(--bs-border-color);
      border-radius: 6px;
      padding: 10px 12px;
      font-size: 14px;
      transition: all 0.2s ease;

      &:focus {
        border-color: var(--bs-primary);
        box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
      }

      &.is-valid {
        border-color: var(--bs-success);

        &:focus {
          border-color: var(--bs-success);
          box-shadow: 0 0 0 0.2rem rgba(var(--bs-success-rgb), 0.25);
        }
      }

      &.is-invalid {
        border-color: var(--bs-danger);

        &:focus {
          border-color: var(--bs-danger);
          box-shadow: 0 0 0 0.2rem rgba(var(--bs-danger-rgb), 0.25);
        }
      }
    }

    .clean-form-error {
      color: var(--bs-danger);
      font-size: 12px;
      margin-top: 4px;
    }

    .clean-form-text {
      color: var(--bs-secondary);
      font-size: 12px;
      margin-top: 4px;
    }
  }
}

/* Location Map Container */
.location-map-container {
  border: 1px solid var(--bs-border-color);
  border-radius: 8px;
  overflow: hidden;

  .leaflet-container {
    border-radius: 8px;
  }
}

/* Address Form Container */
.address-form-container {
  .address-search {
    .search-results {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: white;
      border: 1px solid var(--bs-border-color);
      border-top: none;
      border-radius: 0 0 6px 6px;
      max-height: 200px;
      overflow-y: auto;
      z-index: 1000;

      .search-result-item {
        padding: 10px 12px;
        cursor: pointer;
        border-bottom: 1px solid var(--bs-border-color-translucent);

        &:hover {
          background-color: var(--bs-light);
        }

        &:last-child {
          border-bottom: none;
        }

        .result-title {
          font-weight: 500;
          font-size: 14px;
          margin-bottom: 2px;
        }

        .result-subtitle {
          font-size: 12px;
          color: var(--bs-secondary);
        }
      }
    }
  }
}

/* Step Navigation Responsive */
@media (max-width: 991.98px) {
  .profile-kyc-form-container {
    .steps-navigation {
      margin-bottom: 24px;

      .steps-list {
        display: flex;
        overflow-x: auto;
        padding-bottom: 8px;

        .step-item {
          flex-shrink: 0;
          margin-right: 12px;
          margin-bottom: 0;
          min-width: 140px;

          .step-title {
            white-space: nowrap;
          }
        }
      }
    }
  }
}

/* Phone Input Styling */
.react-tel-input {
  .form-control {
    border: 1px solid var(--bs-border-color);
    border-radius: 6px;
    padding: 10px 12px 10px 48px;
    font-size: 14px;

    &:focus {
      border-color: var(--bs-primary);
      box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
    }

    &.is-valid {
      border-color: var(--bs-success);

      &:focus {
        border-color: var(--bs-success);
        box-shadow: 0 0 0 0.2rem rgba(var(--bs-success-rgb), 0.25);
      }
    }

    &.is-invalid {
      border-color: var(--bs-danger);

      &:focus {
        border-color: var(--bs-danger);
        box-shadow: 0 0 0 0.2rem rgba(var(--bs-danger-rgb), 0.25);
      }
    }
  }

  .flag-dropdown {
    border: 1px solid var(--bs-border-color);
    border-right: none;
    border-radius: 6px 0 0 6px;
    background-color: var(--bs-light);
  }
}

/* Select Input Styling */
.react-select__control {
  border: 1px solid var(--bs-border-color) !important;
  border-radius: 6px !important;
  min-height: 42px !important;

  &:hover {
    border-color: var(--bs-primary) !important;
  }

  &.react-select__control--is-focused {
    border-color: var(--bs-primary) !important;
    box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25) !important;
  }

  &.is-valid {
    border-color: var(--bs-success) !important;

    &.react-select__control--is-focused {
      border-color: var(--bs-success) !important;
      box-shadow: 0 0 0 0.2rem rgba(var(--bs-success-rgb), 0.25) !important;
    }
  }

  &.is-invalid {
    border-color: var(--bs-danger) !important;

    &.react-select__control--is-focused {
      border-color: var(--bs-danger) !important;
      box-shadow: 0 0 0 0.2rem rgba(var(--bs-danger-rgb), 0.25) !important;
    }
  }
}

.react-select__placeholder {
  color: var(--bs-secondary) !important;
  font-size: 14px !important;
}

.react-select__single-value {
  color: var(--bs-dark) !important;
  font-size: 14px !important;
}

.react-select__menu {
  border: 1px solid var(--bs-border-color) !important;
  border-radius: 6px !important;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.react-select__option {
  font-size: 14px !important;

  &:hover {
    background-color: var(--bs-light) !important;
  }

  &.react-select__option--is-selected {
    background-color: var(--bs-primary) !important;
  }

  &.react-select__option--is-focused {
    background-color: var(--bs-light) !important;
    color: var(--bs-dark) !important;
  }
}

/* End of ProfileKYC Styles */

/* ===== TRITRACKZ-FE PROFILESETTINGS DESIGN PATTERN STYLES ===== */

/* Consistent Profile Card Styling */
.profile-settings-card {
  border-radius: 12px;
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
}

.profile-settings-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
}

.profile-settings-card .card-header {
  border-radius: 12px 12px 0 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-bottom: 1px solid #e9ecef;
}

/* Profile Avatar Styling */
.profile-avatar-large {
  width: 80px;
  height: 80px;
  font-size: 32px;
  font-weight: 600;
  letter-spacing: 1px;
}

/* Form Field Styling */
.profile-field-group {
  margin-bottom: 1.5rem;
}

.profile-field-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #6c757d;
  margin-bottom: 0.5rem;
  display: block;
}

.profile-field-value {
  font-size: 1rem;
  color: #495057;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.profile-field-value .text-muted {
  margin-right: 0.5rem;
}

/* Step Navigation Styling */
.step-navigation-card {
  position: sticky;
  top: 2rem;
}

.step-item {
  transition: all 0.3s ease;
  border-radius: 8px;
}

.step-item.clickable:hover {
  background-color: rgba(0, 123, 255, 0.1) !important;
}

.step-item.bg-primary {
  background-color: #007bff !important;
}

.step-item.bg-success {
  background-color: #28a745 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-avatar-large {
    width: 60px;
    height: 60px;
    font-size: 24px;
  }

  .step-navigation-card {
    position: relative;
    top: auto;
    margin-bottom: 1rem;
  }
}

/* Dark Theme Support */
[data-theme="dark"] .profile-settings-card {
  background: var(--bg-card) !important;
  border-color: var(--border-primary) !important;
}

[data-theme="dark"] .profile-settings-card:hover {
  background: var(--bg-card-hover) !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3) !important;
}

[data-theme="dark"] .profile-settings-card .card-header {
  background: var(--bg-card) !important;
  border-bottom-color: var(--border-primary) !important;
}

[data-theme="dark"] .profile-field-label {
  color: var(--text-secondary) !important;
}

[data-theme="dark"] .profile-field-value {
  color: var(--text-primary) !important;
}

/* Light Theme Enhancements */
[data-theme="light"] .profile-settings-card {
  background: #ffffff !important;
  border: 1px solid rgba(11, 31, 58, 0.15) !important;
  box-shadow: 0 2px 12px rgba(11, 31, 58, 0.08) !important;
}

[data-theme="light"] .profile-settings-card:hover {
  background: #fefefe !important;
  border-color: rgba(11, 31, 58, 0.25) !important;
  box-shadow: 0 4px 20px rgba(11, 31, 58, 0.12) !important;
}

[data-theme="light"] .profile-settings-card .card-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%) !important;
  border-bottom: 1px solid rgba(11, 31, 58, 0.15) !important;
}

/* End of TriTrackz-FE ProfileSettings Design Pattern Styles */
