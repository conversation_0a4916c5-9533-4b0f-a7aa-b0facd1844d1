import React from 'react';
import { Field, ErrorMessage } from 'formik';

const BankDetailsForm = ({ formik, existingDocuments, onDocumentChange }) => {
  // existingDocuments and onDocumentChange are not used in this form but passed from parent
  return (
    <div className="clean-form-section">
      <div className="clean-section-header">
        <h3 className="clean-section-title">Bank Details</h3>
        <p className="clean-section-subtitle">Please provide your bank account information</p>
      </div>
      <div className="clean-form-fields">
        <div className="row">
          <div className="col-md-6 mb-3">
            <label htmlFor="bankDetails.accountNumber" className="clean-form-label">
              Account Number <span className="text-danger">*</span>
            </label>
            <Field
              type="text"
              name="bankDetails.accountNumber"
              className={`clean-form-control ${
                formik.touched.bankDetails?.accountNumber && formik.errors.bankDetails?.accountNumber
                  ? 'is-invalid'
                  : formik.touched.bankDetails?.accountNumber && formik.values.bankDetails?.accountNumber
                  ? 'is-valid'
                  : ''
              }`}
              placeholder="Enter bank account number"
              onChange={(e) => {
                // Only allow digits
                const value = e.target.value.replace(/\D/g, '');
                formik.setFieldValue('bankDetails.accountNumber', value);
              }}
            />
            <ErrorMessage
              name="bankDetails.accountNumber"
              component="div"
              className="clean-form-error"
            />
          </div>

          <div className="col-md-6 mb-3">
            <label htmlFor="bankDetails.accountHolderName" className="clean-form-label">
              Account Holder Name <span className="text-danger">*</span>
            </label>
            <Field
              type="text"
              name="bankDetails.accountHolderName"
              className={`clean-form-control ${
                formik.touched.bankDetails?.accountHolderName && formik.errors.bankDetails?.accountHolderName
                  ? 'is-invalid'
                  : formik.touched.bankDetails?.accountHolderName && formik.values.bankDetails?.accountHolderName
                  ? 'is-valid'
                  : ''
              }`}
              placeholder="Enter account holder name"
            />
            <ErrorMessage
              name="bankDetails.accountHolderName"
              component="div"
              className="clean-form-error"
            />
          </div>

          <div className="col-md-6 mb-3">
            <label htmlFor="bankDetails.bankName" className="clean-form-label">
              Bank Name <span className="text-danger">*</span>
            </label>
            <Field
              type="text"
              name="bankDetails.bankName"
              className={`clean-form-control ${
                formik.touched.bankDetails?.bankName && formik.errors.bankDetails?.bankName
                  ? 'is-invalid'
                  : formik.touched.bankDetails?.bankName && formik.values.bankDetails?.bankName
                  ? 'is-valid'
                  : ''
              }`}
              placeholder="Enter bank name"
            />
            <ErrorMessage
              name="bankDetails.bankName"
              component="div"
              className="clean-form-error"
            />
          </div>

          <div className="col-md-6 mb-3">
            <label htmlFor="bankDetails.ifscCode" className="clean-form-label">
              IFSC Code <span className="text-danger">*</span>
            </label>
            <Field
              type="text"
              name="bankDetails.ifscCode"
              className={`clean-form-control ${
                formik.touched.bankDetails?.ifscCode && formik.errors.bankDetails?.ifscCode
                  ? 'is-invalid'
                  : formik.touched.bankDetails?.ifscCode && formik.values.bankDetails?.ifscCode
                  ? 'is-valid'
                  : ''
              }`}
              placeholder="Enter IFSC code (e.g., SBIN0001234)"
              style={{ textTransform: 'uppercase' }}
              onChange={(e) => {
                const value = e.target.value.toUpperCase();
                formik.setFieldValue('bankDetails.ifscCode', value);
              }}
            />
            <ErrorMessage
              name="bankDetails.ifscCode"
              component="div"
              className="clean-form-error"
            />
          </div>

          <div className="col-md-6 mb-3">
            <label htmlFor="bankDetails.branchName" className="clean-form-label">
              Branch Name <span className="text-danger">*</span>
            </label>
            <Field
              type="text"
              name="bankDetails.branchName"
              className={`clean-form-control ${
                formik.touched.bankDetails?.branchName && formik.errors.bankDetails?.branchName
                  ? 'is-invalid'
                  : formik.touched.bankDetails?.branchName && formik.values.bankDetails?.branchName
                  ? 'is-valid'
                  : ''
              }`}
              placeholder="Enter branch name"
            />
            <ErrorMessage
              name="bankDetails.branchName"
              component="div"
              className="clean-form-error"
            />
          </div>
        </div>

        <div className="row mt-3">
          <div className="col-12">
            <div className="alert alert-warning">
              <i className="fas fa-exclamation-triangle me-2"></i>
              <strong>Important:</strong> Please ensure that the bank account details are accurate. 
              These details will be used for payment processing and fund transfers.
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BankDetailsForm;
