import React, { useState, useCallback } from "react";
import { useFormik } from "formik";
import * as Yu<PERSON> from "yup";
import {
  Card,
  CardBody,
  Form,
  FormGroup,
  Label,
  Input,
  Button,
  Row,
  Col,
  Alert,
} from "reactstrap";
import { FaSave, FaTimes, FaSpinner } from "react-icons/fa";

// Validation schema
const validationSchema = Yup.object({
  firstName: Yup.string()
    .min(2, "First name must be at least 2 characters")
    .max(50, "First name must be less than 50 characters")
    .required("First name is required"),
  lastName: Yup.string()
    .min(2, "Last name must be at least 2 characters")
    .max(50, "Last name must be less than 50 characters")
    .required("Last name is required"),
  email: Yup.string()
    .email("Invalid email address")
    .required("Email is required"),
  phoneNumber: Yup.string()
    .matches(/^\+?[\d\s-()]+$/, "Invalid phone number format")
    .required("Phone number is required"),
  companyName: Yup.string()
    .min(2, "Company name must be at least 2 characters")
    .max(100, "Company name must be less than 100 characters"),
  address: Yup.string()
    .max(200, "Address must be less than 200 characters"),
  city: Yup.string()
    .max(50, "City must be less than 50 characters"),
  state: Yup.string()
    .max(50, "State must be less than 50 characters"),
  pincode: Yup.string()
    .matches(/^\d{6}$/, "Pincode must be 6 digits"),
});

const EditUserForm = ({ initialData, onSave, onCancel, isLoading = false }) => {
  const [submitError, setSubmitError] = useState("");

  const formik = useFormik({
    initialValues: {
      firstName: initialData?.firstName || "",
      lastName: initialData?.lastName || "",
      email: initialData?.email || "",
      phoneNumber: initialData?.phoneNumber || "",
      companyName: initialData?.companyName || "",
      address: initialData?.address || "",
      city: initialData?.city || "",
      state: initialData?.state || "",
      pincode: initialData?.pincode || "",
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        setSubmitError("");
        await onSave(values);
      } catch (error) {
        setSubmitError(error.message || "Failed to save user data");
      }
    },
  });

  const handleCancel = useCallback(() => {
    formik.resetForm();
    onCancel();
  }, [formik, onCancel]);

  return (
    <Card className="border-0 shadow-sm">
      <CardBody className="p-4">
        <Form onSubmit={formik.handleSubmit}>
          {submitError && (
            <Alert color="danger" className="mb-4">
              {submitError}
            </Alert>
          )}

          {/* Basic Information */}
          <div className="mb-4">
            <h5 className="mb-3 text-primary">Basic Information</h5>
            <Row>
              <Col md={6}>
                <FormGroup>
                  <Label for="firstName">
                    First Name <span className="text-danger">*</span>
                  </Label>
                  <Input
                    type="text"
                    name="firstName"
                    id="firstName"
                    placeholder="Enter first name"
                    value={formik.values.firstName}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    invalid={formik.touched.firstName && !!formik.errors.firstName}
                  />
                  {formik.touched.firstName && formik.errors.firstName && (
                    <div className="invalid-feedback">{formik.errors.firstName}</div>
                  )}
                </FormGroup>
              </Col>
              <Col md={6}>
                <FormGroup>
                  <Label for="lastName">
                    Last Name <span className="text-danger">*</span>
                  </Label>
                  <Input
                    type="text"
                    name="lastName"
                    id="lastName"
                    placeholder="Enter last name"
                    value={formik.values.lastName}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    invalid={formik.touched.lastName && !!formik.errors.lastName}
                  />
                  {formik.touched.lastName && formik.errors.lastName && (
                    <div className="invalid-feedback">{formik.errors.lastName}</div>
                  )}
                </FormGroup>
              </Col>
            </Row>
          </div>

          {/* Contact Information */}
          <div className="mb-4">
            <h5 className="mb-3 text-primary">Contact Information</h5>
            <Row>
              <Col md={6}>
                <FormGroup>
                  <Label for="email">
                    Email <span className="text-danger">*</span>
                  </Label>
                  <Input
                    type="email"
                    name="email"
                    id="email"
                    placeholder="Enter email address"
                    value={formik.values.email}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    invalid={formik.touched.email && !!formik.errors.email}
                  />
                  {formik.touched.email && formik.errors.email && (
                    <div className="invalid-feedback">{formik.errors.email}</div>
                  )}
                </FormGroup>
              </Col>
              <Col md={6}>
                <FormGroup>
                  <Label for="phoneNumber">
                    Phone Number <span className="text-danger">*</span>
                  </Label>
                  <Input
                    type="text"
                    name="phoneNumber"
                    id="phoneNumber"
                    placeholder="Enter phone number"
                    value={formik.values.phoneNumber}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    invalid={formik.touched.phoneNumber && !!formik.errors.phoneNumber}
                  />
                  {formik.touched.phoneNumber && formik.errors.phoneNumber && (
                    <div className="invalid-feedback">{formik.errors.phoneNumber}</div>
                  )}
                </FormGroup>
              </Col>
            </Row>
          </div>

          {/* Company Information */}
          <div className="mb-4">
            <h5 className="mb-3 text-primary">Company Information</h5>
            <FormGroup>
              <Label for="companyName">Company Name</Label>
              <Input
                type="text"
                name="companyName"
                id="companyName"
                placeholder="Enter company name"
                value={formik.values.companyName}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                invalid={formik.touched.companyName && !!formik.errors.companyName}
              />
              {formik.touched.companyName && formik.errors.companyName && (
                <div className="invalid-feedback">{formik.errors.companyName}</div>
              )}
            </FormGroup>
          </div>

          {/* Address Information */}
          <div className="mb-4">
            <h5 className="mb-3 text-primary">Address Information</h5>
            <FormGroup>
              <Label for="address">Address</Label>
              <Input
                type="textarea"
                name="address"
                id="address"
                placeholder="Enter address"
                rows={3}
                value={formik.values.address}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                invalid={formik.touched.address && !!formik.errors.address}
              />
              {formik.touched.address && formik.errors.address && (
                <div className="invalid-feedback">{formik.errors.address}</div>
              )}
            </FormGroup>
            <Row>
              <Col md={4}>
                <FormGroup>
                  <Label for="city">City</Label>
                  <Input
                    type="text"
                    name="city"
                    id="city"
                    placeholder="Enter city"
                    value={formik.values.city}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    invalid={formik.touched.city && !!formik.errors.city}
                  />
                  {formik.touched.city && formik.errors.city && (
                    <div className="invalid-feedback">{formik.errors.city}</div>
                  )}
                </FormGroup>
              </Col>
              <Col md={4}>
                <FormGroup>
                  <Label for="state">State</Label>
                  <Input
                    type="text"
                    name="state"
                    id="state"
                    placeholder="Enter state"
                    value={formik.values.state}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    invalid={formik.touched.state && !!formik.errors.state}
                  />
                  {formik.touched.state && formik.errors.state && (
                    <div className="invalid-feedback">{formik.errors.state}</div>
                  )}
                </FormGroup>
              </Col>
              <Col md={4}>
                <FormGroup>
                  <Label for="pincode">Pincode</Label>
                  <Input
                    type="text"
                    name="pincode"
                    id="pincode"
                    placeholder="Enter pincode"
                    value={formik.values.pincode}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    invalid={formik.touched.pincode && !!formik.errors.pincode}
                  />
                  {formik.touched.pincode && formik.errors.pincode && (
                    <div className="invalid-feedback">{formik.errors.pincode}</div>
                  )}
                </FormGroup>
              </Col>
            </Row>
          </div>

          {/* Action Buttons */}
          <div className="d-flex justify-content-end gap-2">
            <Button
              type="button"
              color="secondary"
              onClick={handleCancel}
              disabled={isLoading}
            >
              <FaTimes size={12} className="me-1" />
              Cancel
            </Button>
            <Button
              type="submit"
              color="primary"
              disabled={isLoading || !formik.isValid}
            >
              {isLoading ? (
                <FaSpinner className="fa-spin me-1" size={12} />
              ) : (
                <FaSave size={12} className="me-1" />
              )}
              Save Changes
            </Button>
          </div>
        </Form>
      </CardBody>
    </Card>
  );
};

export default EditUserForm;
