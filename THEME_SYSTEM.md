# TriTrackz Theme System Documentation

## Overview
The TriTrackz application now supports both light and dark themes with a smooth toggle functionality. The theme system is built using React Context and CSS custom properties (CSS variables).

## Features
- **Dark Theme (Default)**: Navy blue (#0B1F3A) background with light blue-gray text (#EAEFF6)
- **Light Theme**: Clean white (#FFFFFF) background with dark navy text (#0B1F3A)
- **Smooth Transitions**: Animated theme switching with no flashing
- **Persistent Storage**: Theme preference is saved in localStorage
- **Default Dark Theme**: Always defaults to dark theme, even after logout
- **AuthLayout Dark Mode**: Authentication pages always use dark theme regardless of user preference
- **Logout Theme Reset**: Theme automatically resets to dark when user logs out
- **Responsive Design**: Both themes work seamlessly across all screen sizes

## Implementation Details

### 1. Theme Context (`src/contexts/ThemeContext.jsx`)
- Manages global theme state using React Context
- Provides theme toggle functionality
- Persists theme preference in localStorage
- Handles smooth transitions during theme switching

### 2. Theme Toggle Component (`src/components/ThemeToggle/index.jsx`)
- Interactive button with sun/moon icons
- Smooth icon animations during theme switching
- Located in the top navigation bar
- Accessible with proper ARIA labels

### 3. CSS Variables System (`src/assets/css/Custom.css`)
- Uses CSS custom properties for dynamic theming
- Separate variable definitions for light and dark themes
- Smooth transitions for all theme-related properties
- Comprehensive coverage of all UI components

## Theme Variables

### Dark Theme (Default)
```css
:root, [data-theme="dark"] {
  /* Text Colors */
  --text-primary: #EAEFF6;                      /* Light blue-gray */
  --text-secondary: #A9B7CC;                    /* Medium blue-gray */

  /* Background Colors */
  --bg-primary: #0B1F3A;                        /* Navy blue - unchanged */
  --bg-secondary: #1C2A40;                      /* Surface */
  --bg-tertiary: #2C3B55;                       /* Elevated surface */
  --bg-card: #1C2A40;                           /* Card background */

  /* Button Colors */
  --btn-primary-bg: #0B1F3A;                    /* Primary button */
  --btn-primary-text: #EAEFF6;                  /* Button text */

  /* Accent Colors */
  --accent-complementary: #5C4428;              /* Complementary brown */
  --accent-success: #237983;                    /* Analogous teal */
}
```

### Light Theme
```css
[data-theme="light"] {
  /* Text Colors */
  --text-primary: #0B1F3A;                      /* Dark navy */
  --text-secondary: #4F6075;                    /* Medium blue-gray */

  /* Background Colors */
  --bg-primary: #FFFFFF;                        /* White */
  --bg-secondary: #F5F9FC;                      /* Surface */
  --bg-card: #FFFFFF;                           /* Card background */

  /* Button Colors */
  --btn-primary-bg: #0B1F3A;                    /* Primary button */
  --btn-primary-text: #FFFFFF;                  /* Button text */

  /* Accent Colors */
  --accent-complementary: #8B6B43;              /* Complementary brown */
  --accent-success: #5CA4AC;                    /* Analogous teal */
}
```

## Usage

### Using the Theme Context
```jsx
import { useTheme } from '@contexts/ThemeContext';

function MyComponent() {
  const { theme, toggleTheme, isDark, isLight } = useTheme();
  
  return (
    <div>
      <p>Current theme: {theme}</p>
      <button onClick={toggleTheme}>
        Switch to {isDark ? 'light' : 'dark'} mode
      </button>
    </div>
  );
}
```

### Theme-Aware Styling
```css
/* Component will automatically adapt to theme */
.my-component {
  background: var(--bg-primary);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
}

/* Theme-specific overrides if needed */
[data-theme="light"] .my-component {
  box-shadow: 0 2px 8px var(--shadow-light);
}
```

## Components Covered
- ✅ Main Layout (Sidebar, Topbar, Content)
- ✅ Authentication Pages (Login, Register, Forgot Password)
- ✅ Dashboard Components
- ✅ Cards and Tables
- ✅ Forms and Inputs
- ✅ Buttons and Links
- ✅ Notifications and Dropdowns
- ✅ Navigation Menus

## Browser Support
- All modern browsers that support CSS custom properties
- Graceful fallback for older browsers
- Smooth animations with hardware acceleration

## Performance
- Minimal JavaScript overhead
- CSS-based theme switching for optimal performance
- No layout shifts during theme transitions
- Efficient localStorage usage

## Accessibility
- Proper ARIA labels for theme toggle button
- High contrast ratios in both themes
- Keyboard navigation support
- Screen reader friendly

## Future Enhancements
- System theme detection (prefers-color-scheme)
- Additional theme variants
- Theme-specific component variants
- Advanced animation options
