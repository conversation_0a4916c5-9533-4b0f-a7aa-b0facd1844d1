import { MutationCache, QueryCache, QueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { logoutUser } from "./helper";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      refetchOnWindowFocus: false,
    },
  },
  queryCache: new QueryCache({
    onError: (error, query) => {
      if (error?.response?.status === 401) {
        toast.error("Session expired. Please login again.");
        logoutUser();
      } else {
        console.error("Query error:", error, query);
        toast.error(error?.message, {
          id: query.queryKey[0],
        });
      }
    },
  }),
  mutationCache: new MutationCache({
    onError: (error, variables, context, mutation) => {
      if (error?.response?.status === 401) {
        toast.error("Session expired. Please login again.");
        logoutUser();
      } else {
        console.error("Mutation error:", error);
        toast.error(error?.message, {
          id: mutation.mutationKey[0],
        });
      }
    },
  }),
});

export default queryClient;
