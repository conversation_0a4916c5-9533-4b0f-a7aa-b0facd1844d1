import React from 'react';
import { Field, ErrorMessage } from 'formik';

const GstDetailsForm = ({ formik, existingDocuments, onDocumentChange }) => {
  // existingDocuments and onDocumentChange are not used in this form but passed from parent
  return (
    <div className="clean-form-section">
      <div className="clean-section-header">
        <h3 className="clean-section-title">GST Details</h3>
        <p className="clean-section-subtitle">Please provide your GST registration information</p>
      </div>
      <div className="clean-form-fields">
        <div className="row">
          <div className="col-md-6 mb-3">
            <label htmlFor="gstDetails.gstNumber" className="clean-form-label">
              GST Number <span className="text-danger">*</span>
            </label>
            <Field
              type="text"
              name="gstDetails.gstNumber"
              className={`clean-form-control ${
                formik.touched.gstDetails?.gstNumber && formik.errors.gstDetails?.gstNumber
                  ? 'is-invalid'
                  : formik.touched.gstDetails?.gstNumber && formik.values.gstDetails?.gstNumber
                  ? 'is-valid'
                  : ''
              }`}
              placeholder="Enter GST number (15 digits)"
              style={{ textTransform: 'uppercase' }}
              maxLength="15"
              onChange={(e) => {
                const value = e.target.value.toUpperCase();
                formik.setFieldValue('gstDetails.gstNumber', value);
              }}
            />
            <ErrorMessage
              name="gstDetails.gstNumber"
              component="div"
              className="clean-form-error"
            />
            <small className="clean-form-text">
              Format: 22AAAAA0000A1Z5 (15 characters)
            </small>
          </div>

          <div className="col-md-6 mb-3">
            <label htmlFor="gstDetails.gstBusinessName" className="clean-form-label">
              Business Name as per GST <span className="text-danger">*</span>
            </label>
            <Field
              type="text"
              name="gstDetails.gstBusinessName"
              className={`clean-form-control ${
                formik.touched.gstDetails?.gstBusinessName && formik.errors.gstDetails?.gstBusinessName
                  ? 'is-invalid'
                  : formik.touched.gstDetails?.gstBusinessName && formik.values.gstDetails?.gstBusinessName
                  ? 'is-valid'
                  : ''
              }`}
              placeholder="Enter business name as per GST certificate"
            />
            <ErrorMessage
              name="gstDetails.gstBusinessName"
              component="div"
              className="clean-form-error"
            />
          </div>
        </div>

        <div className="row mt-3">
          <div className="col-12">
            <div className="alert alert-info">
              <i className="fas fa-info-circle me-2"></i>
              <strong>Note:</strong> GST registration is required for businesses with annual turnover 
              exceeding ₹20 lakhs (₹10 lakhs for special category states). Ensure the details match 
              your GST certificate exactly.
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GstDetailsForm;
