import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import PageHeader from "@components/PageHeader";


const Dashboard = () => {
  const [showModernPopup, setShowModernPopup] = useState(false);
  const navigate = useNavigate();

  return (
    <div className="dashboard-page">
      <PageHeader
        title="Dashboard"
        description="Welcome to your logistics management system."
      />

      <div className="dashboard-content p-4">
        <div className="row justify-content-center">
          <div className="col-md-8 text-center">
            <div className="card">
              <div className="card-body py-5">
                <h4 className="card-title mb-3">Ready to Get Started</h4>
                <p className="card-text text-muted mb-4">
                  Your dashboard is ready for customization. Add your content and features here.
                </p>
                <div className="d-flex gap-3 justify-content-center">
                  <button className="btn btn-primary">
                    Get Started
                  </button>
                  <button
                    className="btn btn-outline-secondary"
                    onClick={() => setShowModernPopup(true)}
                  >
                    Test Modern Popup
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modern Action Popup */}
     
    </div>
  );
};

export default Dashboard;
