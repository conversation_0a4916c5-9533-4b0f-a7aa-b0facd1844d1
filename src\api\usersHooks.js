import { useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "@constants/apiNamevariables";
import { baseAPI } from "./axiosInstance";

// API function to fetch comprehensive user profile
export const fetchComprehensiveProfile = async (userId) => {
  const response = await baseAPI.get(`/ComprehensiveProfile/${userId}`);
  return response.data;
};

// Hook to fetch users with advanced filtering and pagination
export const useGetUsersAdvanced = (params = {}, options = {}) => {
  const {
    pageNumber = 1,
    pageSize = 20,
    sortBy = "CreatedAt",
    sortDirection = "desc",
    searchTerm = "",
    userType = null,
    status = null,
    kycStatus = null,
    ...otherParams
  } = params;

  const queryParams = new URLSearchParams({
    pageNumber: pageNumber.toString(),
    pageSize: pageSize.toString(),
    sortBy,
    sortDirection,
    ...(searchTerm && { searchTerm }),
    ...(userType !== null && { userType: userType.toString() }),
    ...(status !== null && { status: status.toString() }),
    ...(kycStatus && { kycStatus }),
    ...otherParams,
  });

  return useQuery({
    queryKey: ["users-advanced", params],
    queryFn: async () => {
      const response = await baseAPI.get(
        `${API_ENDPOINTS.ADMIN_USERS_ADVANCED}?${queryParams.toString()}`
      );
      return response;
    },
    staleTime: 30 * 1000, // 30 seconds
    cacheTime: 5 * 60 * 1000, // 5 minutes
    keepPreviousData: true, // Keep previous data while fetching new data
    ...options,
  });
};

// User type labels for display
export const USER_TYPE_LABELS = {
  2: "Transport Company",
  3: "Broker",
  4: "Carrier",
  5: "Driver",
  7: "Shipper Company",
  8: "Shipper Individual",
};

// User type configurations with icons and colors
export const USER_TYPE_CONFIG = {
  2: {
    label: "Transport Company",
    icon: "FaTruck",
    color: "primary",
    description: "Transportation service providers",
  },
  3: {
    label: "Broker",
    icon: "FaHandshake",
    color: "success",
    description: "Freight brokers and intermediaries",
  },
  4: {
    label: "Carrier",
    icon: "FaShippingFast",
    color: "info",
    description: "Freight carriers and logistics",
  },
  7: {
    label: "Shipper Company",
    icon: "FaIndustry",
    color: "secondary",
    description: "Corporate shippers",
  },
  8: {
    label: "Shipper Individual",
    icon: "FaUserCircle",
    color: "dark",
    description: "Individual shippers",
  },
};

// User status labels
export const USER_STATUS_LABELS = {
  0: "Inactive",
  1: "Active",
  2: "Suspended",
  3: "Pending",
};

// KYC status labels
export const KYC_STATUS_LABELS = {
  NotStarted: "Not Started",
  InProgress: "In Progress",
  Submitted: "Submitted",
  Approved: "Approved",
  Rejected: "Rejected",
};

// Subscription status labels
export const SUBSCRIPTION_STATUS_LABELS = {
  None: "None",
  Active: "Active",
  Expired: "Expired",
  Cancelled: "Cancelled",
  Suspended: "Suspended",
};
