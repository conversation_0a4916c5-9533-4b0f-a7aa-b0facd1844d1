import React, { useState, useEffect } from "react";
import { Outlet, useNavigate, useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { resetUser } from "@store/userSlice";
import { useTheme } from "@contexts/ThemeContext";
import toast from "react-hot-toast";
import {
  FaPlus,
  FaBoxOpen,
  FaClipboardList,
  FaTruck,
  FaUser,
  FaDollarSign,
  FaChartBar,
} from "react-icons/fa";

// Import new components
import NewSidebar from "./Components/NewSidebar/index.jsx";
import NewTopbar from "./Components/NewTopbar/index.jsx";
import RightSidebar from "@components/RightSidebar";
import { RightSidebarProvider } from "@contexts/RightSidebarContext";
import ROUTES from "@constants/routes";

const MainLayout = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const { resetToDefaultTheme } = useTheme();

  // Get user data from Redux
  const { user, token } = useSelector((state) => state.user);

  // Function to get page configuration based on current route
  const getPageConfig = () => {
    const path = location.pathname;

    const pageConfigs = {
      "/": {
        title: "Dashboard",
        showSearch: false,
        showAddButton: false,
        showFilters: false,
        showExport: false,
        addButtonIcon: <FaPlus size={12} />,
      },
      "/dashboard": {
        title: "Dashboard",
        showSearch: false,
        showAddButton: false,
        showFilters: false,
        showExport: false,
        addButtonIcon: <FaPlus size={12} />,
      },
      "/shipments": {
        title: "Shipments Management",
        showSearch: true,
        showAddButton: true,
        showFilters: true,
        showExport: false,
        addButtonText: "Add Shipment",
        addButtonIcon: <FaBoxOpen size={12} />,
      },
      "/orders": {
        title: "Orders",
        showSearch: true,
        showAddButton: true,
        showFilters: true,
        showExport: true,
        addButtonText: "New Order",
        addButtonIcon: <FaClipboardList size={12} />,
      },
      "/fleet": {
        title: "Fleet Management",
        showSearch: true,
        showAddButton: true,
        showFilters: true,
        showExport: false,
        addButtonText: "Add Vehicle",
        addButtonIcon: <FaTruck size={12} />,
      },
      "/drivers": {
        title: "Drivers",
        showSearch: true,
        showAddButton: true,
        showFilters: true,
        showExport: true,
        addButtonText: "Add Driver",
        addButtonIcon: <FaUser size={12} />,
      },
      "/inventory": {
        title: "Inventory",
        showSearch: true,
        showAddButton: true,
        showFilters: true,
        showExport: true,
        addButtonText: "Add Item",
        addButtonIcon: <FaBoxOpen size={12} />,
      },
      "/analytics": {
        title: "Report & Analytics",
        showSearch: false,
        showAddButton: false,
        showFilters: true,
        showExport: true,
      },
      "/profile/settings": {
        title: "Settings",
        showSearch: false,
        showAddButton: false,
        showFilters: false,
        showExport: false,
      },
      "/billing": {
        title: "Billing & Payments",
        showSearch: true,
        showAddButton: true,
        showFilters: true,
        showExport: true,
        addButtonText: "New Invoice",
        addButtonIcon: <FaDollarSign size={12} />,
      },
      "/help": {
        title: "Help",
        showSearch: true,
        showAddButton: false,
        showFilters: false,
        showExport: false,
      },
      "/settings": {
        title: "Settings",
        showSearch: false,
        showAddButton: false,
        showFilters: false,
        showExport: false,
      },
      "/rate-calculator": {
        title: "Rate Calculator",
        showSearch: false,
        showAddButton: false,
        showFilters: false,
        showExport: false,
      },
      "/reports": {
        title: "Reports",
        showSearch: true,
        showAddButton: true,
        showFilters: true,
        showExport: true,
        addButtonText: "Generate Report",
        addButtonIcon: <FaChartBar size={12} />,
      },
    };

    return pageConfigs[path] || pageConfigs["/"];
  };

  const handleLogout = () => {
    dispatch(resetUser());
    resetToDefaultTheme(); // Reset theme to dark on logout
    toast.success("Logged out successfully");
    navigate(ROUTES.LOGIN);
  };

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const toggleMobileSidebar = () => {
    setMobileSidebarOpen(!mobileSidebarOpen);
  };

  const handleSearch = (searchTerm) => {
    console.log("Search:", searchTerm);
    // Implement search functionality here
  };

  const handleAddClick = () => {
    const config = getPageConfig();
    console.log(
      `Add button clicked for ${config.title}:`,
      config.addButtonText
    );
    // Implement add functionality based on current page
  };

  const handleFilterClick = () => {
    console.log("Filter button clicked");
    // Implement filter functionality
  };

  const handleExportClick = () => {
    console.log("Export button clicked");
    // Implement export functionality
  };

  return (
    <RightSidebarProvider>
      <div className="d-flex vh-100 w-100 position-relative new-main-layout">
        {/* New Sidebar */}
        <NewSidebar
          collapsed={sidebarCollapsed}
          mobileOpen={mobileSidebarOpen}
          onMobileClose={() => setMobileSidebarOpen(false)}
        />

        {/* Main Content Area */}
        <div className="flex-fill d-flex flex-column position-relative vh-100 new-main-content">
          {/* New Top Bar */}
          <NewTopbar
            {...getPageConfig()}
            onToggleSidebar={toggleSidebar}
            onToggleMobileSidebar={toggleMobileSidebar}
            onSearch={handleSearch}
            onAddClick={handleAddClick}
            onFilterClick={handleFilterClick}
            onExportClick={handleExportClick}
            onLogout={handleLogout}
          />

          {/* Page Content with Container */}
          <main className="flex-fill overflow-auto new-page-content">
            <div className="h-100 overflow-auto content-container">
              <Outlet />
            </div>
          </main>
        </div>

        {/* Right Sidebar */}
        <RightSidebar />
      </div>
    </RightSidebarProvider>
  );
};

export default MainLayout;
