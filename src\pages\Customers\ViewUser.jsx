import React, { memo, useMemo, useCallback, useState } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import {
  Fa<PERSON>ser,
  FaEnvelope,
  FaPhone,
  FaSpinner,
  FaExclamationTriangle,
  FaArrowLeft,
  FaBuilding,
  FaIdCard,
  FaIndustry,
  FaMapMarkerAlt,
  FaCalendarAlt,
  FaFileAlt,
  FaChartLine,
  FaEye,
  FaEyeSlash,
  FaEdit,
  FaCheck,
  FaTimes,
  FaUpload,
} from "react-icons/fa";
import { Button } from "reactstrap";
import { fetchComprehensiveProfile } from "@api/usersHooks";
import { USER_TYPE_LABELS } from "@constants/enum";
import { ProfileKYCForm } from "@components/ProfileKYC";

// Move static styles outside component to prevent recreation
const LOADING_CONTAINER_STYLE = { minHeight: "400px" };

// Memoized LoadingState component
const LoadingState = memo(() => (
  <div
    className="d-flex justify-content-center align-items-center"
    style={LOADING_CONTAINER_STYLE}
  >
    <div className="text-center">
      <FaSpinner className="fa-spin text-primary mb-3" size={32} />
      <h6 className="text-muted">Loading user profile...</h6>
    </div>
  </div>
));

LoadingState.displayName = "LoadingState";

// Memoized ErrorState component
const ErrorState = memo(({ error, onRetry }) => (
  <div
    className="d-flex justify-content-center align-items-center"
    style={LOADING_CONTAINER_STYLE}
  >
    <div className="text-center">
      <FaExclamationTriangle className="text-danger mb-3" size={32} />
      <h6 className="text-danger mb-3">Failed to load user profile</h6>
      <p className="text-muted mb-3">{error?.message || "An error occurred"}</p>
      <Button color="primary" onClick={onRetry}>
        Try Again
      </Button>
    </div>
  </div>
));

ErrorState.displayName = "ErrorState";

// Memoized UserHeader component - matches ProfileSettings header with action buttons
const UserHeader = memo(
  ({ userData, onBack, onEdit, onApprove, onReject, isLoading = false }) => {
    const displayName = useMemo(() => {
      return (
        userData?.displayName || `${userData?.firstName} ${userData?.lastName}`
      );
    }, [userData?.displayName, userData?.firstName, userData?.lastName]);

    return (
      <div className="d-flex align-items-center justify-content-between mb-4">
        <div className="d-flex align-items-center">
          <Button size="sm" className="me-3" onClick={onBack}>
            <FaArrowLeft size={14} />
          </Button>
          <div>
            <h4 className="mb-1">{displayName}</h4>
            <p className="text-muted mb-0">User Profile Details</p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="d-flex gap-2">
          <Button
            color="success"
            size="sm"
            onClick={onApprove}
            disabled={isLoading}
            className="d-flex align-items-center"
          >
            <FaCheck size={12} className="me-1" />
            Approve
          </Button>
          <Button
            color="danger"
            size="sm"
            onClick={onReject}
            disabled={isLoading}
            className="d-flex align-items-center"
          >
            <FaTimes size={12} className="me-1" />
            Reject
          </Button>
          <Button
            color="primary"
            size="sm"
            onClick={onEdit}
            disabled={isLoading}
            className="d-flex align-items-center"
          >
            <FaEdit size={12} className="me-1" />
            Edit
          </Button>
        </div>
      </div>
    );
  }
);

UserHeader.displayName = "UserHeader";

// Profile Header Card - matches TriTrackz-FE ProfileSettings design
const ProfileCard = memo(({ userData }) => {
  const displayName = useMemo(() => {
    return (
      userData?.displayName || `${userData?.firstName} ${userData?.lastName}`
    );
  }, [userData?.displayName, userData?.firstName, userData?.lastName]);

  const userTypeLabel = useMemo(() => {
    return USER_TYPE_LABELS[userData?.userType] || "User";
  }, [userData?.userType]);

  return (
    <div className="row mb-4">
      <div className="col-12">
        <div className="card border-0 shadow-sm">
          <div className="card-body p-4">
            <div className="row align-items-center">
              <div className="col-auto">
                <div
                  className="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center"
                  style={{ width: "80px", height: "80px", fontSize: "32px" }}
                >
                  {userData?.firstName?.charAt(0) || "U"}
                </div>
              </div>
              <div className="col">
                <h5 className="mb-1">{displayName}</h5>
                <p className="text-muted mb-2">{userTypeLabel}</p>
                <div className="d-flex align-items-center gap-3">
                  <span className="text-muted">
                    <FaEnvelope className="me-1" size={14} />
                    {userData?.email || "N/A"}
                  </span>
                  <span className="text-muted">
                    <FaPhone className="me-1" size={14} />
                    {userData?.phoneNumber || "N/A"}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

ProfileCard.displayName = "ProfileCard";

// Basic Details and Business Address Side by Side - matches TriTrackz-FE ProfileSettings design
const BasicDetailsAndAddressCards = memo(({ userData }) => {
  return (
    <div className="row mb-3">
      <div className="col-lg-6 col-md-12 mb-3">
        <div className="card border-0 shadow-sm compact-details-card h-100">
          <div className="card-header bg-transparent border-bottom py-3">
            <div className="d-flex align-items-center">
              <FaUser className="text-primary me-2" size={16} />
              <h6 className="mb-0 fw-semibold">Basic Details</h6>
            </div>
          </div>
          <div className="card-body p-4">
            <div className="row g-3">
              <div className="col-md-6">
                <div className="compact-field-group">
                  <label className="compact-field-label">First Name</label>
                  <div className="compact-field-value">
                    <span>{userData?.firstName || "N/A"}</span>
                  </div>
                </div>
              </div>
              <div className="col-md-6">
                <div className="compact-field-group">
                  <label className="compact-field-label">Last Name</label>
                  <div className="compact-field-value">
                    <span>{userData?.lastName || "N/A"}</span>
                  </div>
                </div>
              </div>
              <div className="col-md-6">
                <div className="compact-field-group">
                  <label className="compact-field-label">Display Name</label>
                  <div className="compact-field-value">
                    <span>{userData?.displayName || "N/A"}</span>
                  </div>
                </div>
              </div>
              <div className="col-md-6">
                <div className="compact-field-group">
                  <label className="compact-field-label">User Type</label>
                  <div className="compact-field-value">
                    <span>{USER_TYPE_LABELS[userData?.userType] || "N/A"}</span>
                  </div>
                </div>
              </div>
              <div className="col-12">
                <div className="compact-field-group">
                  <label className="compact-field-label">Email</label>
                  <div className="compact-field-value">
                    <span>{userData?.email || "N/A"}</span>
                  </div>
                </div>
              </div>
              <div className="col-12">
                <div className="compact-field-group">
                  <label className="compact-field-label">Phone Number</label>
                  <div className="compact-field-value">
                    <span>{userData?.phoneNumber || "N/A"}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Business Address Card - Right Side */}
      <div className="col-lg-6 col-md-12">
        <div className="card border-0 shadow-sm compact-details-card h-100">
          <div className="card-header bg-transparent border-bottom py-3">
            <div className="d-flex align-items-center">
              <FaMapMarkerAlt className="text-primary me-2" size={16} />
              <h6 className="mb-0 fw-semibold">Business Address</h6>
            </div>
          </div>
          <div className="card-body p-4 d-flex align-items-center justify-content-center">
            <div className="address-format-container">
              <div className="formatted-address">
                <div className="address-line">
                  {userData?.address || "Street Address Not Available"}
                </div>
                <div className="address-line">
                  {[userData?.city, userData?.state, userData?.postalCode]
                    .filter(Boolean)
                    .join(", ") || "City, State, Postal Code Not Available"}
                </div>
                <div className="address-line">
                  {userData?.country || "Country Not Available"}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

BasicDetailsAndAddressCards.displayName = "BasicDetailsAndAddressCards";

// Company Details Card - Full Width - matches TriTrackz-FE ProfileSettings design
const CompanyDetailsCard = memo(({ userData }) => {
  const isCompanyType = () => {
    return userData?.userType === 1 || userData?.userType === 2; // Company or Transport Company
  };

  if (!isCompanyType()) return null;

  return (
    <div className="row mb-3">
      <div className="col-12">
        <div className="card border-0 shadow-sm compact-details-card">
          <div className="card-header bg-transparent border-bottom py-3">
            <div className="d-flex align-items-center">
              <FaBuilding className="text-primary me-2" size={16} />
              <h6 className="mb-0 fw-semibold">Company Details</h6>
            </div>
          </div>
          <div className="card-body p-4">
            <div className="row g-3">
              <div className="col-lg-2 col-md-4 col-sm-6">
                <div className="compact-field-group">
                  <label className="compact-field-label">Company Name</label>
                  <div className="compact-field-value">
                    <span>{userData?.companyName || "N/A"}</span>
                  </div>
                </div>
              </div>
              <div className="col-lg-2 col-md-4 col-sm-6">
                <div className="compact-field-group">
                  <label className="compact-field-label">Business Type</label>
                  <div className="compact-field-value">
                    <span>{userData?.businessType || "N/A"}</span>
                  </div>
                </div>
              </div>
              <div className="col-lg-2 col-md-4 col-sm-6">
                <div className="compact-field-group">
                  <label className="compact-field-label">Legal Name</label>
                  <div className="compact-field-value">
                    <span>{userData?.legalName || "N/A"}</span>
                  </div>
                </div>
              </div>
              <div className="col-lg-2 col-md-4 col-sm-6">
                <div className="compact-field-group">
                  <label className="compact-field-label">Trade Name</label>
                  <div className="compact-field-value">
                    <span>{userData?.tradeName || "N/A"}</span>
                  </div>
                </div>
              </div>
              <div className="col-lg-2 col-md-4 col-sm-6">
                <div className="compact-field-group">
                  <label className="compact-field-label">Company Email</label>
                  <div className="compact-field-value">
                    <span>{userData?.companyEmail || "N/A"}</span>
                  </div>
                </div>
              </div>
              <div className="col-lg-2 col-md-4 col-sm-6">
                <div className="compact-field-group">
                  <label className="compact-field-label">Company Phone</label>
                  <div className="compact-field-value">
                    <span>{userData?.companyPhone || "N/A"}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

CompanyDetailsCard.displayName = "CompanyDetailsCard";

// GST Details Card - Full Width - matches TriTrackz-FE ProfileSettings design
const GSTDetailsCard = memo(({ userData }) => {
  const isCompanyType = () => {
    return userData?.userType === 1 || userData?.userType === 2; // Company or Transport Company
  };

  if (!isCompanyType()) return null;

  const formatDate = useCallback((dateString) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  }, []);

  return (
    <div className="row mb-3">
      <div className="col-12">
        <div className="card border-0 shadow-sm compact-details-card">
          <div className="card-header bg-transparent border-bottom py-3">
            <div className="d-flex align-items-center">
              <FaFileAlt className="text-primary me-2" size={16} />
              <h6 className="mb-0 fw-semibold">GST Details</h6>
            </div>
          </div>
          <div className="card-body p-4">
            <div className="row g-3">
              <div className="col-lg-3 col-md-6 col-sm-6">
                <div className="compact-field-group">
                  <label className="compact-field-label">GST Number</label>
                  <div className="compact-field-value">
                    <span>{userData?.gstNumber || "N/A"}</span>
                  </div>
                </div>
              </div>
              <div className="col-lg-3 col-md-6 col-sm-6">
                <div className="compact-field-group">
                  <label className="compact-field-label">
                    GST Registration Date
                  </label>
                  <div className="compact-field-value">
                    <span>{formatDate(userData?.gstRegistrationDate)}</span>
                  </div>
                </div>
              </div>
              <div className="col-lg-3 col-md-6 col-sm-6">
                <div className="compact-field-group">
                  <label className="compact-field-label">
                    GST Registration State
                  </label>
                  <div className="compact-field-value">
                    <span>{userData?.gstRegistrationState || "N/A"}</span>
                  </div>
                </div>
              </div>
              <div className="col-lg-3 col-md-6 col-sm-6">
                <div className="compact-field-group">
                  <label className="compact-field-label">CIN Number</label>
                  <div className="compact-field-value">
                    <span>{userData?.cinNumber || "N/A"}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

GSTDetailsCard.displayName = "GSTDetailsCard";

// Physical Card Style Layout - PAN, Aadhaar, Bank Cards - matches TriTrackz-FE design
const PhysicalCardsSection = memo(({ userData }) => {
  const [showNumbers, setShowNumbers] = useState({
    pan: false,
    aadhaar: false,
    account: false,
  });

  const toggleNumberVisibility = useCallback((type) => {
    setShowNumbers((prev) => ({
      ...prev,
      [type]: !prev[type],
    }));
  }, []);

  const formatDate = useCallback((dateString) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  }, []);

  const maskNumber = useCallback((number, show) => {
    if (!number) return "N/A";
    if (show) return number;
    return number.replace(/./g, "*");
  }, []);

  return (
    <div className="row mb-3">
      <div className="col-12">
        <div className="card border-0 shadow-sm compact-details-card">
          <div className="card-header bg-transparent border-bottom py-3">
            <div className="d-flex align-items-center">
              <FaIdCard className="text-primary me-2" size={16} />
              <h6 className="mb-0 fw-semibold">Identity & Financial Cards</h6>
            </div>
          </div>
          <div className="card-body p-4">
            <div className="row g-3">
              {/* PAN Card */}
              <div className="col-lg-4 col-md-6 col-sm-12">
                <div className="compact-physical-card pan-card">
                  <div className="compact-card-header">
                    <span className="card-title">PAN CARD</span>
                  </div>
                  <div className="compact-card-content">
                    <div className="row g-2">
                      <div className="col-6">
                        <div className="compact-card-field">
                          <span className="field-label">Name</span>
                          <span className="field-value">
                            {userData?.nameAsPerPan || "N/A"}
                          </span>
                        </div>
                      </div>
                      <div className="col-6">
                        <div className="compact-card-field">
                          <span className="field-label">Father's Name</span>
                          <span className="field-value">
                            {userData?.fatherOrHusbandNameInPan || "N/A"}
                          </span>
                        </div>
                      </div>
                      <div className="col-6">
                        <div className="compact-card-field">
                          <span className="field-label">Date of Birth</span>
                          <span className="field-value">
                            {formatDate(userData?.dateOfBirthInPan)}
                          </span>
                        </div>
                      </div>
                      <div className="col-6">
                        <div className="compact-card-field">
                          <span className="field-label">PAN Number</span>
                          <div className="d-flex align-items-center">
                            <span className="field-value me-2">
                              {maskNumber(userData?.panNumber, showNumbers.pan)}
                            </span>
                            <button
                              type="button"
                              className="btn btn-sm btn-outline-secondary p-1"
                              onClick={() => toggleNumberVisibility("pan")}
                              style={{ fontSize: "10px", lineHeight: 1 }}
                            >
                              {showNumbers.pan ? (
                                <FaEyeSlash size={10} />
                              ) : (
                                <FaEye size={10} />
                              )}
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Aadhaar Card */}
              <div className="col-lg-4 col-md-6 col-sm-12">
                <div className="compact-physical-card aadhaar-card">
                  <div className="compact-card-header">
                    <span className="card-title">AADHAAR CARD</span>
                  </div>
                  <div className="compact-card-content">
                    <div className="row g-2">
                      <div className="col-6">
                        <div className="compact-card-field">
                          <span className="field-label">Name</span>
                          <span className="field-value">
                            {userData?.nameAsPerAadhaar || "N/A"}
                          </span>
                        </div>
                      </div>
                      <div className="col-6">
                        <div className="compact-card-field">
                          <span className="field-label">Father's Name</span>
                          <span className="field-value">
                            {userData?.fatherOrHusbandNameInAadhaar || "N/A"}
                          </span>
                        </div>
                      </div>
                      <div className="col-6">
                        <div className="compact-card-field">
                          <span className="field-label">Date of Birth</span>
                          <span className="field-value">
                            {formatDate(userData?.dateOfBirthInAadhaar)}
                          </span>
                        </div>
                      </div>
                      <div className="col-6">
                        <div className="compact-card-field">
                          <span className="field-label">Aadhaar Number</span>
                          <div className="d-flex align-items-center">
                            <span className="field-value me-2">
                              {maskNumber(
                                userData?.aadharNumber,
                                showNumbers.aadhaar
                              )}
                            </span>
                            <button
                              type="button"
                              className="btn btn-sm btn-outline-secondary p-1"
                              onClick={() => toggleNumberVisibility("aadhaar")}
                              style={{ fontSize: "10px", lineHeight: 1 }}
                            >
                              {showNumbers.aadhaar ? (
                                <FaEyeSlash size={10} />
                              ) : (
                                <FaEye size={10} />
                              )}
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Bank Account Card */}
              <div className="col-lg-4 col-md-6 col-sm-12">
                <div className="compact-physical-card bank-card">
                  <div className="compact-card-header">
                    <span className="card-title">BANK ACCOUNT</span>
                  </div>
                  <div className="compact-card-content">
                    <div className="row g-2">
                      <div className="col-6">
                        <div className="compact-card-field">
                          <span className="field-label">Account Holder</span>
                          <span className="field-value">
                            {userData?.accountHolderName || "N/A"}
                          </span>
                        </div>
                      </div>
                      <div className="col-6">
                        <div className="compact-card-field">
                          <span className="field-label">Bank Name</span>
                          <span className="field-value">
                            {userData?.bankName || "N/A"}
                          </span>
                        </div>
                      </div>
                      <div className="col-6">
                        <div className="compact-card-field">
                          <span className="field-label">IFSC Code</span>
                          <span className="field-value">
                            {userData?.ifscCode || "N/A"}
                          </span>
                        </div>
                      </div>
                      <div className="col-6">
                        <div className="compact-card-field">
                          <span className="field-label">Account Number</span>
                          <div className="d-flex align-items-center">
                            <span className="field-value me-2">
                              {maskNumber(
                                userData?.accountNumber,
                                showNumbers.account
                              )}
                            </span>
                            <button
                              type="button"
                              className="btn btn-sm btn-outline-secondary p-1"
                              onClick={() => toggleNumberVisibility("account")}
                              style={{ fontSize: "10px", lineHeight: 1 }}
                            >
                              {showNumbers.account ? (
                                <FaEyeSlash size={10} />
                              ) : (
                                <FaEye size={10} />
                              )}
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

PhysicalCardsSection.displayName = "PhysicalCardsSection";

// Documents Section - matches TriTrackz-FE ProfileSettings design
const DocumentsSection = memo(({ userData }) => {
  const getFileIconAndColor = useCallback((fileName) => {
    if (!fileName) return { icon: FaFileAlt, color: "#6c757d" };

    const extension = fileName.toLowerCase().split(".").pop();
    switch (extension) {
      case "pdf":
        return { icon: FaFileAlt, color: "#dc3545" };
      case "jpg":
      case "jpeg":
      case "png":
      case "gif":
        return { icon: FaFileAlt, color: "#28a745" };
      case "doc":
      case "docx":
        return { icon: FaFileAlt, color: "#007bff" };
      default:
        return { icon: FaFileAlt, color: "#6c757d" };
    }
  }, []);

  const getRequiredDocumentsByUserType = useCallback((userType) => {
    const baseDocuments = [
      { key: "PanCard", name: "PAN Card" },
      { key: "AadharCard", name: "Aadhaar Card" },
      { key: "BankStatement", name: "Bank Statement" },
    ];

    if (userType === 1 || userType === 2) {
      // Company types
      return [
        ...baseDocuments,
        { key: "GstCertificate", name: "GST Certificate" },
        { key: "TradeLicense", name: "Trade License" },
        { key: "BusinessLicense", name: "Business License" },
      ];
    }

    return baseDocuments;
  }, []);

  const userDocuments = userData?.documents || [];
  const userType = userData?.userType;

  return (
    <div className="row mb-3">
      <div className="col-12">
        <div className="card border-0 shadow-sm compact-details-card">
          <div className="card-header bg-transparent border-bottom py-3">
            <div className="d-flex align-items-center">
              <FaFileAlt className="text-primary me-2" size={16} />
              <h6 className="mb-0 fw-semibold">Documents</h6>
              {userType !== 3 && ( // Don't show badge for Broker (userType 3)
                <span className="badge bg-info text-dark ms-auto">
                  {USER_TYPE_LABELS[userType] || "User"} Documents
                </span>
              )}
            </div>
          </div>
          <div className="card-body p-4">
            <div className="documents-list">
              {getRequiredDocumentsByUserType(userType).map((requiredDoc) => {
                const uploadedDoc = userDocuments.find(
                  (doc) => doc.documentType === requiredDoc.key
                );

                return (
                  <div key={requiredDoc.key} className="document-row">
                    {/* Document Icon */}
                    <div
                      className={`document-icon ${
                        !uploadedDoc ? "missing" : ""
                      }`}
                    >
                      {uploadedDoc ? (
                        (() => {
                          const { icon: FileIcon, color } = getFileIconAndColor(
                            uploadedDoc.fileName
                          );
                          return (
                            <FileIcon
                              size={20}
                              style={{ color }}
                              title={uploadedDoc.fileName}
                            />
                          );
                        })()
                      ) : (
                        <FaFileAlt className="text-muted" size={18} />
                      )}
                    </div>

                    {/* Document Name */}
                    <div className="document-name">
                      <h6>{requiredDoc.name}</h6>
                    </div>

                    {/* Document Status */}
                    <div className="document-status">
                      {uploadedDoc ? (
                        <span className="badge bg-success">
                          Document Uploaded
                        </span>
                      ) : (
                        <span className="badge bg-warning text-dark">
                          Not Uploaded
                        </span>
                      )}
                    </div>

                    {/* Download Button */}
                    <div className="document-actions">
                      {uploadedDoc ? (
                        <Button
                          color="primary"
                          size="sm"
                          className="download-btn"
                          title="Download Document"
                        >
                          Download
                        </Button>
                      ) : (
                        <Button
                          color="secondary"
                          size="sm"
                          disabled
                          className="download-btn"
                          title="Document not available"
                        >
                          Upload Required
                        </Button>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

DocumentsSection.displayName = "DocumentsSection";

// Full Width Account Information Card - matches TriTrackz-FE design
const AccountInformationCard = memo(({ userData }) => {
  const formatDate = useCallback((dateString) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  }, []);

  return (
    <div className="row mb-3">
      <div className="col-12">
        <div className="card border-0 shadow-sm compact-details-card">
          <div className="card-header bg-transparent border-bottom py-3">
            <div className="d-flex align-items-center">
              <FaChartLine className="text-primary me-2" size={16} />
              <h6 className="mb-0 fw-semibold">Account Information</h6>
            </div>
          </div>
          <div className="card-body p-4">
            <div className="row g-3">
              <div className="col-lg-3 col-md-6 col-sm-6">
                <div className="compact-field-group">
                  <label className="compact-field-label">Account Created</label>
                  <div className="compact-field-value">
                    <span>{formatDate(userData?.createdAt)}</span>
                  </div>
                </div>
              </div>
              <div className="col-lg-3 col-md-6 col-sm-6">
                <div className="compact-field-group">
                  <label className="compact-field-label">Last Updated</label>
                  <div className="compact-field-value">
                    <span>{formatDate(userData?.updatedAt)}</span>
                  </div>
                </div>
              </div>
              <div className="col-lg-3 col-md-6 col-sm-6">
                <div className="compact-field-group">
                  <label className="compact-field-label">Last Login</label>
                  <div className="compact-field-value">
                    <span>{formatDate(userData?.lastLoginAt)}</span>
                  </div>
                </div>
              </div>
              <div className="col-lg-3 col-md-6 col-sm-6">
                <div className="compact-field-group">
                  <label className="compact-field-label">KYC Status</label>
                  <div className="compact-field-value">
                    <span
                      className={`badge ${
                        userData?.kycStatus === "Approved"
                          ? "bg-success"
                          : userData?.kycStatus === "Pending"
                          ? "bg-warning"
                          : userData?.kycStatus === "Rejected"
                          ? "bg-danger"
                          : "bg-secondary"
                      }`}
                    >
                      {userData?.kycStatus || "Not Started"}
                    </span>
                  </div>
                </div>
              </div>
              <div className="col-lg-3 col-md-6 col-sm-6">
                <div className="compact-field-group">
                  <label className="compact-field-label">User Type</label>
                  <div className="compact-field-value">
                    <span>
                      {USER_TYPE_LABELS[userData?.userType] || "User"}
                    </span>
                  </div>
                </div>
              </div>
              <div className="col-lg-3 col-md-6 col-sm-6">
                <div className="compact-field-group">
                  <label className="compact-field-label">Status</label>
                  <div className="compact-field-value">
                    <span
                      className={`badge ${
                        userData?.isActive ? "bg-success" : "bg-danger"
                      }`}
                    >
                      {userData?.isActive ? "Active" : "Inactive"}
                    </span>
                  </div>
                </div>
              </div>
              <div className="col-lg-3 col-md-6 col-sm-6">
                <div className="compact-field-group">
                  <label className="compact-field-label">
                    Profile Complete
                  </label>
                  <div className="compact-field-value">
                    <span
                      className={`badge ${
                        userData?.isComplete ? "bg-success" : "bg-warning"
                      }`}
                    >
                      {userData?.isComplete ? "Complete" : "Incomplete"}
                    </span>
                  </div>
                </div>
              </div>
              <div className="col-lg-3 col-md-6 col-sm-6">
                <div className="compact-field-group">
                  <label className="compact-field-label">
                    Total Business Value
                  </label>
                  <div className="compact-field-value">
                    <span>
                      ₹{userData?.totalBusinessValue?.toLocaleString() || "0"}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

AccountInformationCard.displayName = "AccountInformationCard";

const ViewUser = () => {
  const { userId } = useParams();
  const navigate = useNavigate();
  const [isEditing, setIsEditing] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);

  // Memoize the back handler to prevent recreation
  const handleBack = useCallback(() => {
    navigate("/customers");
  }, [navigate]);

  // Handle edit mode toggle
  const handleEditClick = useCallback(() => {
    setIsEditing(true);
  }, []);

  const handleCancelEdit = useCallback(() => {
    setIsEditing(false);
  }, []);

  const handleSaveSuccess = useCallback((refetchFn) => {
    setIsEditing(false);
    refetchFn(); // Refresh the profile data
  }, []);

  // Handle approve action
  const handleApprove = useCallback(async () => {
    setActionLoading(true);
    try {
      // TODO: Implement approve API call
      console.log("Approving user:", userId);
      // await approveUser(userId);
      alert("User approved successfully!");
    } catch (error) {
      console.error("Error approving user:", error);
      alert("Failed to approve user. Please try again.");
    } finally {
      setActionLoading(false);
    }
  }, [userId]);

  // Handle reject action
  const handleReject = useCallback(async () => {
    setActionLoading(true);
    try {
      // TODO: Implement reject API call
      console.log("Rejecting user:", userId);
      // await rejectUser(userId);
      alert("User rejected successfully!");
    } catch (error) {
      console.error("Error rejecting user:", error);
      alert("Failed to reject user. Please try again.");
    } finally {
      setActionLoading(false);
    }
  }, [userId]);

  // Fetch user data with optimized caching
  const {
    data: userData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["comprehensiveProfile", userId],
    queryFn: () => fetchComprehensiveProfile(userId),
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  // Memoize retry handler
  const handleRetry = useCallback(() => {
    refetch();
  }, [refetch]);

  if (isLoading) {
    return (
      <div className="container-fluid">
        <LoadingState />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container-fluid">
        <ErrorState error={error} onRetry={handleRetry} />
      </div>
    );
  }

  // If in edit mode, show the ProfileKYCForm
  if (isEditing) {
    return (
      <div className="container-fluid py-4">
        <div className="row mb-4">
          <div className="col-12">
            <div className="d-flex align-items-center mb-3">
              <button
                className="btn btn-link p-0 me-3 text-decoration-none"
                onClick={handleCancelEdit}
                style={{ color: "inherit" }}
              >
                <FaArrowLeft size={16} />
              </button>
              <div>
                <h4 className="mb-1">Edit User Profile</h4>
                <p className="text-muted mb-0">
                  Update user account information
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="row">
          <div className="col-12">
            <ProfileKYCForm
              initialData={userData}
              isEditMode={true}
              onSuccess={() => handleSaveSuccess(refetch)}
              onCancel={handleCancelEdit}
              hideHeader={true}
            />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container-fluid py-4">
      {/* User Header with Action Buttons */}
      <UserHeader
        userData={userData}
        onBack={handleBack}
        onEdit={handleEditClick}
        onApprove={handleApprove}
        onReject={handleReject}
        isLoading={actionLoading}
      />

      {/* Profile Header Card */}
      <ProfileCard userData={userData} />

      {/* Basic Details and Business Address Side by Side */}
      <BasicDetailsAndAddressCards userData={userData} />

      {/* Company Details Card - Full Width */}
      <CompanyDetailsCard userData={userData} />

      {/* GST Details Card - Full Width */}
      <GSTDetailsCard userData={userData} />

      {/* Physical Card Style Layout - PAN, Aadhaar, Bank Cards */}
      <PhysicalCardsSection userData={userData} />

      {/* Documents Section */}
      <DocumentsSection userData={userData} />

      {/* Full Width Account Information Card */}
      <AccountInformationCard userData={userData} />
    </div>
  );
};

export default memo(ViewUser);
