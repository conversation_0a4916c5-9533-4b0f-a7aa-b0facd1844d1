import React, {
  useState,
  useEffect,
  useRef,
  useMemo,
  useCallback,
} from "react";
import {
  <PERSON>a<PERSON><PERSON>,
  <PERSON>a<PERSON>ell,
  <PERSON>a<PERSON>ser,
  FaSignOutAlt,
  FaSun,
  FaMoon,
} from "react-icons/fa";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { useTheme } from "@contexts/ThemeContext";
import { getDisplayName, getUserRoleLabel } from "@utils/profileUtils";
import { Avatar } from "@components/Common";
import ROUTES from "@constants/routes";

const NewTopbar = ({
  pageTitle = "Dashboard",
  onToggleSidebar,
  onToggleMobileSidebar,
  onLogout,
}) => {
  const [showNotifications, setShowNotifications] = useState(false);
  const [showProfileMenu, setShowProfileMenu] = useState(false);

  const { toggleTheme, isDark } = useTheme();
  const { user } = useSelector((state) => state.user);
  const navigate = useNavigate();

  // Memoize display name and role to prevent recalculation
  const displayName = useMemo(() => getDisplayName(user), [user]);
  const userRole = useMemo(() => getUserRoleLabel(user), [user]);
  console.log("userRole", user);
  const notificationRef = useRef(null);
  const profileRef = useRef(null);

  // Memoize event handlers to prevent unnecessary re-renders
  const toggleNotifications = useCallback(() => {
    console.log("Toggling notifications:", !showNotifications);
    setShowNotifications(!showNotifications);
    setShowProfileMenu(false);
  }, [showNotifications]);

  const toggleProfileMenu = useCallback(() => {
    console.log("Toggling profile menu:", !showProfileMenu);
    setShowProfileMenu(!showProfileMenu);
    setShowNotifications(false);
  }, [showProfileMenu]);

  const handleProfileSettingsClick = useCallback(() => {
    navigate(ROUTES.PROFILE_SETTINGS);
    setShowProfileMenu(false);
  }, [navigate]);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        notificationRef.current &&
        !notificationRef.current.contains(event.target)
      ) {
        setShowNotifications(false);
      }
      if (profileRef.current && !profileRef.current.contains(event.target)) {
        setShowProfileMenu(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="new-topbar">
      <div className="topbar-left">
        {/* Mobile Menu Toggle */}
        <button
          className="mobile-menu-btn d-lg-none"
          onClick={onToggleMobileSidebar}
        >
          <FaBars size={16} />
        </button>

        {/* Desktop Sidebar Toggle */}
        <button
          className="sidebar-toggle-btn d-none d-lg-block"
          onClick={onToggleSidebar}
        >
          <FaBars size={16} />
        </button>
      </div>

      <div className="topbar-right">
        {/* Notifications */}
        <div className="profile-info d-none d-md-block">
          <span className="profile-role">{userRole}</span>
        </div>
        <div className="notification-wrapper" ref={notificationRef}>
          <button className="notification-btn" onClick={toggleNotifications}>
            <FaBell size={16} />
            <span className="notification-badge">3</span>
          </button>

          {showNotifications && (
            <div
              className="notification-dropdown"
              style={{
                position: "absolute",
                top: "100%",
                right: "0",
                background: "#1a365d",
                borderRadius: "12px",
                width: "320px",
                zIndex: 9999,
                marginTop: "8px",
              }}
            >
              <div className="notification-header">
                <h6>Notifications</h6>
              </div>
              <div className="notification-list">
                <div className="notification-item text-center py-4">
                  <div className="notification-content">
                    <p className="text-muted">No new notifications</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Profile Avatar */}
        <div className="profile-wrapper" ref={profileRef}>
          <button className="profile-btn" onClick={toggleProfileMenu}>
            <Avatar
              name={displayName}
              size="medium"
              className="profile-avatar"
              showBorder={true}
            />
          </button>

          {showProfileMenu && (
            <div
              className="profile-dropdown"
              style={{
                position: "absolute",
                top: "100%",
                right: "0",
                background: "#1a365d",

                borderRadius: "12px",
                width: "280px",
                zIndex: 9999,
                marginTop: "8px",
              }}
            >
              <div className="profile-dropdown-header">
                <Avatar
                  name={displayName}
                  size="medium"
                  className="profile-avatar-large"
                  showBorder={true}
                />
                <div className="profile-details">
                  <h6>{displayName}</h6>
                  <p>{userRole}</p>
                </div>
              </div>
              <div className="profile-menu">
                <div
                  className="profile-menu-item"
                  onClick={handleProfileSettingsClick}
                  style={{ cursor: "pointer" }}
                >
                  <FaUser size={14} />
                  <span>Profile Settings</span>
                </div>
                <div
                  className="profile-menu-item theme-menu-item"
                  onClick={toggleTheme}
                >
                  {isDark ? <FaSun size={14} /> : <FaMoon size={14} />}
                  <span>{isDark ? "Light Mode" : "Dark Mode"}</span>
                </div>
                <a href="#" className="profile-menu-item" onClick={onLogout}>
                  <FaSignOutAlt size={14} />
                  <span>Logout</span>
                </a>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default NewTopbar;
