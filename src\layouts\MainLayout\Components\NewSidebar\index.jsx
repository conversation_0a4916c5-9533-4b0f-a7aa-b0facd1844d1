import React, { useMemo } from "react";
import { Link, useLocation } from "react-router-dom";
import { useSelector } from "react-redux";
import { FaHome, FaCog, FaUser, FaChartBar, FaBoxOpen } from "react-icons/fa";
import { getDisplayName, getUserRoleLabel } from "@utils/profileUtils";
import { Avatar } from "@components/Common";
import ROUTES from "@constants/routes";

const NewSidebar = ({ collapsed, mobileOpen, onMobileClose }) => {
  const location = useLocation();
  const { user } = useSelector((state) => state.user);

  // Memoize display name and role to prevent recalculation
  const displayName = useMemo(() => getDisplayName(user), [user]);
  const userRole = useMemo(() => getUserRoleLabel(user), [user]);

  const isActive = (route) => location.pathname === route;

  const menuItems = [
    {
      id: "dashboard",
      label: "Dashboard",
      icon: <FaHome size={16} />,
      route: ROUTES.DASHBOARD,
      active: isActive(ROUTES.DASHBOARD),
    },
    {
      id: "customers",
      label: "Customers",
      icon: <FaUser size={16} />,
      route: ROUTES.CUSTOMERS,
      active: isActive(ROUTES.CUSTOMERS),
    },
    {
      id: "shipments",
      label: "Shipments",
      icon: <FaBoxOpen size={16} />,
      route: "/shipments",
      active: isActive("/shipments"),
    },
    {
      id: "reports",
      label: "Reports",
      icon: <FaChartBar size={16} />,
      route: "/reports",
      active: isActive("/reports"),
    },
  ];

  const bottomMenuItems = [
    {
      id: "settings",
      label: "Settings",
      icon: <FaCog size={16} />,
      route: "/settings",
      active: isActive("/settings"),
    },
  ];

  return (
    <>
      {/* Mobile Backdrop */}
      {mobileOpen && (
        <div className="new-sidebar-backdrop" onClick={onMobileClose}></div>
      )}

      {/* Sidebar */}
      <div
        className={`new-sidebar ${collapsed ? "collapsed" : ""} ${
          mobileOpen ? "mobile-open" : ""
        }`}
      >
        {/* Logo Section */}
        <div className="new-sidebar-header">
          <div className="new-sidebar-logo">
            <div className="logo-icon">
              <span>🚚</span>
            </div>
            {!collapsed && (
              <div className="logo-text ">
                <h5 className="text-white">Tritrackz</h5>
              </div>
            )}
          </div>
        </div>

        {/* Main Menu */}
        <div className="new-sidebar-menu">
          <nav className="menu-nav">
            {menuItems.map((item) => (
              <Link
                key={item.id}
                to={item.route}
                className={`menu-item ${item.active ? "active" : ""}`}
                onClick={() => mobileOpen && onMobileClose()}
              >
                <div className="menu-icon">{item.icon}</div>
                {!collapsed && <span className="menu-label">{item.label}</span>}
              </Link>
            ))}
          </nav>
        </div>

        {/* Bottom Menu */}
        <div className="new-sidebar-bottom">
          <nav className="bottom-nav">
            {bottomMenuItems.map((item) => (
              <Link
                key={item.id}
                to={item.route}
                className={`menu-item ${item.active ? "active" : ""}`}
                onClick={() => mobileOpen && onMobileClose()}
              >
                <div className="menu-icon">{item.icon}</div>
                {!collapsed && <span className="menu-label">{item.label}</span>}
              </Link>
            ))}
          </nav>

          {/* User Profile */}
          {!collapsed && (
            <div className="user-profile">
              <Avatar name={displayName} size="small" className="user-avatar" />
              <div className="user-info">
                <div className="user-name">{displayName}</div>
                <div className="user-role">{userRole}</div>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default NewSidebar;
