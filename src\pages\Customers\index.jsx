import React, { useMemo, useState, memo, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import DataTable from "react-data-table-component";
import { Avatar } from "@components/Common";
import PageHeader from "@components/PageHeader";
import {
  Button,
  Dropdown,
  DropdownToggle,
  DropdownMenu,
  DropdownItem,
  Input,
  Label,
  FormGroup,
  Row,
  Col,
  Card,
  CardBody,
  Collapse,
} from "reactstrap";
import {
  FaUser,
  FaPhone,
  FaMapMarkerAlt,
  FaSpinner,
  FaSearch,
  FaFilter,
  FaDownload,
  FaTruck,
  FaHandshake,
  FaShippingFast,
  FaIndustry,
  FaUserCircle,
  FaSync,
  FaEye,
  FaTimes,
  FaClock,
  FaBuilding,
} from "react-icons/fa";
import {
  useGetUsersAdvanced,
  USER_TYPE_LABELS,
  USER_TYPE_CONFIG,
  USER_STATUS_LABELS,
  KYC_STATUS_LABELS,
  SUBSCRIPTION_STATUS_LABELS,
} from "@api/usersHooks";

// Static constants moved outside component to prevent recreation
const DEFAULT_FILTERS = {
  status: "",
  kycStatus: "",
  subscriptionStatus: "",
  dateFrom: "",
  dateTo: "",
  city: "",
  state: "",
};

// Static table custom styles to prevent recreation
const TABLE_CUSTOM_STYLES = {
  table: {
    style: {
      backgroundColor: "transparent",
    },
  },
  headRow: {
    style: {
      backgroundColor: "var(--bg-secondary)",
      borderBottom: "1px solid var(--border-primary)",
    },
  },
  headCells: {
    style: {
      color: "var(--text-primary)",
      fontWeight: "600",
      fontSize: "14px",
    },
  },
  rows: {
    style: {
      backgroundColor: "transparent",
      borderBottom: "1px solid var(--border-secondary)",
      "&:hover": {
        backgroundColor: "var(--bg-card)",
      },
    },
  },
  cells: {
    style: {
      color: "var(--text-secondary)",
      fontSize: "14px",
    },
  },
  pagination: {
    style: {
      backgroundColor: "var(--bg-secondary)",
      borderTop: "1px solid var(--border-primary)",
      color: "var(--text-primary)",
    },
    pageButtonsStyle: {
      color: "var(--text-primary)",
      backgroundColor: "transparent",
      border: "none",
      "&:hover": {
        backgroundColor: "var(--bg-card)",
      },
      "& svg": {
        fill: "var(--text-primary)",
      },
    },
  },
};

// Memoized UserCell component to prevent unnecessary re-renders
const UserCell = memo(({ row }) => {
  const displayName = useMemo(() => {
    return row.displayName || `${row.firstName} ${row.lastName}`;
  }, [row.displayName, row.firstName, row.lastName]);

  return (
    <div className="d-flex align-items-center py-2">
      <div className="flex-shrink-0">
        <Avatar
          name={displayName}
          size="small"
          style={{ width: "40px", height: "40px" }}
        />
      </div>
      <div className="flex-grow-1 ms-3">
        <div className="fw-semibold">{displayName}</div>
        <div className="text-muted small">{row.email}</div>
      </div>
    </div>
  );
});

UserCell.displayName = "UserCell";

// Memoized Loading Component
const LoadingComponent = memo(() => (
  <div className="d-flex justify-content-center align-items-center py-5">
    <FaSpinner className="fa-spin text-primary me-2" />
    <span>Loading users...</span>
  </div>
));

LoadingComponent.displayName = "LoadingComponent";

// Memoized No Data Component
const NoDataComponent = memo(() => (
  <div className="text-center py-5">
    <FaUser size={48} className="text-muted mb-3" />
    <h6 className="text-muted">No users found</h6>
    <p className="text-muted small">Try adjusting your search criteria</p>
  </div>
));

NoDataComponent.displayName = "NoDataComponent";

// Memoized StatusBadge Component
const StatusBadge = memo(({ status, type }) => {
  const badgeClass = useMemo(() => {
    let baseClass = "badge ";
    let label = "";

    if (type === "status") {
      label = USER_STATUS_LABELS[status] || "Unknown";
      baseClass += status === "Active" ? "bg-success" : "bg-warning";
    } else if (type === "kyc") {
      label = KYC_STATUS_LABELS[status] || "Unknown";
      baseClass +=
        status === "Approved"
          ? "bg-success"
          : status === "Rejected"
          ? "bg-danger"
          : status === "Submitted"
          ? "bg-warning"
          : "bg-secondary";
    } else if (type === "subscription") {
      label = SUBSCRIPTION_STATUS_LABELS[status] || "Unknown";
      baseClass += status === "Active" ? "bg-success" : "bg-secondary";
    }

    return { badgeClass: baseClass, label };
  }, [status, type]);

  return <span className={badgeClass.badgeClass}>{badgeClass.label}</span>;
});

StatusBadge.displayName = "StatusBadge";

const Customers = () => {
  const navigate = useNavigate();

  // State for pagination and filtering
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [sortBy, setSortBy] = useState("CreatedAt");
  const [sortDirection, setSortDirection] = useState("desc");
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState("2"); // Default to Transport Company

  // Advanced filter states
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState(DEFAULT_FILTERS);

  // Dropdown states
  const [dropdownOpen, setDropdownOpen] = useState({
    status: false,
    kycStatus: false,
    subscription: false,
    pageSize: false,
  });

  // Memoized user type filter based on active tab
  const getUserTypeFilter = useCallback(() => {
    return parseInt(activeTab);
  }, [activeTab]);

  // Fetch users data
  const {
    data: usersResponse,
    isLoading,
    error,
    refetch,
  } = useGetUsersAdvanced({
    pageNumber: currentPage,
    pageSize,
    sortBy,
    sortDirection,
    searchTerm,
    userType: getUserTypeFilter(),
    status: filters.status || null,
    kycStatus: filters.kycStatus || null,
    subscriptionStatus: filters.subscriptionStatus || null,
    dateFrom: filters.dateFrom || null,
    dateTo: filters.dateTo || null,
    city: filters.city || null,
    state: filters.state || null,
  });

  const users = usersResponse?.users || [];
  const totalCount = usersResponse?.totalCount || 0;
  const statistics = usersResponse?.statistics || {};

  // Memoized event handlers to prevent unnecessary re-renders
  const handlePageChange = useCallback((page) => {
    setCurrentPage(page);
  }, []);

  const handlePerRowsChange = useCallback((newPageSize) => {
    setPageSize(newPageSize);
    setCurrentPage(1);
  }, []);

  const handleSort = useCallback((column, sortDirection) => {
    setSortBy(column.sortField || column.selector);
    setSortDirection(sortDirection);
  }, []);

  const handleSearch = useCallback((e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  }, []);

  const handleTabChange = useCallback((tab) => {
    setActiveTab(tab);
    setCurrentPage(1);
  }, []);

  const handleFilterChange = useCallback((filterName, value) => {
    setFilters((prev) => ({
      ...prev,
      [filterName]: value,
    }));
    setCurrentPage(1);
  }, []);

  const clearFilters = useCallback(() => {
    setFilters(DEFAULT_FILTERS);
    setSearchTerm("");
    setCurrentPage(1);
  }, []);

  const handleViewUser = useCallback(
    (userId) => {
      navigate(`/customers/view/${userId}`);
    },
    [navigate]
  );

  const columns = useMemo(() => {
    return [
      {
        name: "User",
        selector: (row) =>
          row.displayName || `${row.firstName} ${row.lastName}`,
        sortable: true,
        sortField: "displayName",
        cell: (row) => <UserCell row={row} />,
        minWidth: "250px",
      },
      {
        name: "Contact",
        cell: (row) => (
          <div>
            <div className="d-flex align-items-center mb-1">
              <FaPhone size={12} className="text-muted me-2" />
              <span className="small">{row.phoneNumber}</span>
            </div>
            <div className="d-flex align-items-center">
              <FaMapMarkerAlt size={12} className="text-muted me-2" />
              <span className="small text-muted">
                {row.address?.city}, {row.address?.state}
              </span>
            </div>
          </div>
        ),
        minWidth: "200px",
      },
      {
        name: "Type",
        selector: (row) => USER_TYPE_LABELS[row.userType],
        sortable: true,
        sortField: "userType",
        cell: (row) => (
          <span className="badge bg-info">
            {USER_TYPE_LABELS[row.userType] || "Unknown"}
          </span>
        ),
        minWidth: "150px",
      },
      {
        name: "Status",
        selector: (row) => row.status,
        sortable: true,
        sortField: "status",
        cell: (row) => <StatusBadge status={row.status} type="status" />,
        minWidth: "100px",
      },
      {
        name: "KYC Status",
        selector: (row) => row.kycStatus,
        sortable: true,
        sortField: "kycStatus",
        cell: (row) => <StatusBadge status={row.kycStatus} type="kyc" />,
        minWidth: "120px",
      },
      {
        name: "Subscription",
        cell: (row) => (
          <div>
            <StatusBadge
              status={row.subscription?.status}
              type="subscription"
            />
            {row.subscription?.planName && (
              <div className="small text-muted mt-1">
                {row.subscription.planName}
              </div>
            )}
          </div>
        ),
        minWidth: "130px",
      },
      {
        name: "Created",
        selector: (row) => row.createdAt,
        sortable: true,
        sortField: "createdAt",
        cell: (row) => (
          <div className="small">
            {new Date(row.createdAt).toLocaleDateString()}
          </div>
        ),
        minWidth: "100px",
      },
      {
        name: "Actions",
        cell: (row) => (
          <div className="d-flex gap-2">
            <Button
              color="primary"
              size="sm"
              outline
              onClick={() => handleViewUser(row.userId)}
              title="View User Details"
            >
              <FaEye size={12} />
            </Button>
          </div>
        ),
        minWidth: "100px",
        center: true,
      },
    ];
  }, [handleViewUser]);

  // Memoized dropdown toggle
  const toggleDropdown = useCallback((dropdownName) => {
    setDropdownOpen((prev) => ({
      ...prev,
      [dropdownName]: !prev[dropdownName],
    }));
  }, []);

  // Memoized active filter count
  const activeFilterCount = useMemo(() => {
    return (
      Object.values(filters).filter((value) => value !== "").length +
      (searchTerm ? 1 : 0)
    );
  }, [filters, searchTerm]);

  // Memoized icon component for user type
  const getUserTypeIcon = useCallback((userType) => {
    const config = USER_TYPE_CONFIG[userType];
    if (!config) return <FaUser />;

    const iconMap = {
      FaTruck: <FaTruck />,
      FaHandshake: <FaHandshake />,
      FaShippingFast: <FaShippingFast />,
      FaIndustry: <FaIndustry />,
      FaUserCircle: <FaUserCircle />,
    };

    return iconMap[config.icon] || <FaUser />;
  }, []);

  return (
    <div className="container-fluid py-4">
      {/* Enhanced PageHeader Component */}
      <PageHeader
        title="Users Management"
        description="Manage user onboarding and KYC processes"
        icon={<FaUser size={24} />}
        enhanced={true}
        showFilters={showFilters}
        onToggleFilters={() => setShowFilters(!showFilters)}
        activeFilterCount={activeFilterCount}
        onClearFilters={clearFilters}
        buttons={[
          {
            text: "Reset",
            icon: <FaSync size={14} />,
            onClick: clearFilters,
            className: "enhanced-reset-btn",
          },
          {
            text: "Export",
            icon: <FaDownload size={14} />,
            onClick: () => console.log("Export clicked"),
            className: "enhanced-export-btn",
          },
        ]}
      />

      {/* Page Level Filter Forms */}
      <Collapse isOpen={showFilters}>
        <Card className="mb-4">
          <CardBody>
            <Row>
              <Col md={3}>
                <FormGroup>
                  <Label className="form-label small fw-semibold">Status</Label>
                  <Dropdown
                    isOpen={dropdownOpen.status}
                    toggle={() => toggleDropdown("status")}
                  >
                    <DropdownToggle
                      caret
                      className="form-control text-start d-flex justify-content-between align-items-center"
                    >
                      {filters.status
                        ? USER_STATUS_LABELS[filters.status]
                        : "All Status"}
                    </DropdownToggle>
                    <DropdownMenu className="w-100">
                      <DropdownItem
                        onClick={() => handleFilterChange("status", "")}
                      >
                        All Status
                      </DropdownItem>
                      {Object.entries(USER_STATUS_LABELS).map(
                        ([value, label]) => (
                          <DropdownItem
                            key={value}
                            onClick={() => handleFilterChange("status", value)}
                          >
                            {label}
                          </DropdownItem>
                        )
                      )}
                    </DropdownMenu>
                  </Dropdown>
                </FormGroup>
              </Col>

              <Col md={3}>
                <FormGroup>
                  <Label className="form-label small fw-semibold">KYC Status</Label>
                  <Dropdown
                    isOpen={dropdownOpen.kycStatus}
                    toggle={() => toggleDropdown("kycStatus")}
                  >
                    <DropdownToggle
                      caret
                      className="form-control text-start d-flex justify-content-between align-items-center"
                    >
                      {filters.kycStatus
                        ? KYC_STATUS_LABELS[filters.kycStatus]
                        : "All KYC Status"}
                    </DropdownToggle>
                    <DropdownMenu className="w-100">
                      <DropdownItem
                        onClick={() => handleFilterChange("kycStatus", "")}
                      >
                        All KYC Status
                      </DropdownItem>
                      {Object.entries(KYC_STATUS_LABELS).map(
                        ([value, label]) => (
                          <DropdownItem
                            key={value}
                            onClick={() =>
                              handleFilterChange("kycStatus", value)
                            }
                          >
                            {label}
                          </DropdownItem>
                        )
                      )}
                    </DropdownMenu>
                  </Dropdown>
                </FormGroup>
              </Col>

              <Col md={3}>
                <FormGroup>
                  <Label className="form-label small fw-semibold">Subscription</Label>
                  <Dropdown
                    isOpen={dropdownOpen.subscription}
                    toggle={() => toggleDropdown("subscription")}
                  >
                    <DropdownToggle
                      caret
                      className="form-control text-start d-flex justify-content-between align-items-center"
                    >
                      {filters.subscriptionStatus
                        ? SUBSCRIPTION_STATUS_LABELS[filters.subscriptionStatus]
                        : "All Subscriptions"}
                    </DropdownToggle>
                    <DropdownMenu className="w-100">
                      <DropdownItem
                        onClick={() =>
                          handleFilterChange("subscriptionStatus", "")
                        }
                      >
                        All Subscriptions
                      </DropdownItem>
                      {Object.entries(SUBSCRIPTION_STATUS_LABELS).map(
                        ([value, label]) => (
                          <DropdownItem
                            key={value}
                            onClick={() =>
                              handleFilterChange("subscriptionStatus", value)
                            }
                          >
                            {label}
                          </DropdownItem>
                        )
                      )}
                    </DropdownMenu>
                  </Dropdown>
                </FormGroup>
              </Col>

              <Col md={3}>
                <FormGroup>
                  <Label className="form-label small fw-semibold">City</Label>
                  <Input
                    type="text"
                    placeholder="Enter city"
                    value={filters.city}
                    onChange={(e) => handleFilterChange("city", e.target.value)}
                  />
                </FormGroup>
              </Col>
            </Row>

            <Row>
              <Col md={3}>
                <FormGroup>
                  <Label className="form-label small fw-semibold">State</Label>
                  <Input
                    type="text"
                    placeholder="Enter state"
                    value={filters.state}
                    onChange={(e) => handleFilterChange("state", e.target.value)}
                  />
                </FormGroup>
              </Col>

              <Col md={3}>
                <FormGroup>
                  <Label className="form-label small fw-semibold">Date From</Label>
                  <Input
                    type="date"
                    value={filters.dateFrom}
                    onChange={(e) =>
                      handleFilterChange("dateFrom", e.target.value)
                    }
                  />
                </FormGroup>
              </Col>

              <Col md={3}>
                <FormGroup>
                  <Label className="form-label small fw-semibold">Date To</Label>
                  <Input
                    type="date"
                    value={filters.dateTo}
                    onChange={(e) =>
                      handleFilterChange("dateTo", e.target.value)
                    }
                  />
                </FormGroup>
              </Col>

              <Col md={3}>
                <FormGroup>
                  <Label className="form-label small fw-semibold">Actions</Label>
                  <div className="d-flex gap-2">
                    <Button
                      color="primary"
                      size="sm"
                      onClick={() => refetch()}
                      disabled={isLoading}
                    >
                      <FaSearch size={14} className="me-2" />
                      Apply
                    </Button>
                    <Button
                      color="outline-secondary"
                      size="sm"
                      onClick={clearFilters}
                    >
                      <FaTimes size={14} className="me-2" />
                      Clear
                    </Button>
                  </div>
                </FormGroup>
              </Col>
            </Row>
          </CardBody>
        </Card>
      </Collapse>



      {/* Modern User Type Tabs */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="customers-tabs-container">
            <div className="customers-tabs-wrapper">
              {Object.entries(USER_TYPE_CONFIG).map(([userType, config]) => (
                <button
                  key={userType}
                  className={`customers-tab-btn ${
                    activeTab === userType ? "active" : ""
                  }`}
                  onClick={() => handleTabChange(userType)}
                >
                  <div className="customers-tab-icon">
                    {getUserTypeIcon(parseInt(userType))}
                  </div>
                  <span className="customers-tab-label">{config.label}</span>
                  <div className="customers-tab-indicator"></div>
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Statistics Cards */}
      <div className="row mb-4">
        <div className="col-md-3 mb-3 mb-md-0">
          <div className="customers-stat-card">
            <div className="customers-stat-content">
              <div className="customers-stat-icon customers-stat-icon-primary">
                <FaUser size={20} />
              </div>
              <div className="customers-stat-details">
                <div className="customers-stat-label">Total Users</div>
                <div className="customers-stat-value">{totalCount}</div>
              </div>
            </div>
            <div className="customers-stat-decoration"></div>
          </div>
        </div>
        <div className="col-md-3 mb-3 mb-md-0">
          <div className="customers-stat-card">
            <div className="customers-stat-content">
              <div className="customers-stat-icon customers-stat-icon-success">
                <FaUser size={20} />
              </div>
              <div className="customers-stat-details">
                <div className="customers-stat-label">Active Users</div>
                <div className="customers-stat-value">{statistics.activeUsers || 0}</div>
              </div>
            </div>
            <div className="customers-stat-decoration"></div>
          </div>
        </div>
        <div className="col-md-3 mb-3 mb-md-0">
          <div className="customers-stat-card">
            <div className="customers-stat-content">
              <div className="customers-stat-icon customers-stat-icon-warning">
                <FaClock size={20} />
              </div>
              <div className="customers-stat-details">
                <div className="customers-stat-label">KYC Pending</div>
                <div className="customers-stat-value">{statistics.kycPending || 0}</div>
              </div>
            </div>
            <div className="customers-stat-decoration"></div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="customers-stat-card">
            <div className="customers-stat-content">
              <div className="customers-stat-icon customers-stat-icon-info">
                <FaBuilding size={20} />
              </div>
              <div className="customers-stat-details">
                <div className="customers-stat-label">With Subscription</div>
                <div className="customers-stat-value">
                  {statistics.withActiveSubscription || 0}
                </div>
              </div>
            </div>
            <div className="customers-stat-decoration"></div>
          </div>
        </div>
      </div>

      {/* Enhanced Search Controls */}
      <div className="row mb-4">
        <div className="col-md-6">
          <div className="customers-search-container">
            <div className="customers-search-wrapper">
              <div className="customers-search-icon">
                <FaSearch size={16} />
              </div>
              <Input
                type="text"
                placeholder="Search users by name, email, or phone..."
                value={searchTerm}
                onChange={handleSearch}
                className="customers-search-input"
              />
              {searchTerm && (
                <button
                  className="customers-search-clear"
                  onClick={() => setSearchTerm("")}
                  type="button"
                >
                  <FaTimes size={14} />
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Users Table */}
      <div className="customers-table-container">
        <div className="customers-table-wrapper">
          <DataTable
            columns={columns}
            data={users}
            pagination
            paginationServer
            paginationTotalRows={totalCount}
            paginationDefaultPage={currentPage}
            paginationPerPage={pageSize}
            paginationRowsPerPageOptions={[10, 20, 50, 100]}
            onChangeRowsPerPage={handlePerRowsChange}
            onChangePage={handlePageChange}
            onSort={handleSort}
            sortServer
            progressPending={isLoading}
            progressComponent={<LoadingComponent />}
            noDataComponent={<NoDataComponent />}
            customStyles={TABLE_CUSTOM_STYLES}
            responsive
            pointerOnHover
          />
        </div>
      </div>

      {error && (
        <div className="alert alert-danger mt-3">
          <strong>Error:</strong> {error.message || "Failed to load users"}
          <button
            className="btn btn-link btn-sm ms-2"
            onClick={() => refetch()}
          >
            Retry
          </button>
        </div>
      )}
    </div>
  );
};

export default memo(Customers);
