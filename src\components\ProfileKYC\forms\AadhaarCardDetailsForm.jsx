import React from 'react';
import { Field, ErrorMessage } from 'formik';
import SelectInput from '@components/Common/SelectInput';

const GENDER_OPTIONS = [
  { value: 'Male', label: 'Male' },
  { value: 'Female', label: 'Female' },
  { value: 'Other', label: 'Other' }
];

const AadhaarCardDetailsForm = ({ formik, existingDocuments = [], onDocumentChange }) => {
  return (
    <div className="clean-form-section">
      <div className="clean-section-header">
        <h3 className="clean-section-title">Aadhaar Card Details</h3>
        <p className="clean-section-subtitle">Please provide your Aadhaar card information</p>
      </div>
      <div className="clean-form-fields">
        <div className="row">
          <div className="col-md-6 mb-3">
            <label htmlFor="aadhaarCardDetails.aadhaarNumber" className="clean-form-label">
              Aadhaar Number <span className="text-danger">*</span>
            </label>
            <Field
              type="text"
              name="aadhaarCardDetails.aadhaarNumber"
              className={`clean-form-control ${
                formik.touched.aadhaarCardDetails?.aadhaarNumber && formik.errors.aadhaarCardDetails?.aadhaarNumber
                  ? 'is-invalid'
                  : formik.touched.aadhaarCardDetails?.aadhaarNumber && formik.values.aadhaarCardDetails?.aadhaarNumber
                  ? 'is-valid'
                  : ''
              }`}
              placeholder="Enter 12-digit Aadhaar number"
              maxLength="12"
              onChange={(e) => {
                // Only allow digits
                const value = e.target.value.replace(/\D/g, '');
                formik.setFieldValue('aadhaarCardDetails.aadhaarNumber', value);
              }}
            />
            <ErrorMessage
              name="aadhaarCardDetails.aadhaarNumber"
              component="div"
              className="clean-form-error"
            />
          </div>

          <div className="col-md-6 mb-3">
            <label htmlFor="aadhaarCardDetails.nameAsPerAadhaar" className="clean-form-label">
              Name as per Aadhaar <span className="text-danger">*</span>
            </label>
            <Field
              type="text"
              name="aadhaarCardDetails.nameAsPerAadhaar"
              className={`clean-form-control ${
                formik.touched.aadhaarCardDetails?.nameAsPerAadhaar && formik.errors.aadhaarCardDetails?.nameAsPerAadhaar
                  ? 'is-invalid'
                  : formik.touched.aadhaarCardDetails?.nameAsPerAadhaar && formik.values.aadhaarCardDetails?.nameAsPerAadhaar
                  ? 'is-valid'
                  : ''
              }`}
              placeholder="Enter name as per Aadhaar"
            />
            <ErrorMessage
              name="aadhaarCardDetails.nameAsPerAadhaar"
              component="div"
              className="clean-form-error"
            />
          </div>

          <div className="col-md-6 mb-3">
            <label htmlFor="aadhaarCardDetails.dateOfBirthInAadhaar" className="clean-form-label">
              Date of Birth <span className="text-danger">*</span>
            </label>
            <Field
              type="date"
              name="aadhaarCardDetails.dateOfBirthInAadhaar"
              className={`clean-form-control ${
                formik.touched.aadhaarCardDetails?.dateOfBirthInAadhaar && formik.errors.aadhaarCardDetails?.dateOfBirthInAadhaar
                  ? 'is-invalid'
                  : formik.touched.aadhaarCardDetails?.dateOfBirthInAadhaar && formik.values.aadhaarCardDetails?.dateOfBirthInAadhaar
                  ? 'is-valid'
                  : ''
              }`}
            />
            <ErrorMessage
              name="aadhaarCardDetails.dateOfBirthInAadhaar"
              component="div"
              className="clean-form-error"
            />
          </div>

          <div className="col-md-6 mb-3">
            <label htmlFor="aadhaarCardDetails.genderInAadhaar" className="clean-form-label">
              Gender <span className="text-danger">*</span>
            </label>
            <SelectInput
              name="aadhaarCardDetails.genderInAadhaar"
              options={GENDER_OPTIONS}
              value={formik.values.aadhaarCardDetails?.genderInAadhaar || ''}
              onChange={(selectedOption) => {
                formik.setFieldValue('aadhaarCardDetails.genderInAadhaar', selectedOption?.value || '');
              }}
              onBlur={formik.handleBlur}
              placeholder="Select gender"
              isInvalid={formik.touched.aadhaarCardDetails?.genderInAadhaar && formik.errors.aadhaarCardDetails?.genderInAadhaar}
              isValid={formik.touched.aadhaarCardDetails?.genderInAadhaar && formik.values.aadhaarCardDetails?.genderInAadhaar && !formik.errors.aadhaarCardDetails?.genderInAadhaar}
            />
            <ErrorMessage
              name="aadhaarCardDetails.genderInAadhaar"
              component="div"
              className="clean-form-error"
            />
          </div>
        </div>

        <div className="row mt-3">
          <div className="col-12">
            <div className="alert alert-info">
              <i className="fas fa-info-circle me-2"></i>
              <strong>Important:</strong> Ensure that the details entered match exactly with your Aadhaar card. 
              Any mismatch may lead to verification issues.
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AadhaarCardDetailsForm;
