import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Card, CardBody } from "reactstrap";
import {
  FaF<PERSON><PERSON>,
  FaSync,
  FaChevronUp,
  FaChevronDown,
} from "react-icons/fa";

const PageHeader = ({
  title = "Welcome back!",
  description = "Here's what's happening with your logistics operations today.",
  showDescription = true,
  icon = null,
  // Action buttons configuration
  buttons = [],
  // Filter configuration
  showFilters = false,
  filterFields = [],
  onClearFilters = () => {},
  activeFilterCount = 0,
  // Enhanced styling
  enhanced = false,
}) => {
  const [showFiltersCollapse, setShowFiltersCollapse] = useState(false);

  const handleFilterToggle = () => {
    setShowFiltersCollapse(!showFiltersCollapse);
  };

  if (enhanced) {
    return (
      <>
        {/* Enhanced Header */}
        <div className="row mb-4">
          <div className="col-12">
            <div className="enhanced-page-header-card">
              <div className="d-flex justify-content-between align-items-center">
                <div className="enhanced-page-header-content">
                  {icon && (
                    <div className="enhanced-page-header-icon">
                      {icon}
                    </div>
                  )}
                  <div>
                    <h4 className="enhanced-page-header-title mb-1">{title}</h4>
                    {showDescription && (
                      <p className="enhanced-page-header-subtitle mb-0">
                        {description}
                      </p>
                    )}
                  </div>
                </div>
                <div className="enhanced-page-header-actions d-flex gap-2">
                  {showFilters && (
                    <Button
                      className="enhanced-action-btn enhanced-filter-btn position-relative"
                      onClick={handleFilterToggle}
                    >
                      <FaFilter size={14} className="me-2" />
                      Filters
                      {activeFilterCount > 0 && (
                        <span className="enhanced-filter-badge position-absolute top-0 start-100 translate-middle badge rounded-pill">
                          {activeFilterCount}
                        </span>
                      )}
                      {showFiltersCollapse ? (
                        <FaChevronUp size={12} className="ms-2" />
                      ) : (
                        <FaChevronDown size={12} className="ms-2" />
                      )}
                    </Button>
                  )}
                  {buttons.map((button, index) => (
                    <Button
                      key={index}
                      className={`enhanced-action-btn ${button.className || ''}`}
                      onClick={button.onClick}
                      disabled={button.disabled}
                    >
                      {button.icon && <span className="me-2">{button.icon}</span>}
                      {button.text}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Filters */}
        {showFilters && filterFields.length > 0 && (
          <Collapse isOpen={showFiltersCollapse}>
            <div className="enhanced-filters-container mb-4">
              <Card className="enhanced-filters-card">
                <CardBody className="enhanced-filters-body">
                  <div className="row">
                    {filterFields.map((field, index) => (
                      <div key={index} className={field.colClass || "col-md-3"}>
                        {field.component}
                      </div>
                    ))}
                  </div>
                  <div className="enhanced-filters-actions mt-3 pt-3">
                    <Button
                      className="enhanced-action-btn enhanced-reset-btn"
                      onClick={onClearFilters}
                      size="sm"
                    >
                      <FaSync size={12} className="me-2" />
                      Clear Filters
                    </Button>
                  </div>
                </CardBody>
              </Card>
            </div>
          </Collapse>
        )}
      </>
    );
  }

  // Original simple header
  return (
    <div className="page-header-sticky">
      <div className="page-header-content">
        <h1 className="page-header-title">{title}</h1>
        {showDescription && (
          <p className="page-header-description">{description}</p>
        )}
      </div>
    </div>
  );
};

export default PageHeader;
