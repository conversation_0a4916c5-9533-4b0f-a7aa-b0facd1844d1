import { resetUser } from "@store/userSlice";
import { store } from "@store/store";

export const stringifyParams = (params) => {
  return Object.keys(params)
    ?.map((key) => {
      return encodeURIComponent(key) + "=" + encodeURIComponent(params[key]);
    })
    .join("&");
};
export const deflateParams = (params) =>
  params && Object.keys(params)?.map((key) => params[key]);
export const logoutUser = () => {
  store.dispatch(resetUser());
  // Reset theme to dark on logout
  localStorage.removeItem('tritrackz-theme');
  const root = document.documentElement;
  root.setAttribute('data-theme', 'dark');
  root.setAttribute('data-bs-theme', 'dark');
};
export const validatePhoneNumberLength = (value) => {
  const countryCode = "+91";
  const phoneNumber = value?.startsWith(countryCode)
    ? value?.slice(countryCode.length)
    : value;

  if (!phoneNumber) {
    return true;
  }
  const length = phoneNumber.length;
  // return length >= 9 && length <= 11;
  return length === 12;
};
