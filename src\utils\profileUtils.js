/**
 * Profile utility functions for handling profile data operations
 */

/**
 * Extract auto-fill data from comprehensive profile response
 * @param {Object} profileData - Profile data from API response
 * @returns {Object} - Auto-fill data for forms
 */
export const extractAutoFillData = (profileData) => {
  if (!profileData) return {};

  // Format phone number for form input (ensure it has country code for react-phone-input-2)
  let formattedPhone = "";
  if (profileData.phoneNumber) {
    const cleaned = profileData.phoneNumber.replace(/\D/g, "");
    if (cleaned.length === 10) {
      formattedPhone = `91${cleaned}`; // Add country code for react-phone-input-2
    } else if (cleaned.length === 12 && cleaned.startsWith("91")) {
      formattedPhone = cleaned; // Already has country code
    } else {
      formattedPhone = profileData.phoneNumber; // Use as is
    }
  }

  return {
    firstName: profileData.firstName || "",
    lastName: profileData.lastName || "",
    displayName: profileData.displayName || "",
    email: profileData.email || "",
    phoneNumber: formattedPhone,
    companyName: profileData.companyName || "",
    gstNumber: profileData.gstNumber || "",
    panNumber: profileData.panNumber || "",
    aadharNumber: profileData.aadharNumber || "",
    licenseNumber: profileData.licenseNumber || "",
    address: profileData.address || "",
    city: profileData.city || "",
    state: profileData.state || "",
    postalCode: profileData.postalCode || "",
    country: profileData.country || "",
    region: profileData.region || "",
    userType: profileData.userType || null,
    status: profileData.status || 0,
    isComplete: profileData.isComplete || false,
    kycStatus: profileData.kycStatus || "NotStarted",
  };
};

/**
 * Check if mobile number should be disabled (already filled and verified)
 * @param {Object} profileData - Profile data from API response
 * @returns {boolean} - Whether mobile number field should be disabled
 */
export const shouldDisableMobileNumber = (profileData) => {
  try {
    return !!(
      profileData?.phoneNumber && profileData.phoneNumber.trim() !== ""
    );
  } catch (error) {
    console.warn("Error checking mobile number status:", error);
    return false;
  }
};

/**
 * Format display name from profile data
 * @param {Object} profileData - Profile data from API response
 * @returns {string} - Formatted display name
 */
export const getDisplayName = (profileData) => {
  try {
    if (!profileData) return "User";

    if (profileData.displayName && profileData.displayName.trim()) {
      return profileData.displayName.trim();
    }

    if (profileData.firstName && profileData.lastName) {
      return `${profileData.firstName.trim()} ${profileData.lastName.trim()}`;
    }

    if (profileData.firstName && profileData.firstName.trim()) {
      return profileData.firstName.trim();
    }

    return "User";
  } catch (error) {
    console.warn("Error getting display name:", error);
    return "User";
  }
};

/**
 * Get user role/type label from profile data
 * @param {Object} profileData - Profile data from API response
 * @returns {string} - User role label
 */
export const getUserRoleLabel = (profileData) => {
  const userTypeLabels = {
    0: "Admin",
    2: "Transport Company",
    3: "Broker",
    4: "Carrier",
    5: "Driver",
    6: "Shipper Company",
    7: "Shipper Individual",
    8: "Shipper Individual",
  };

  return userTypeLabels[profileData?.userType] || "User";
};

/**
 * Check if profile data is loaded and valid
 * @param {Object} profileData - Profile data from API response
 * @returns {boolean} - Whether profile data is valid
 */
export const isValidProfileData = (profileData) => {
  return !!(profileData && profileData.id && profileData.userId);
};

/**
 * Phone number utility functions
 */

/**
 * Format phone number for API payload
 * @param {string} phoneNumber - Phone number from form (can be with or without country code)
 * @returns {Object} - Formatted phone data for API
 */
export const formatPhoneForAPI = (phoneNumber) => {
  try {
    if (!phoneNumber) {
      return {
        mobileNumber: "",
        countryCode: "IN",
      };
    }

    // Remove all non-digit characters
    const cleaned = phoneNumber.replace(/\D/g, "");

    let formattedNumber = "";

    if (cleaned.length === 10) {
      // 10 digits - add country code
      formattedNumber = `+91${cleaned}`;
    } else if (cleaned.length === 12 && cleaned.startsWith("91")) {
      // 12 digits starting with 91 - already has country code
      formattedNumber = `+${cleaned}`;
    } else if (cleaned.length === 13 && cleaned.startsWith("91")) {
      // 13 digits starting with 91 (might have extra digit)
      formattedNumber = `+${cleaned.substring(0, 12)}`;
    } else {
      // Invalid format - return as is with +91 prefix
      formattedNumber = `+91${cleaned.slice(-10)}`;
    }

    return {
      mobileNumber: formattedNumber,
      countryCode: "IN",
    };
  } catch (error) {
    console.warn("Error formatting phone number for API:", error);
    return {
      mobileNumber: phoneNumber || "",
      countryCode: "IN",
    };
  }
};

/**
 * Extract 10-digit phone number from any format
 * @param {string} phoneNumber - Phone number in any format
 * @returns {string} - 10-digit phone number
 */
export const extractTenDigitPhone = (phoneNumber) => {
  try {
    if (!phoneNumber) return "";

    // Remove all non-digit characters
    const cleaned = phoneNumber.replace(/\D/g, "");

    if (cleaned.length >= 10) {
      // Return last 10 digits
      return cleaned.slice(-10);
    }

    return cleaned;
  } catch (error) {
    console.warn("Error extracting 10-digit phone:", error);
    return "";
  }
};

/**
 * Validate 10-digit Indian mobile number
 * @param {string} phoneNumber - Phone number to validate
 * @returns {boolean} - Whether the number is valid
 */
export const isValidIndianMobile = (phoneNumber) => {
  try {
    const tenDigit = extractTenDigitPhone(phoneNumber);
    return /^[6-9]\d{9}$/.test(tenDigit);
  } catch (error) {
    console.warn("Error validating Indian mobile:", error);
    return false;
  }
};

/**
 * Format phone number for display
 * @param {string} phoneNumber - Phone number to format
 * @returns {string} - Formatted phone number for display
 */
export const formatPhoneForDisplay = (phoneNumber) => {
  try {
    if (!phoneNumber) return "";

    const tenDigit = extractTenDigitPhone(phoneNumber);
    if (tenDigit.length === 10) {
      return `+91 ${tenDigit.slice(0, 5)} ${tenDigit.slice(5)}`;
    }

    return phoneNumber;
  } catch (error) {
    console.warn("Error formatting phone for display:", error);
    return phoneNumber || "";
  }
};
