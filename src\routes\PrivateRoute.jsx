import React from "react";
import { Navigate, Outlet } from "react-router-dom";
import { useSelector } from "react-redux";
import ROUTES from "@constants/routes";

const PrivateRoute = () => {
  const token = useSelector((state) => state.user.token);
  const user = useSelector((state) => state.user.user);
  const { phoneNumber, isPhoneVerified, basicInfoCompleted } = useSelector((state) => state.user.authData);

  // If user has token and user data, they're authenticated
  if (token && user) {
    return <Outlet />;
  }

  // If user is in the middle of registration flow, redirect appropriately
  if (phoneNumber && isPhoneVerified && !basicInfoCompleted) {
    return <Navigate to={ROUTES.BASIC_INFO} replace />;
  }

  // Default: redirect to unified auth (login/register)
  return <Navigate to={ROUTES.LOGIN} replace />;
};

export default PrivateRoute;
