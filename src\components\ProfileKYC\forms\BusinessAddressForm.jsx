import React, { useCallback, useState } from 'react';
import { Field, ErrorMessage } from 'formik';
import LocationMap from '@components/Common/LocationMap';

const BusinessAddressForm = ({ formik, existingDocuments, onDocumentChange }) => {
  // existingDocuments and onDocumentChange are not used in this form but passed from parent
  // State to store location coordinates for map display
  const [locationCoords, setLocationCoords] = useState({
    latitude: formik.values.businessAddress?.latitude || null,
    longitude: formik.values.businessAddress?.longitude || null
  });

  // Handle location selection from map
  const handleLocationSelect = useCallback((lat, lng, address) => {
    setLocationCoords({ latitude: lat, longitude: lng });
    
    // Update formik values
    formik.setFieldValue('businessAddress.latitude', lat);
    formik.setFieldValue('businessAddress.longitude', lng);
    
    // If address is provided from reverse geocoding, update address fields
    if (address) {
      if (address.city) {
        formik.setFieldValue('businessAddress.city', address.city);
      }
      if (address.state) {
        formik.setFieldValue('businessAddress.state', address.state);
      }
      if (address.pincode) {
        formik.setFieldValue('businessAddress.pincode', address.pincode);
      }
      if (address.formattedAddress) {
        formik.setFieldValue('businessAddress.address', address.formattedAddress);
      }
    }
  }, [formik]);

  return (
    <div className="clean-form-section">
      <div className="clean-section-header">
        <h3 className="clean-section-title">Business Address</h3>
        <p className="clean-section-subtitle">
          Use the interactive map to select your location or fill in the address details manually
        </p>
      </div>

      <div className="row g-4">
        {/* Map Section */}
        <div className="col-lg-6">
          <div className="location-map-container">
            <LocationMap
              latitude={locationCoords.latitude}
              longitude={locationCoords.longitude}
              onLocationSelect={handleLocationSelect}
              height="520px"
              enableGPS={true}
              enableMapClick={true}
            />
          </div>
        </div>

        {/* Form Fields Section */}
        <div className="col-lg-6">
          <div className="address-form-container">
            {/* Complete Address Field */}
            <div className="mb-3">
              <label htmlFor="businessAddress.address" className="clean-form-label">
                Complete Address <span className="text-danger">*</span>
              </label>
              <Field
                as="textarea"
                name="businessAddress.address"
                rows="3"
                className={`clean-form-control ${
                  formik.touched.businessAddress?.address && formik.errors.businessAddress?.address
                    ? 'is-invalid'
                    : formik.touched.businessAddress?.address && formik.values.businessAddress?.address
                    ? 'is-valid'
                    : ''
                }`}
                placeholder="Enter complete address (building, street, area, etc.)"
              />
              <ErrorMessage
                name="businessAddress.address"
                component="div"
                className="clean-form-error"
              />
              <small className="clean-form-text">
                Provide detailed address including building number, street name, and landmarks
              </small>
            </div>

            {/* City Field */}
            <div className="mb-3">
              <label htmlFor="businessAddress.city" className="clean-form-label">
                City <span className="text-danger">*</span>
              </label>
              <Field
                type="text"
                name="businessAddress.city"
                className={`clean-form-control ${
                  formik.touched.businessAddress?.city && formik.errors.businessAddress?.city
                    ? 'is-invalid'
                    : formik.touched.businessAddress?.city && formik.values.businessAddress?.city
                    ? 'is-valid'
                    : ''
                }`}
                placeholder="Enter city"
              />
              <ErrorMessage
                name="businessAddress.city"
                component="div"
                className="clean-form-error"
              />
            </div>

            {/* State Field */}
            <div className="mb-3">
              <label htmlFor="businessAddress.state" className="clean-form-label">
                State <span className="text-danger">*</span>
              </label>
              <Field
                type="text"
                name="businessAddress.state"
                className={`clean-form-control ${
                  formik.touched.businessAddress?.state && formik.errors.businessAddress?.state
                    ? 'is-invalid'
                    : formik.touched.businessAddress?.state && formik.values.businessAddress?.state
                    ? 'is-valid'
                    : ''
                }`}
                placeholder="Enter state"
              />
              <ErrorMessage
                name="businessAddress.state"
                component="div"
                className="clean-form-error"
              />
            </div>

            {/* Pincode Field */}
            <div className="mb-3">
              <label htmlFor="businessAddress.pincode" className="clean-form-label">
                Pincode <span className="text-danger">*</span>
              </label>
              <Field
                type="text"
                name="businessAddress.pincode"
                className={`clean-form-control ${
                  formik.touched.businessAddress?.pincode && formik.errors.businessAddress?.pincode
                    ? 'is-invalid'
                    : formik.touched.businessAddress?.pincode && formik.values.businessAddress?.pincode
                    ? 'is-valid'
                    : ''
                }`}
                placeholder="Enter 6-digit pincode"
                maxLength="6"
              />
              <ErrorMessage
                name="businessAddress.pincode"
                component="div"
                className="clean-form-error"
              />
            </div>

            {/* Coordinates Display (Read-only) */}
            {locationCoords.latitude && locationCoords.longitude && (
              <div className="mb-3">
                <label className="clean-form-label">Location Coordinates</label>
                <div className="row">
                  <div className="col-6">
                    <input
                      type="text"
                      className="clean-form-control"
                      value={`Lat: ${locationCoords.latitude.toFixed(6)}`}
                      readOnly
                    />
                  </div>
                  <div className="col-6">
                    <input
                      type="text"
                      className="clean-form-control"
                      value={`Lng: ${locationCoords.longitude.toFixed(6)}`}
                      readOnly
                    />
                  </div>
                </div>
                <small className="clean-form-text text-success">
                  Location coordinates captured from map selection
                </small>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BusinessAddressForm;
