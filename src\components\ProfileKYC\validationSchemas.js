import * as Yup from 'yup';

// Basic Details Validation
export const basicDetailsSchema = Yup.object().shape({
  firstName: Yup.string()
    .min(2, 'First Name must be between 2 and 50 characters')
    .max(50, 'First Name must be between 2 and 50 characters')
    .matches(/^[a-zA-Z\s]+$/, 'Enter valid First Name')
    .required('First name is required'),
  lastName: Yup.string()
    .min(1, 'Last Name must be between 1 and 50 characters')
    .max(50, 'Last Name must be between 1 and 50 characters')
    .matches(/^[a-zA-Z\s]+$/, 'Enter valid Last Name'),
  email: Yup.string()
    .trim()
    .matches(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, 'Enter valid Email Address')
    .required('Email is required'),
  phoneNumber: Yup.string()
    .required('Mobile number is required')
    .test('phone-format', 'Enter valid Mobile Number (10 digits starting with 6-9)', function(value) {
      if (!value) return false;
      
      // Remove any non-digit characters
      const cleaned = value.replace(/\D/g, '');
      
      // Check if it's 10 digits (without country code) or 12 digits (with 91 country code)
      if (cleaned.length === 10) {
        return /^[6-9]\d{9}$/.test(cleaned);
      } else if (cleaned.length === 12) {
        return /^91[6-9]\d{9}$/.test(cleaned);
      }
      
      return false;
    }),
  displayName: Yup.string()
    .min(3, 'Display Name must be between 3 to 100 characters')
    .max(100, 'Display Name must be between 3 to 100 characters')
    .matches(/^[a-zA-Z0-9\s]+$/, 'Enter valid Display Name')
});

// Business Address Validation
export const businessAddressSchema = Yup.object().shape({
  address: Yup.string()
    .min(10, 'Address must be at least 10 characters')
    .max(200, 'Address must be less than 200 characters')
    .required('Address is required'),
  city: Yup.string()
    .min(2, 'City must be between 2 to 50 characters')
    .max(50, 'City must be between 2 to 50 characters')
    .matches(/^[a-zA-Z\s]+$/, 'Enter valid City Name')
    .required('City is required'),
  state: Yup.string()
    .min(2, 'State must be between 2 to 50 characters')
    .max(50, 'State must be between 2 to 50 characters')
    .matches(/^[a-zA-Z\s]+$/, 'Enter valid State Name')
    .required('State is required'),
  pincode: Yup.string()
    .matches(/^\d{6}$/, 'Pincode must be exactly 6 digits')
    .required('Pincode is required'),
  latitude: Yup.number()
    .min(-90, 'Invalid latitude')
    .max(90, 'Invalid latitude')
    .nullable(),
  longitude: Yup.number()
    .min(-180, 'Invalid longitude')
    .max(180, 'Invalid longitude')
    .nullable()
});

// Company Details Validation
export const companyDetailsSchema = Yup.object().shape({
  companyName: Yup.string()
    .min(3, 'Company Name must be between 3 to 150 characters')
    .max(150, 'Company Name must be between 3 to 150 characters')
    .matches(/^[a-zA-Z0-9&.\- ]+$/, 'Enter valid Company Name')
    .required('Company Name is required'),
  brandName: Yup.string()
    .min(3, 'Brand Name must be between 3 to 100 characters')
    .max(100, 'Brand Name must be between 3 to 100 characters')
    .matches(/^[a-zA-Z0-9&.\- ]+$/, 'Enter valid Brand Name'),
  companyContactEmail: Yup.string()
    .trim()
    .matches(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, 'Enter valid Email Address')
    .required('Email is required'),
  companyContactPhone: Yup.string()
    .required('Mobile number is required')
    .test('phone-format', 'Enter valid Mobile Number (10 digits starting with 6-9)', function(value) {
      if (!value) return false;

      // Remove any non-digit characters
      const cleaned = value.replace(/\D/g, '');

      // Check if it's 10 digits (without country code) or 12 digits (with 91 country code)
      if (cleaned.length === 10) {
        return /^[6-9]\d{9}$/.test(cleaned);
      } else if (cleaned.length === 12) {
        return /^91[6-9]\d{9}$/.test(cleaned);
      }

      return false;
    }),
  companyLogo: Yup.string()
    .url('Invalid URL format')
    .nullable()
});

// PAN Card Details Validation
export const panCardDetailsSchema = Yup.object().shape({
  panNumber: Yup.string()
    .matches(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/, 'Enter valid PAN Number (e.g., **********)')
    .required('PAN Number is required'),
  nameAsPerPan: Yup.string()
    .min(3, 'PAN Name must be between 3 to 100 characters')
    .max(100, 'PAN Name must be between 3 to 100 characters')
    .matches(/^[a-zA-Z\s.'-]+$/, 'Enter valid PAN Name')
    .required('PAN Name is required'),
  fatherOrHusbandNameInPan: Yup.string()
    .min(3, 'Name must be between 3 to 100 characters')
    .max(100, 'Name must be between 3 to 100 characters')
    .matches(/^[a-zA-Z\s.'-]+$/, 'Enter a valid Father/Husband Name')
    .required('Father/Husband Name is required'),
  dateOfBirthInPan: Yup.date()
    .typeError('Enter a valid date')
    .required('Date of Birth is required')
    .max(new Date(), 'Date of Birth cannot be in the future'),
  businessPanNumber: Yup.string()
    .matches(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/, 'Enter valid Business PAN Number (e.g., **********)')
    .nullable(),
  businessNameAsPerPan: Yup.string()
    .min(3, 'Business Name must be between 3 to 100 characters')
    .max(100, 'Business Name must be between 3 to 100 characters')
    .matches(/^[a-zA-Z0-9&.\- ]+$/, 'Enter valid Business Name')
    .nullable()
});

// Aadhaar Card Details Validation
export const aadhaarCardDetailsSchema = Yup.object().shape({
  aadhaarNumber: Yup.string()
    .matches(/^\d{12}$/, 'Aadhaar Number must be exactly 12 digits')
    .required('Aadhaar Number is required'),
  nameAsPerAadhaar: Yup.string()
    .min(3, 'Name must be between 3 to 100 characters')
    .max(100, 'Name must be between 3 to 100 characters')
    .matches(/^[a-zA-Z\s.'-]+$/, 'Enter valid Name as per Aadhaar')
    .required('Name as per Aadhaar is required'),
  dateOfBirthInAadhaar: Yup.date()
    .typeError('Enter a valid date')
    .required('Date of Birth is required')
    .max(new Date(), 'Date of Birth cannot be in the future'),
  genderInAadhaar: Yup.string()
    .oneOf(['Male', 'Female', 'Other'], 'Select a valid gender')
    .required('Gender is required')
});

// Bank Details Validation
export const bankDetailsSchema = Yup.object().shape({
  accountNumber: Yup.string()
    .matches(/^[0-9]+$/, 'Account Number must contain only numbers')
    .min(6, 'Account Number must be between 6 and 18 digits')
    .max(18, 'Account Number must be between 6 and 18 digits')
    .required('Account Number is required'),
  accountHolderName: Yup.string()
    .matches(/^[a-zA-Z\s]+$/, 'Enter valid Account Holder Name')
    .min(3, 'Account Holder Name must be between 3 and 100 characters')
    .max(100, 'Account Holder Name must be between 3 and 100 characters')
    .required('Account Holder Name is required'),
  bankName: Yup.string()
    .matches(/^[a-zA-Z\s]+$/, 'Enter valid Bank Name')
    .min(3, 'Bank Name must be between 3 and 100 characters')
    .max(100, 'Bank Name must be between 3 and 100 characters')
    .required('Bank Name is required'),
  ifscCode: Yup.string()
    .matches(/^[A-Z]{4}0[A-Z0-9]{6}$/, 'Enter valid IFSC Code (e.g., SBIN0001234)')
    .required('IFSC Code is required'),
  branchName: Yup.string()
    .matches(/^[a-zA-Z\s]+$/, 'Enter valid Branch Name')
    .min(3, 'Branch Name must be between 3 and 100 characters')
    .max(100, 'Branch Name must be between 3 and 100 characters')
    .required('Branch Name is required')
});

// GST Details Validation
export const gstDetailsSchema = Yup.object().shape({
  gstNumber: Yup.string()
    .matches(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/, 'Enter valid GST Number')
    .required('GST Number is required'),
  gstBusinessName: Yup.string()
    .min(3, 'Business Name must be between 3 to 150 characters')
    .max(150, 'Business Name must be between 3 to 150 characters')
    .matches(/^[a-zA-Z0-9&.\- ]+$/, 'Enter valid Business Name')
    .required('Business Name is required')
});

// CIN Number Validation
export const cinNumberSchema = Yup.object().shape({
  cinNumber: Yup.string()
    .matches(/^[LU]\d{5}[A-Z]{2}\d{4}[A-Z]{3}\d{6}$/, 'Enter valid CIN Number')
    .required('CIN Number is required')
});

// Industry Details Validation
export const industryDetailsSchema = Yup.object().shape({
  industryType: Yup.string()
    .required('Industry Type is required'),
  customIndustryType: Yup.string().when('industryType', {
    is: 'Others',
    then: (schema) => schema
      .required('Custom Industry Type is required')
      .min(3, 'Custom Industry Type must be between 3 and 50 characters')
      .max(50, 'Custom Industry Type must be between 3 and 50 characters')
      .matches(/^[a-zA-Z\s]+$/, 'Enter valid Custom Industry Type'),
    otherwise: (schema) => schema.nullable().notRequired(),
  }),
  businessCategory: Yup.string()
    .required('Business Category is required'),
  customBusinessCategory: Yup.string().when('businessCategory', {
    is: 'Others',
    then: (schema) => schema
      .required('Custom Business Category is required')
      .min(3, 'Custom Business Category must be between 3 and 50 characters')
      .max(50, 'Custom Business Category must be between 3 and 50 characters')
      .matches(/^[a-zA-Z\s]+$/, 'Enter valid Custom Business Category'),
    otherwise: (schema) => schema.nullable().notRequired(),
  })
});
