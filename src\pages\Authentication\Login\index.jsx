import React, { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import { useNavigate, Link } from "react-router-dom";
import { setUser, setToken } from "@store/userSlice";
import toast from "react-hot-toast";
import ROUTES from "@constants/routes";
import { useFormik } from "formik";
import * as Yup from "yup";
import { FaEye, FaEyeSlash } from "react-icons/fa";
import { useLogin } from "@api/authHooks";
import {
  storeRememberedCredentials,
  getRememberedCredentials,
  clearRememberedCredentials,
} from "@utils/encryptedStorage";

const Login = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);

  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Login mutation
  const loginMutation = useLogin({
    onSuccess: (response) => {
      console.log("Login Response:", response);

      if (response.accessToken) {
        dispatch(setToken(response.accessToken));

        const userData = {
          id: response.userId,
          firstName: response.firstName,
          lastName: response.lastName,
          email: response.email,
          userType: response.userType,
          mobileNumber: response.mobileNumber,
          userName: response.userName,
        };

        dispatch(setUser(userData));

        // Handle remember me functionality
        if (rememberMe) {
          // Encrypt and store credentials in localStorage
          storeRememberedCredentials(
            formik.values.email,
            formik.values.password
          );
        } else {
          // Clear stored credentials if remember me is unchecked
          clearRememberedCredentials();
        }

        toast.success("Login successful!");
        navigate(ROUTES.DASHBOARD);
      } else {
        toast.error(response.message || "Login failed");
      }
    },
    onError: (error) => {
      toast.error(
        error?.response?.data?.message ||
          "Login failed. Please check your credentials."
      );
    },
  });

  // Email and password validation schema
  const validationSchema = Yup.object({
    email: Yup.string()
      .email("Please enter a valid email address")
      .required("Email is required"),
    password: Yup.string()
      .min(6, "Password must be at least 6 characters")
      .required("Password is required"),
  });

  const formik = useFormik({
    initialValues: {
      email: "",
      password: "",
      userType: 0,
      isAdmin: true,
      deviceInfo: "web-browser",
    },
    validationSchema,
    onSubmit: (values) => {
      const payload = {
        email: values.email.trim().toLowerCase(),
        password: values.password,
        userType: values.userType,
        isAdmin: values.isAdmin,
        deviceInfo: values.deviceInfo,
      };

      console.log("Login payload:", payload);
      loginMutation.mutate(payload);
    },
  });

  // Load remembered credentials on component mount
  useEffect(() => {
    const rememberedCredentials = getRememberedCredentials();
    if (rememberedCredentials) {
      // Decrypt and pre-populate form fields
      formik.setValues({
        ...formik.values,
        email: rememberedCredentials.email,
        password: rememberedCredentials.password,
      });
      setRememberMe(true);
    }
  }, []);

  return (
    <div className="auth-form">
      {/* Header */}
      <div className="auth-welcome-text mb-4">
        <h2 className="fw-bold text-white">Welcome to TriTrackz</h2>
        <p className="text-light">Sign in to your account</p>
      </div>

      {/* Login Form */}
      <form onSubmit={formik.handleSubmit}>
        {/* Email Field */}
        <div className="mb-4">
          <label
            htmlFor="email"
            className="form-label fw-semibold mb-2 text-light"
          >
            Email Address
          </label>
          <input
            type="email"
            className={`form-control auth-input ${
              formik.touched.email && formik.errors.email ? "is-invalid" : ""
            }`}
            id="email"
            name="email"
            value={formik.values.email}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            placeholder="Enter your email address"
            autoComplete="email"
          />
          {formik.touched.email && formik.errors.email && (
            <div className="invalid-feedback">{formik.errors.email}</div>
          )}
        </div>

        {/* Password Field */}
        <div className="mb-4">
          <label
            htmlFor="password"
            className="form-label fw-semibold mb-2 text-light"
          >
            Password
          </label>
          <div className="position-relative">
            <input
              type={showPassword ? "text" : "password"}
              className={`form-control auth-input auth-input-with-icon ${
                formik.touched.password && formik.errors.password
                  ? "is-invalid"
                  : ""
              }`}
              id="password"
              name="password"
              value={formik.values.password}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              placeholder="Enter your password"
              autoComplete="current-password"
            />
            <button
              type="button"
              className="btn position-absolute auth-password-toggle"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? <FaEyeSlash size={18} /> : <FaEye size={18} />}
            </button>
          </div>
          {formik.touched.password && formik.errors.password && (
            <div className="invalid-feedback">{formik.errors.password}</div>
          )}
        </div>

        {/* Remember Me and Forgot Password */}
        <div className="d-flex justify-content-between align-items-center mb-4">
          {/* Remember Me Checkbox */}
          <div className="form-check">
            <input
              type="checkbox"
              className="form-check-input"
              id="rememberMe"
              checked={rememberMe}
              onChange={(e) => setRememberMe(e.target.checked)}
            />
            <label
              className="form-check-label text-light small"
              htmlFor="rememberMe"
            >
              Remember me
            </label>
          </div>

          {/* Forgot Password Link */}
          <Link
            to={ROUTES.FORGOT_PASSWORD}
            className="btn btn-link auth-link-btn text-decoration-none p-0 small"
          >
            Forgot Password?
          </Link>
        </div>

        {/* Login Button */}
        <button
          type="submit"
          className="btn btn-primary w-100 py-3 fw-semibold auth-submit-btn"
          disabled={loginMutation.isPending || !formik.isValid}
        >
          {loginMutation.isPending ? (
            <>
              <span className="spinner-border spinner-border-sm me-2" />
              Signing in...
            </>
          ) : (
            "Sign In"
          )}
        </button>
      </form>
    </div>
  );
};

export default Login;
