import React, { memo, useMemo } from "react";
import { FaTimes } from "react-icons/fa";
import { useRightSidebar } from "@contexts/RightSidebarContext";

// Move static styles outside component
const ICON_STYLE = {
  width: "32px",
  height: "32px",
  fontSize: "14px",
};

// Memoized Header component
const SidebarHeader = memo(({ title, icon, onClose }) => (
  <div className="right-sidebar-header d-flex align-items-center justify-content-between p-3 flex-shrink-0">
    <div className="right-sidebar-title d-flex align-items-center gap-3">
      {icon && (
        <span
          className="right-sidebar-icon d-flex align-items-center justify-content-center rounded-2"
          style={ICON_STYLE}
        >
          {icon}
        </span>
      )}
      <h5 className="mb-0 fw-semibold">{title}</h5>
    </div>
    <button
      type="button"
      className="right-sidebar-close btn p-2 rounded-2"
      onClick={onClose}
      aria-label="Close sidebar"
    >
      <FaTimes size={18} />
    </button>
  </div>
));

SidebarHeader.displayName = "SidebarHeader";

// Memoized Content component
const SidebarContent = memo(({ ContentComponent }) => (
  <div className="right-sidebar-content flex-fill overflow-auto">
    {ContentComponent && <ContentComponent />}
  </div>
));

SidebarContent.displayName = "SidebarContent";

const RightSidebar = memo(() => {
  const { isOpen, ContentComponent, title, icon, width, closeSidebar } =
    useRightSidebar();

  // Memoize the sidebar style to prevent object recreation
  const sidebarStyle = useMemo(
    () => ({
      "--sidebar-width": width,
      width: isOpen ? width : "0",
    }),
    [width, isOpen]
  );

  return (
    <div
      className={`layout-right-sidebar d-flex flex-column h-100 ${
        isOpen ? "open" : ""
      }`}
      style={sidebarStyle}
    >
      {isOpen && (
        <>
          <SidebarHeader title={title} icon={icon} onClose={closeSidebar} />
          <SidebarContent ContentComponent={ContentComponent} />
        </>
      )}
    </div>
  );
});

RightSidebar.displayName = "RightSidebar";

export default RightSidebar;
