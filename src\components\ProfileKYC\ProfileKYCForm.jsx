import React, { useState, useMemo, useCallback } from "react";
import { useFormik, FormikProvider } from "formik";
import { useNavigate } from "react-router-dom";
import { FaArrowLeft, FaCheck } from "react-icons/fa";
import { But<PERSON> } from "reactstrap";
import toast from "react-hot-toast";

// Import form components
import BasicDetailsForm from "./forms/BasicDetailsForm";
import BusinessAddressForm from "./forms/BusinessAddressForm";
import CompanyDetailsForm from "./forms/CompanyDetailsForm";
import PanCardDetailsForm from "./forms/PanCardDetailsForm";
import AadhaarCardDetailsForm from "./forms/AadhaarCardDetailsForm";
import BankDetailsForm from "./forms/BankDetailsForm";
import GstDetailsForm from "./forms/GstDetailsForm";
import CinNumberForm from "./forms/CinNumberForm";
import IndustryDetailsForm from "./forms/IndustryDetailsForm";

// Import validation schemas
import {
  basicDetailsSchema,
  businessAddressSchema,
  companyDetailsSchema,
  panCardDetailsSchema,
  aadhaarCardDetailsSchema,
  bankDetailsSchema,
  gstDetailsSchema,
  cinNumberSchema,
  industryDetailsSchema,
} from "./validationSchemas";

// User types constants
const USER_TYPES = {
  TRANSPORT_COMPANY: "2",
  BROKER: "3",
  CARRIER: "4",
  SHIPPER_COMPANY: "7",
  SHIPPER_INDIVIDUAL: "8",
};

const ProfileKYCForm = ({
  initialData = null,
  isEditMode = false,
  onSuccess = null,
  onCancel = null,
  hideHeader = false,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState([]);
  const [documents, setDocuments] = useState([]);

  const navigate = useNavigate();
  const userType = initialData?.userType || USER_TYPES.SHIPPER_INDIVIDUAL;

  // Define steps based on user type
  const getSteps = (userType) => {
    const baseSteps = [
      { id: "basic", title: "Basic Details", component: BasicDetailsForm },
      {
        id: "address",
        title: "Business Address",
        component: BusinessAddressForm,
      },
      { id: "pan", title: "PAN Card Details", component: PanCardDetailsForm },
      {
        id: "aadhaar",
        title: "Aadhaar Card Details",
        component: AadhaarCardDetailsForm,
      },
      { id: "bank", title: "Bank Details", component: BankDetailsForm },
    ];

    // Add company-specific steps for business users
    if (
      [
        USER_TYPES.TRANSPORT_COMPANY,
        USER_TYPES.CARRIER,
        USER_TYPES.SHIPPER_COMPANY,
      ].includes(userType)
    ) {
      baseSteps.splice(2, 0, {
        id: "company",
        title: "Company Details",
        component: CompanyDetailsForm,
      });
      baseSteps.push({
        id: "gst",
        title: "GST Details",
        component: GstDetailsForm,
      });
      baseSteps.push({
        id: "cin",
        title: "CIN Number",
        component: CinNumberForm,
      });
    }

    // Add industry details for Shipper Company
    if (userType === USER_TYPES.SHIPPER_COMPANY) {
      baseSteps.push({
        id: "industry",
        title: "Industry Details",
        component: IndustryDetailsForm,
      });
    }

    return baseSteps;
  };

  const steps = getSteps(userType);

  // Get current step validation schema
  const getCurrentStepSchema = (stepIndex) => {
    const step = steps[stepIndex];
    const stepId = step?.id;

    switch (stepId) {
      case "basic":
        return basicDetailsSchema;
      case "company":
        return companyDetailsSchema;
      case "address":
        return businessAddressSchema;
      case "gst":
        return gstDetailsSchema;
      case "cin":
        return cinNumberSchema;
      case "pan":
        return panCardDetailsSchema;
      case "aadhaar":
        return aadhaarCardDetailsSchema;
      case "bank":
        return bankDetailsSchema;
      case "industry":
        return industryDetailsSchema;
      default:
        return null;
    }
  };

  // Get form initial values
  const getFormInitialValues = () => {
    const defaultValues = {
      basicDetails: {
        firstName: "",
        lastName: "",
        email: "",
        phoneNumber: "",
        displayName: "",
      },
      businessAddress: {
        address: "",
        city: "",
        state: "",
        pincode: "",
        latitude: null,
        longitude: null,
      },
      companyDetails: {
        companyName: "",
        brandName: "",
        companyContactEmail: "",
        companyContactPhone: "",
        companyLogo: "",
      },
      panCardDetails: {
        panNumber: "",
        nameAsPerPan: "",
        fatherOrHusbandNameInPan: "",
        dateOfBirthInPan: "",
        businessPanNumber: "",
        businessNameAsPerPan: "",
      },
      aadhaarCardDetails: {
        aadhaarNumber: "",
        nameAsPerAadhaar: "",
        dateOfBirthInAadhaar: "",
        genderInAadhaar: "",
      },
      bankDetails: {
        accountNumber: "",
        accountHolderName: "",
        bankName: "",
        ifscCode: "",
        branchName: "",
      },
      gstDetails: {
        gstNumber: "",
        gstBusinessName: "",
      },
      cinDetails: {
        cinNumber: "",
      },
      industryDetails: {
        industryType: "",
        customIndustryType: "",
        businessCategory: "",
        customBusinessCategory: "",
      },
    };

    // If editing, merge with initial data
    if (isEditMode && initialData) {
      return {
        basicDetails: {
          ...defaultValues.basicDetails,
          ...initialData.basicDetails,
        },
        businessAddress: {
          ...defaultValues.businessAddress,
          ...initialData.businessAddress,
        },
        companyDetails: {
          ...defaultValues.companyDetails,
          ...initialData.companyDetails,
        },
        panCardDetails: {
          ...defaultValues.panCardDetails,
          ...initialData.panCardDetails,
        },
        aadhaarCardDetails: {
          ...defaultValues.aadhaarCardDetails,
          ...initialData.aadhaarCardDetails,
        },
        bankDetails: {
          ...defaultValues.bankDetails,
          ...initialData.bankDetails,
        },
        gstDetails: {
          ...defaultValues.gstDetails,
          ...initialData.gstDetails,
        },
        cinDetails: {
          ...defaultValues.cinDetails,
          ...initialData.cinDetails,
        },
        industryDetails: {
          ...defaultValues.industryDetails,
          ...initialData.industryDetails,
        },
      };
    }

    return defaultValues;
  };

  // Formik setup
  const formik = useFormik({
    initialValues: getFormInitialValues(),
    validationSchema: undefined, // Use step-specific validation
    validateOnChange: false,
    validateOnBlur: true,
    enableReinitialize: true,
    onSubmit: async (values) => {
      console.log("🚀 Form submission started");
      console.log("📋 Form values:", values);

      setIsSubmitting(true);

      try {
        // TODO: Implement API call to save/update user profile
        console.log("Saving profile data:", values);

        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 2000));

        toast.success(
          isEditMode
            ? "Profile updated successfully!"
            : "Profile created successfully!"
        );

        if (onSuccess) {
          onSuccess(values);
        } else {
          navigate("/customers");
        }
      } catch (error) {
        console.error("Error saving profile:", error);
        toast.error("Failed to save profile. Please try again.");
      } finally {
        setIsSubmitting(false);
      }
    },
  });

  // Handle document changes
  const handleDocumentChange = useCallback((newDocuments) => {
    setDocuments(newDocuments);
  }, []);

  // Step navigation handlers
  const handleNext = useCallback(async () => {
    const currentStepSchema = getCurrentStepSchema(currentStep);

    if (currentStepSchema) {
      try {
        await currentStepSchema.validate(formik.values, { abortEarly: false });

        // Mark current step as completed
        setCompletedSteps((prev) => [...new Set([...prev, currentStep])]);

        // Move to next step
        if (currentStep < steps.length - 1) {
          setCurrentStep(currentStep + 1);
        }
      } catch (validationErrors) {
        // Set validation errors
        const errors = {};
        validationErrors.inner.forEach((error) => {
          errors[error.path] = error.message;
        });
        formik.setErrors(errors);
        formik.setTouched(
          validationErrors.inner.reduce((touched, error) => {
            touched[error.path] = true;
            return touched;
          }, {})
        );

        toast.error("Please fix the errors before proceeding");
      }
    } else {
      // No validation schema, just move to next step
      setCurrentStep(currentStep + 1);
    }
  }, [currentStep, formik, steps.length, getCurrentStepSchema]);

  const handlePrevious = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  }, [currentStep]);

  const handleStepClick = useCallback(
    (stepIndex) => {
      // Allow navigation to completed steps or current step
      if (completedSteps.includes(stepIndex) || stepIndex <= currentStep) {
        setCurrentStep(stepIndex);
      }
    },
    [completedSteps, currentStep]
  );

  const handleCancel = useCallback(() => {
    if (onCancel) {
      onCancel();
    } else {
      navigate("/customers");
    }
  }, [onCancel, navigate]);

  // Get current step component
  const CurrentStepComponent = steps[currentStep]?.component;

  return (
    <div className="profile-kyc-form-container">
      {/* Header */}
      {!hideHeader && (
        <div className="profile-kyc-header">
          <div className="d-flex align-items-center">
            <Button
              color="link"
              className="text-white p-0 me-3"
              onClick={handleCancel}
            >
              <FaArrowLeft size={16} />
            </Button>
            <div>
              <h4 className="text-white mb-1">
                {isEditMode ? "Edit Profile" : "Edit Profile"}
              </h4>
              <p className="text-white-50 mb-0 small">
                {isEditMode
                  ? "Update your account information"
                  : "Update your account information"}
              </p>
            </div>
          </div>
        </div>
      )}

      <div className="profile-kyc-content">
        {/* Left Sidebar - Step Navigation */}
        <div className="profile-kyc-sidebar">
          <div className="steps-navigation">
            {steps.map((step, index) => (
              <div
                key={step.id}
                className={`step-item ${
                  index === currentStep ? "active" : ""
                } ${completedSteps.includes(index) ? "completed" : ""} ${
                  completedSteps.includes(index) || index <= currentStep
                    ? "clickable"
                    : "disabled"
                }`}
                onClick={() => handleStepClick(index)}
              >
                <div className="step-number">
                  {completedSteps.includes(index) ? (
                    <FaCheck size={14} />
                  ) : (
                    <span>{index + 1}</span>
                  )}
                </div>
                <div className="step-content">
                  <div className="step-label">STEP {index + 1}</div>
                  <div className="step-title">{step.title}</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Main Content Area */}
        <div className="profile-kyc-main">
          <div className="form-content">
            <div className="form-header">
              <h5 className="text-white mb-1">{steps[currentStep]?.title}</h5>
              <p className="text-white-50 mb-0 small">
                Please provide your basic information
              </p>
            </div>

            <div className="form-body">
              <FormikProvider value={formik}>
                <form onSubmit={formik.handleSubmit}>
                  {/* Current Step Form */}
                  {CurrentStepComponent && (
                    <CurrentStepComponent
                      formik={formik}
                      userType={userType}
                      existingDocuments={documents}
                      onDocumentChange={handleDocumentChange}
                    />
                  )}
                </form>
              </FormikProvider>
            </div>

            {/* Navigation Footer */}
            <div className="form-footer">
              <div className="d-flex justify-content-between align-items-center">
                <div className="step-indicator">
                  <span className="text-white-50 small">
                    Step {currentStep + 1} of {steps.length}
                  </span>
                </div>

                <div className="d-flex gap-3">
                  {currentStep > 0 && (
                    <Button
                      type="button"
                      color="outline-light"
                      size="sm"
                      onClick={handlePrevious}
                      disabled={isSubmitting}
                      className="px-4"
                    >
                      Previous
                    </Button>
                  )}

                  {currentStep < steps.length - 1 ? (
                    <Button
                      type="button"
                      color="warning"
                      size="sm"
                      onClick={handleNext}
                      disabled={isSubmitting}
                      className="px-4"
                    >
                      Next
                    </Button>
                  ) : (
                    <Button
                      type="button"
                      color="success"
                      size="sm"
                      onClick={formik.handleSubmit}
                      disabled={isSubmitting}
                      className="px-4"
                    >
                      {isSubmitting ? (
                        <>
                          <span className="spinner-border spinner-border-sm me-2" />
                          Saving...
                        </>
                      ) : (
                        "Save & Complete"
                      )}
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileKYCForm;
