import React, { useState } from 'react';
import { Field, ErrorMessage } from 'formik';

const PanCardDetailsForm = ({ formik, userType, existingDocuments = [], onDocumentChange }) => {
  const [panVerificationStatus, setPanVerificationStatus] = useState(null);
  const [businessPanVerificationStatus, setBusinessPanVerificationStatus] = useState(null);

  // Check if this is a company user type
  const isCompany = userType && ['2', '4', '7'].includes(userType.toString()); // Transport Company, Carrier, Shipper Company

  return (
    <div className="clean-form-section">
      <div className="clean-section-header">
        <h3 className="clean-section-title">PAN Card Details</h3>
        <p className="clean-section-subtitle">Please provide your PAN card information</p>
      </div>
      <div className="clean-form-fields">
        <div className="row">
          {/* Personal PAN Number Field */}
          <div className="col-md-6 mb-3">
            <label htmlFor="panCardDetails.panNumber" className="clean-form-label">
              PAN Number <span className="text-danger">*</span>
            </label>
            <div className="position-relative">
              <Field
                type="text"
                name="panCardDetails.panNumber"
                className={`clean-form-control ${
                  formik.touched.panCardDetails?.panNumber && formik.errors.panCardDetails?.panNumber
                    ? 'is-invalid'
                    : panVerificationStatus === 'success'
                    ? 'is-valid'
                    : panVerificationStatus === 'error'
                    ? 'is-invalid'
                    : ''
                }`}
                placeholder="Enter PAN number (e.g., **********)"
                style={{ textTransform: 'uppercase' }}
                onChange={(e) => {
                  const value = e.target.value.toUpperCase();
                  formik.setFieldValue('panCardDetails.panNumber', value);
                  setPanVerificationStatus(null);
                }}
              />
              {panVerificationStatus === 'success' && (
                <div className="position-absolute top-50 end-0 translate-middle-y me-2">
                  <i className="fas fa-check-circle text-success"></i>
                </div>
              )}
              {panVerificationStatus === 'error' && (
                <div className="position-absolute top-50 end-0 translate-middle-y me-2">
                  <i className="fas fa-times-circle text-danger"></i>
                </div>
              )}
            </div>
            <ErrorMessage
              name="panCardDetails.panNumber"
              component="div"
              className="clean-form-error"
            />
          </div>

          {/* Name as per PAN */}
          <div className="col-md-6 mb-3">
            <label htmlFor="panCardDetails.nameAsPerPan" className="clean-form-label">
              Name as per PAN <span className="text-danger">*</span>
            </label>
            <Field
              type="text"
              name="panCardDetails.nameAsPerPan"
              className={`clean-form-control ${
                formik.touched.panCardDetails?.nameAsPerPan && formik.errors.panCardDetails?.nameAsPerPan
                  ? 'is-invalid'
                  : formik.touched.panCardDetails?.nameAsPerPan && formik.values.panCardDetails?.nameAsPerPan
                  ? 'is-valid'
                  : ''
              }`}
              placeholder="Enter name as per PAN card"
            />
            <ErrorMessage
              name="panCardDetails.nameAsPerPan"
              component="div"
              className="clean-form-error"
            />
          </div>

          {/* Father/Husband Name */}
          <div className="col-md-6 mb-3">
            <label htmlFor="panCardDetails.fatherOrHusbandNameInPan" className="clean-form-label">
              Father/Husband Name <span className="text-danger">*</span>
            </label>
            <Field
              type="text"
              name="panCardDetails.fatherOrHusbandNameInPan"
              className={`clean-form-control ${
                formik.touched.panCardDetails?.fatherOrHusbandNameInPan && formik.errors.panCardDetails?.fatherOrHusbandNameInPan
                  ? 'is-invalid'
                  : formik.touched.panCardDetails?.fatherOrHusbandNameInPan && formik.values.panCardDetails?.fatherOrHusbandNameInPan
                  ? 'is-valid'
                  : ''
              }`}
              placeholder="Enter father/husband name as per PAN"
            />
            <ErrorMessage
              name="panCardDetails.fatherOrHusbandNameInPan"
              component="div"
              className="clean-form-error"
            />
          </div>

          {/* Date of Birth */}
          <div className="col-md-6 mb-3">
            <label htmlFor="panCardDetails.dateOfBirthInPan" className="clean-form-label">
              Date of Birth <span className="text-danger">*</span>
            </label>
            <Field
              type="date"
              name="panCardDetails.dateOfBirthInPan"
              className={`clean-form-control ${
                formik.touched.panCardDetails?.dateOfBirthInPan && formik.errors.panCardDetails?.dateOfBirthInPan
                  ? 'is-invalid'
                  : formik.touched.panCardDetails?.dateOfBirthInPan && formik.values.panCardDetails?.dateOfBirthInPan
                  ? 'is-valid'
                  : ''
              }`}
            />
            <ErrorMessage
              name="panCardDetails.dateOfBirthInPan"
              component="div"
              className="clean-form-error"
            />
          </div>

          {/* Company-specific PAN fields */}
          {isCompany && (
            <>
              <div className="col-12 mb-3">
                <hr className="my-4" />
                <h5 className="text-primary mb-3">Business PAN Details</h5>
              </div>

              <div className="col-md-6 mb-3">
                <label htmlFor="panCardDetails.businessPanNumber" className="clean-form-label">
                  Business PAN Number <span className="text-danger">*</span>
                </label>
                <div className="position-relative">
                  <Field
                    type="text"
                    name="panCardDetails.businessPanNumber"
                    className={`clean-form-control ${
                      formik.touched.panCardDetails?.businessPanNumber && formik.errors.panCardDetails?.businessPanNumber
                        ? 'is-invalid'
                        : businessPanVerificationStatus === 'success'
                        ? 'is-valid'
                        : businessPanVerificationStatus === 'error'
                        ? 'is-invalid'
                        : ''
                    }`}
                    placeholder="Enter business PAN number"
                    style={{ textTransform: 'uppercase' }}
                    onChange={(e) => {
                      const value = e.target.value.toUpperCase();
                      formik.setFieldValue('panCardDetails.businessPanNumber', value);
                      setBusinessPanVerificationStatus(null);
                    }}
                  />
                  {businessPanVerificationStatus === 'success' && (
                    <div className="position-absolute top-50 end-0 translate-middle-y me-2">
                      <i className="fas fa-check-circle text-success"></i>
                    </div>
                  )}
                  {businessPanVerificationStatus === 'error' && (
                    <div className="position-absolute top-50 end-0 translate-middle-y me-2">
                      <i className="fas fa-times-circle text-danger"></i>
                    </div>
                  )}
                </div>
                <ErrorMessage
                  name="panCardDetails.businessPanNumber"
                  component="div"
                  className="clean-form-error"
                />
              </div>

              <div className="col-md-6 mb-3">
                <label htmlFor="panCardDetails.businessNameAsPerPan" className="clean-form-label">
                  Business Name as per PAN <span className="text-danger">*</span>
                </label>
                <Field
                  type="text"
                  name="panCardDetails.businessNameAsPerPan"
                  className={`clean-form-control ${
                    formik.touched.panCardDetails?.businessNameAsPerPan && formik.errors.panCardDetails?.businessNameAsPerPan
                      ? 'is-invalid'
                      : formik.touched.panCardDetails?.businessNameAsPerPan && formik.values.panCardDetails?.businessNameAsPerPan
                      ? 'is-valid'
                      : ''
                  }`}
                  placeholder="Enter business name as per PAN"
                />
                <ErrorMessage
                  name="panCardDetails.businessNameAsPerPan"
                  component="div"
                  className="clean-form-error"
                />
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default PanCardDetailsForm;
