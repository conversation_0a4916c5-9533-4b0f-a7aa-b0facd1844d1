import React, { useState, useEffect } from "react";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import { useFormik } from "formik";
import * as Yup from "yup";
import { <PERSON>a<PERSON>ock, <PERSON>a<PERSON>ye, FaEyeSlash, FaCheckCircle } from "react-icons/fa";
import toast from "react-hot-toast";
import ROUTES from "@constants/routes";
import { useSetPassword } from "@api/authHooks";

const SetPassword = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [tokenValid, setTokenValid] = useState(null);

  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const token = searchParams.get("token");
  const email = searchParams.get("email");

  // Set password mutation
  const setPasswordMutation = useSetPassword({
    onSuccess: (response) => {
      console.log("Set password response:", response);
      toast.success("Password reset successful!");
      setIsSuccess(true);

      // Redirect to login after 3 seconds
      setTimeout(() => {
        navigate(ROUTES.LOGIN);
      }, 3000);
    },
    onError: (error) => {
      console.error("Set password error:", error);
      toast.error(
        error?.response?.data?.message ||
          "Failed to reset password. Please try again."
      );
    },
  });

  // Validation schema
  const validationSchema = Yup.object({
    newPassword: Yup.string()
      .min(8, "Password must be at least 8 characters")
      .matches(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
        "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"
      )
      .required("Password is required"),
    confirmPassword: Yup.string()
      .oneOf([Yup.ref("newPassword"), null], "Passwords must match")
      .required("Please confirm your password"),
  });

  // Formik setup
  const formik = useFormik({
    initialValues: {
      newPassword: "",
      confirmPassword: "",
    },
    validationSchema,
    onSubmit: (values) => {
      // Prepare API payload according to v1/Auth/forgot-password endpoint
      const payload = {
        email: email, // Get email from URL searchParams
        token: token,
        newPassword: values.newPassword,
        confirmPassword: values.confirmPassword,
      };

      console.log("Set password request:", payload);
      setPasswordMutation.mutate(payload);
    },
  });

  // Check token and email validity on component mount
  useEffect(() => {
    const validateToken = async () => {
      if (!token || !email) {
        setTokenValid(false);
        return;
      }

      try {
        // TODO: Implement token validation API call
        console.log("Validating token:", token, "for email:", email);

        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1000));

        setTokenValid(true);
      } catch (error) {
        console.error("Token validation error:", error);
        setTokenValid(false);
      }
    };

    validateToken();
  }, [token, email]);

  // Loading state while validating token
  if (tokenValid === null) {
    return (
      <div className="auth-form">
        <div className="text-center">
          <span className="spinner-border text-white mb-3" />
          <p className="text-light">Validating reset link...</p>
        </div>
      </div>
    );
  }

  // Invalid token state
  if (tokenValid === false) {
    return (
      <div className="auth-form">
        {/* Error Header */}
        <div className="auth-welcome-text mb-4">
          <h2 className="fw-bold text-white">Invalid Reset Link</h2>
          <p className="text-light">This link is invalid or has expired</p>
        </div>

        {/* Error Content */}
        <div className="text-center mb-4">
          <div
            className="bg-danger bg-opacity-20 rounded-circle d-inline-flex align-items-center justify-content-center mb-3"
            style={{ width: "80px", height: "80px" }}
          >
            <FaLock className="text-white" size={32} />
          </div>
          <p className="text-light mb-4">
            This password reset link is invalid, missing required parameters, or
            has expired. Please request a new password reset link.
          </p>
        </div>

        {/* Action Buttons */}
        <Link
          to={ROUTES.FORGOT_PASSWORD}
          className="btn btn-primary w-100 py-3 fw-semibold auth-submit-btn mb-3"
        >
          Request New Link
        </Link>

        <Link
          to={ROUTES.LOGIN}
          className="btn btn-link auth-link-btn text-decoration-none w-100"
        >
          Back to Login
        </Link>
      </div>
    );
  }

  // Success state
  if (isSuccess) {
    return (
      <div className="auth-form">
        {/* Success Header */}
        <div className="auth-welcome-text mb-4">
          <h2 className="fw-bold text-white">Password Reset Successful</h2>
          <p className="text-light">Your password has been updated</p>
        </div>

        {/* Success Content */}
        <div className="text-center mb-4">
          <div
            className="bg-success bg-opacity-20 rounded-circle d-inline-flex align-items-center justify-content-center mb-3"
            style={{ width: "80px", height: "80px" }}
          >
            <FaCheckCircle className="text-white" size={32} />
          </div>
          <p className="text-light mb-4">
            Your password has been successfully reset. You will be redirected to
            the login page shortly.
          </p>
        </div>

        {/* Continue Button */}
        <Link
          to={ROUTES.LOGIN}
          className="btn btn-primary w-100 py-3 fw-semibold auth-submit-btn"
        >
          Continue to Login
        </Link>
      </div>
    );
  }

  return (
    <div className="auth-form">
      {/* Header */}
      <div className="auth-welcome-text mb-4">
        <h2 className="fw-bold text-white">Set New Password</h2>
        <p className="text-light">
          {email
            ? `Setting password for ${email}`
            : "Please enter your new password below"}
        </p>
      </div>

      {/* Form */}
      <form onSubmit={formik.handleSubmit}>
        {/* New Password Field */}
        <div className="mb-4">
          <label
            htmlFor="newPassword"
            className="form-label fw-semibold mb-2 text-light"
          >
            New Password
          </label>
          <div className="position-relative">
            <input
              type={showPassword ? "text" : "password"}
              className={`form-control auth-input auth-input-with-icon ${
                formik.touched.newPassword && formik.errors.newPassword
                  ? "is-invalid"
                  : ""
              }`}
              id="newPassword"
              name="newPassword"
              value={formik.values.newPassword}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              placeholder="Enter new password"
              autoComplete="new-password"
            />
            <button
              type="button"
              className="btn position-absolute auth-password-toggle"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? <FaEyeSlash size={18} /> : <FaEye size={18} />}
            </button>
          </div>
          {formik.touched.newPassword && formik.errors.newPassword && (
            <div className="invalid-feedback">{formik.errors.newPassword}</div>
          )}
        </div>

        {/* Confirm Password Field */}
        <div className="mb-4">
          <label
            htmlFor="confirmPassword"
            className="form-label fw-semibold mb-2 text-light"
          >
            Confirm Password
          </label>
          <div className="position-relative">
            <input
              type={showConfirmPassword ? "text" : "password"}
              className={`form-control auth-input auth-input-with-icon ${
                formik.touched.confirmPassword && formik.errors.confirmPassword
                  ? "is-invalid"
                  : ""
              }`}
              id="confirmPassword"
              name="confirmPassword"
              value={formik.values.confirmPassword}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              placeholder="Confirm new password"
              autoComplete="new-password"
            />
            <button
              type="button"
              className="btn position-absolute auth-password-toggle"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            >
              {showConfirmPassword ? (
                <FaEyeSlash size={18} />
              ) : (
                <FaEye size={18} />
              )}
            </button>
          </div>
          {formik.touched.confirmPassword && formik.errors.confirmPassword && (
            <div className="invalid-feedback">
              {formik.errors.confirmPassword}
            </div>
          )}
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          className="btn btn-primary w-100 py-3 fw-semibold auth-submit-btn"
          disabled={setPasswordMutation.isPending || !formik.isValid}
        >
          {setPasswordMutation.isPending ? (
            <>
              <span className="spinner-border spinner-border-sm me-2" />
              Setting Password...
            </>
          ) : (
            "Set New Password"
          )}
        </button>

        {/* Back to Login Link */}
        <div className="text-center mt-4">
          <Link
            to={ROUTES.LOGIN}
            className="btn btn-link auth-link-btn text-decoration-none"
          >
            Back to Login
          </Link>
        </div>
      </form>
    </div>
  );
};

export default SetPassword;
