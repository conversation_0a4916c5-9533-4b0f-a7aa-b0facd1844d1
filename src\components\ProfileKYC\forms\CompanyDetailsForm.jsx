import React from 'react';
import { Field, ErrorMessage } from 'formik';
import PhoneInput from '@components/Common/PhoneInput';

const CompanyDetailsForm = ({ formik, existingDocuments, onDocumentChange }) => {
  // existingDocuments and onDocumentChange are not used in this form but passed from parent
  return (
    <div className="clean-form-section">
      <div className="clean-section-header">
        <h3 className="clean-section-title">Company Details</h3>
        <p className="clean-section-subtitle">Please provide your company information</p>
      </div>
      <div className="clean-form-fields">
        <div className="row">
          <div className="col-md-6 mb-3">
            <label htmlFor="companyDetails.companyName" className="clean-form-label">
              Company Name <span className="text-danger">*</span>
            </label>
            <Field
              type="text"
              name="companyDetails.companyName"
              className={`clean-form-control ${
                formik.touched.companyDetails?.companyName && formik.errors.companyDetails?.companyName
                  ? 'is-invalid'
                  : formik.touched.companyDetails?.companyName && formik.values.companyDetails?.companyName
                  ? 'is-valid'
                  : ''
              }`}
              placeholder="Enter company name"
            />
            <ErrorMessage
              name="companyDetails.companyName"
              component="div"
              className="clean-form-error"
            />
          </div>

          <div className="col-md-6 mb-3">
            <label htmlFor="companyDetails.brandName" className="clean-form-label">
              Brand Name
            </label>
            <Field
              type="text"
              name="companyDetails.brandName"
              className={`clean-form-control ${
                formik.touched.companyDetails?.brandName && formik.errors.companyDetails?.brandName
                  ? 'is-invalid'
                  : formik.touched.companyDetails?.brandName && formik.values.companyDetails?.brandName
                  ? 'is-valid'
                  : ''
              }`}
              placeholder="Enter brand name (optional)"
            />
            <ErrorMessage
              name="companyDetails.brandName"
              component="div"
              className="clean-form-error"
            />
          </div>

          <div className="col-md-6 mb-3">
            <label htmlFor="companyDetails.companyContactEmail" className="clean-form-label">
              Company Email <span className="text-danger">*</span>
            </label>
            <Field
              type="email"
              name="companyDetails.companyContactEmail"
              className={`clean-form-control ${
                formik.touched.companyDetails?.companyContactEmail && formik.errors.companyDetails?.companyContactEmail
                  ? 'is-invalid'
                  : formik.touched.companyDetails?.companyContactEmail && formik.values.companyDetails?.companyContactEmail
                  ? 'is-valid'
                  : ''
              }`}
              placeholder="Enter company email address"
            />
            <ErrorMessage
              name="companyDetails.companyContactEmail"
              component="div"
              className="clean-form-error"
            />
          </div>

          <div className="col-md-6 mb-3">
            <label htmlFor="companyDetails.companyContactPhone" className="clean-form-label">
              Company Phone <span className="text-danger">*</span>
            </label>
            <PhoneInput
              name="companyDetails.companyContactPhone"
              value={formik.values.companyDetails?.companyContactPhone || ''}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              placeholder="Enter company phone number"
              isInvalid={formik.touched.companyDetails?.companyContactPhone && formik.errors.companyDetails?.companyContactPhone}
              isValid={formik.touched.companyDetails?.companyContactPhone && formik.values.companyDetails?.companyContactPhone && !formik.errors.companyDetails?.companyContactPhone}
              country="in"
            />
            <ErrorMessage
              name="companyDetails.companyContactPhone"
              component="div"
              className="clean-form-error"
            />
          </div>

          <div className="col-12 mb-3">
            <label htmlFor="companyDetails.companyLogo" className="clean-form-label">
              Company Logo URL
            </label>
            <Field
              type="url"
              name="companyDetails.companyLogo"
              className={`clean-form-control ${
                formik.touched.companyDetails?.companyLogo && formik.errors.companyDetails?.companyLogo
                  ? 'is-invalid'
                  : formik.touched.companyDetails?.companyLogo && formik.values.companyDetails?.companyLogo
                  ? 'is-valid'
                  : ''
              }`}
              placeholder="Enter company logo URL (optional)"
            />
            <ErrorMessage
              name="companyDetails.companyLogo"
              component="div"
              className="clean-form-error"
            />
            <small className="clean-form-text">
              Provide a direct URL to your company logo image
            </small>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompanyDetailsForm;
