import React, { useCallback } from "react";
import { useRightSidebar } from "@contexts/RightSidebarContext";
import ChangePassword from "@pages/ProfileSettings/components/ChangePassword";

export const useSidebarActions = () => {
  const { openSidebar } = useRightSidebar();

  // Memoize the openChangePassword function to prevent unnecessary re-renders
  const openChangePassword = useCallback(() => {
    openSidebar({
      title: "Change Password",
      icon: null,
      ContentComponent: ChangePassword,
      width: "520px",
    });
  }, [openSidebar]);

  return {
    openChangePassword,
  };
};
