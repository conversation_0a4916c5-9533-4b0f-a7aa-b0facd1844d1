import React from "react";
import { Field, ErrorMessage } from "formik";
import PhoneInput from "@components/Common/PhoneInput";

const BasicDetailsForm = ({
  formik,
  userType,
  existingDocuments,
  onDocumentChange,
}) => {
  // existingDocuments and onDocumentChange are not used in this form but passed from parent
  return (
    <div className="kyc-form-section">
      <div className="row">
        <div className="col-md-6 mb-4">
          <label htmlFor="basicDetails.firstName" className="form-label">
            First Name <span className="text-danger">*</span>
          </label>
          <Field
            type="text"
            name="basicDetails.firstName"
            className={`form-control ${
              formik.touched.basicDetails?.firstName &&
              formik.errors.basicDetails?.firstName
                ? "is-invalid"
                : formik.touched.basicDetails?.firstName &&
                  formik.values.basicDetails?.firstName
                ? "is-valid"
                : ""
            }`}
            placeholder="Praveen"
          />
          <ErrorMessage
            name="basicDetails.firstName"
            component="div"
            className="invalid-feedback"
          />
        </div>

        <div className="col-md-6 mb-4">
          <label htmlFor="basicDetails.lastName" className="form-label">
            Last Name <span className="text-danger">*</span>
          </label>
          <Field
            type="text"
            name="basicDetails.lastName"
            className={`form-control ${
              formik.touched.basicDetails?.lastName &&
              formik.errors.basicDetails?.lastName
                ? "is-invalid"
                : formik.touched.basicDetails?.lastName &&
                  formik.values.basicDetails?.lastName
                ? "is-valid"
                : ""
            }`}
            placeholder="Pinto"
          />
          <ErrorMessage
            name="basicDetails.lastName"
            component="div"
            className="invalid-feedback"
          />
        </div>

        <div className="col-md-6 mb-4">
          <label htmlFor="basicDetails.email" className="form-label">
            Email <span className="text-danger">*</span>
          </label>
          <Field
            type="email"
            name="basicDetails.email"
            className={`form-control ${
              formik.touched.basicDetails?.email &&
              formik.errors.basicDetails?.email
                ? "is-invalid"
                : formik.touched.basicDetails?.email &&
                  formik.values.basicDetails?.email
                ? "is-valid"
                : ""
            }`}
            placeholder="<EMAIL>"
          />
          <ErrorMessage
            name="basicDetails.email"
            component="div"
            className="invalid-feedback"
          />
        </div>

        <div className="col-md-6 mb-4">
          <label htmlFor="basicDetails.phoneNumber" className="form-label">
            Phone Number <span className="text-danger">*</span>{" "}
            <span className="badge bg-success ms-2">Verified</span>
          </label>
          <div className="input-group">
            <span className="input-group-text">
              <img
                src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMTQiIHZpZXdCb3g9IjAgMCAyMCAxNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjE0IiByeD0iMiIgZmlsbD0iI0ZGOTkzMyIvPgo8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iNC42NjY2NyIgcng9IjIiIGZpbGw9IndoaXRlIi8+CjxyZWN0IHk9IjkuMzMzMzQiIHdpZHRoPSIyMCIgaGVpZ2h0PSI0LjY2NjY3IiByeD0iMiIgZmlsbD0iIzEzOEEzNiIvPgo8Y2lyY2xlIGN4PSIxMCIgY3k9IjciIHI9IjMiIGZpbGw9IiMwMDRCODciLz4KPC9zdmc+"
                alt="IN"
                className="flag-icon"
              />
              +91
            </span>
            <Field
              type="tel"
              name="basicDetails.phoneNumber"
              className={`form-control ${
                formik.touched.basicDetails?.phoneNumber &&
                formik.errors.basicDetails?.phoneNumber
                  ? "is-invalid"
                  : formik.touched.basicDetails?.phoneNumber &&
                    formik.values.basicDetails?.phoneNumber
                  ? "is-valid"
                  : ""
              }`}
              placeholder="7560121102"
              disabled
            />
          </div>
          <small className="text-muted">
            Mobile number is already verified and cannot be changed.
          </small>
          <ErrorMessage
            name="basicDetails.phoneNumber"
            component="div"
            className="invalid-feedback"
          />
        </div>
      </div>
    </div>
  );
};

export default BasicDetailsForm;
