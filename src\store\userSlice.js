import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  user: null,
  token: null,
  isLoading: false,

  // Authentication flow state
  authData: {
    phoneNumber: null,
    isPhoneVerified: false,
    otpRequestId: null,
    selectedUserType: null,
    existingUserTypes: [], // Array of existing user types for this phone number
    isNewRegistration: false,
    basicInfoCompleted: false,
  },

  // User type conflict resolution
  userTypeConflict: {
    hasConflict: false,
    existingUserType: null,
    requestedUserType: null,
    allowParallelRegistration: false,
  },
};

const userSlice = createSlice({
  name: "user",
  initialState,
  reducers: {
    setUser: (state, action) => {
      state.user = action.payload;
    },
    setToken: (state, action) => {
      state.token = action.payload;
    },
    resetUser: (state) => {
      state.user = null;
      state.token = null;
      state.isLoading = false;
      state.authData = initialState.authData;
      state.userTypeConflict = initialState.userTypeConflict;
    },
    setIsLoading: (state, action) => {
      state.isLoading = action.payload;
    },

    // Authentication flow actions
    setPhoneNumber: (state, action) => {
      state.authData.phoneNumber = action.payload;
    },
    setPhoneVerified: (state, action) => {
      state.authData.isPhoneVerified = action.payload;
    },
    setOtpRequestId: (state, action) => {
      state.authData.otpRequestId = action.payload;
    },
    setSelectedUserType: (state, action) => {
      state.authData.selectedUserType = action.payload;
    },
    setExistingUserTypes: (state, action) => {
      state.authData.existingUserTypes = action.payload;
    },
    setIsNewRegistration: (state, action) => {
      state.authData.isNewRegistration = action.payload;
    },
    setBasicInfoCompleted: (state, action) => {
      state.authData.basicInfoCompleted = action.payload;
    },
    resetAuthData: (state) => {
      state.authData = initialState.authData;
    },

    // User type conflict actions
    setUserTypeConflict: (state, action) => {
      state.userTypeConflict = { ...state.userTypeConflict, ...action.payload };
    },
    clearUserTypeConflict: (state) => {
      state.userTypeConflict = initialState.userTypeConflict;
    },
    setAllowParallelRegistration: (state, action) => {
      state.userTypeConflict.allowParallelRegistration = action.payload;
    },
  },
});

export const {
  setUser,
  setToken,
  resetUser,
  setIsLoading,
  // Authentication flow actions
  setPhoneNumber,
  setPhoneVerified,
  setOtpRequestId,
  setSelectedUserType,
  setExistingUserTypes,
  setIsNewRegistration,
  setBasicInfoCompleted,
  resetAuthData,
  // User type conflict actions
  setUserTypeConflict,
  clearUserTypeConflict,
  setAllowParallelRegistration,
} = userSlice.actions;

export default userSlice.reducer;
