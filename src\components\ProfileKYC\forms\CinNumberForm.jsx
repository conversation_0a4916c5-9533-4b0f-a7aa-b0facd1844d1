import React from 'react';
import { Field, ErrorMessage } from 'formik';

const CinNumberForm = ({ formik, existingDocuments, onDocumentChange }) => {
  // existingDocuments and onDocumentChange are not used in this form but passed from parent
  return (
    <div className="clean-form-section">
      <div className="clean-section-header">
        <h3 className="clean-section-title">CIN Number</h3>
        <p className="clean-section-subtitle">Please provide your Corporate Identification Number</p>
      </div>
      <div className="clean-form-fields">
        <div className="row">
          <div className="col-md-8 mb-3">
            <label htmlFor="cinDetails.cinNumber" className="clean-form-label">
              CIN Number <span className="text-danger">*</span>
            </label>
            <Field
              type="text"
              name="cinDetails.cinNumber"
              className={`clean-form-control ${
                formik.touched.cinDetails?.cinNumber && formik.errors.cinDetails?.cinNumber
                  ? 'is-invalid'
                  : formik.touched.cinDetails?.cinNumber && formik.values.cinDetails?.cinNumber
                  ? 'is-valid'
                  : ''
              }`}
              placeholder="Enter CIN number (e.g., L12345AB1234PLC567890)"
              style={{ textTransform: 'uppercase' }}
              maxLength="21"
              onChange={(e) => {
                const value = e.target.value.toUpperCase();
                formik.setFieldValue('cinDetails.cinNumber', value);
              }}
            />
            <ErrorMessage
              name="cinDetails.cinNumber"
              component="div"
              className="clean-form-error"
            />
            <small className="clean-form-text">
              Format: L12345AB1234PLC567890 (21 characters)
            </small>
          </div>
        </div>

        <div className="row mt-3">
          <div className="col-12">
            <div className="alert alert-info">
              <i className="fas fa-info-circle me-2"></i>
              <strong>About CIN:</strong> Corporate Identification Number (CIN) is a unique 21-digit 
              alphanumeric code assigned to companies incorporated in India. It's mandatory for all 
              companies registered under the Companies Act.
            </div>
          </div>
        </div>

        <div className="row mt-2">
          <div className="col-12">
            <div className="card border-light bg-light">
              <div className="card-body p-3">
                <h6 className="card-title mb-2">CIN Format Breakdown:</h6>
                <ul className="list-unstyled mb-0 small">
                  <li><strong>L/U:</strong> Listing status (L = Listed, U = Unlisted)</li>
                  <li><strong>12345:</strong> Industry code (5 digits)</li>
                  <li><strong>AB:</strong> State code (2 letters)</li>
                  <li><strong>1234:</strong> Year of incorporation (4 digits)</li>
                  <li><strong>PLC:</strong> Company type (PLC/PTC/OPC etc.)</li>
                  <li><strong>567890:</strong> Registration number (6 digits)</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CinNumberForm;
