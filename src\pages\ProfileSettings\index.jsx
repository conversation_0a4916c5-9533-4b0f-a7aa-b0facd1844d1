import React, { useMemo, useCallback, memo } from "react";
import { useSelector } from "react-redux";

import { useSidebarActions } from "@hooks/useSidebarActions";
import { Avatar } from "@components/Common";
import {
  <PERSON>a<PERSON><PERSON>,
  FaEnvelope,
  FaPhone,
  FaLock,
  Fa<PERSON><PERSON>ner,
  FaExclamationTriangle,
} from "react-icons/fa";

// Move static styles outside component to prevent recreation
const LOADING_CONTAINER_STYLE = { minHeight: "400px" };

// Memoized sub-components to prevent unnecessary re-renders
const LoadingState = memo(() => (
  <div
    className="d-flex justify-content-center align-items-center"
    style={LOADING_CONTAINER_STYLE}
  >
    <div className="text-center">
      <FaSpinner className="fa-spin text-primary mb-3" size={32} />
      <p className="text-muted">Loading profile...</p>
    </div>
  </div>
));

LoadingState.displayName = "LoadingState";

const ErrorState = memo(({ onRetry }) => (
  <div
    className="d-flex justify-content-center align-items-center"
    style={LOADING_CONTAINER_STYLE}
  >
    <div className="text-center">
      <FaExclamationTriangle className="text-warning mb-3" size={32} />
      <p className="text-muted">Failed to load profile data</p>
      <button className="btn btn-primary btn-sm" onClick={onRetry}>
        Try Again
      </button>
    </div>
  </div>
));

ErrorState.displayName = "ErrorState";

// Memoized ProfileHeader component
const ProfileHeader = memo(({ onChangePassword }) => (
  <div className="row mb-4">
    <div className="col-12">
      <div className="d-flex justify-content-between align-items-center">
        <div>
          <h4 className="mb-1">Profile Settings</h4>
          <p className="text-muted mb-0">
            Manage your account information and preferences
          </p>
        </div>
        <button className="btn btn-primary" onClick={onChangePassword}>
          <FaLock size={14} className="me-2" />
          Change Password
        </button>
      </div>
    </div>
  </div>
));

ProfileHeader.displayName = "ProfileHeader";

// Memoized ProfileCard component
const ProfileCard = memo(({ displayName, userTypeLabel }) => (
  <div className="row mb-4">
    <div className="col-12">
      <div className="card border-0 shadow-sm">
        <div className="card-body p-4">
          <div className="row align-items-center">
            <div className="col-auto">
              <Avatar name={displayName} size="large" showBorder={true} />
            </div>
            <div className="col">
              <h5 className="mb-1">{displayName}</h5>
              <p className="text-muted mb-2">{userTypeLabel}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
));

ProfileCard.displayName = "ProfileCard";

// Memoized InfoField component
const InfoField = memo(({ icon: Icon, value }) => (
  <div className="d-flex align-items-center">
    <Icon className="text-muted me-2" size={14} />
    <span>{value || "N/A"}</span>
  </div>
));

InfoField.displayName = "InfoField";

// Memoized BasicDetailsCard component
const BasicDetailsCard = memo(({ profileData }) => {
  const firstName =
    profileData?.basicDetails?.firstName || profileData?.firstName;
  const lastName = profileData?.basicDetails?.lastName || profileData?.lastName;
  const email = profileData?.basicDetails?.email || profileData?.email;
  const phone = profileData?.mobileNumber;

  return (
    <div className="row mb-4">
      <div className="col-12">
        <div className="card border-0 shadow-sm">
          <div className="card-header bg-white border-bottom">
            <div className="d-flex align-items-center">
              <FaUser className="text-primary me-2" size={16} />
              <h6 className="mb-0">Administrator Details</h6>
            </div>
          </div>
          <div className="card-body p-4">
            <div className="row">
              <div className="col-md-6">
                <div className="mb-4">
                  <h6 className="text-muted mb-3">Personal Information</h6>
                  <div className="row g-3">
                    <div className="col-6">
                      <label className="form-label text-muted">
                        First Name
                      </label>
                      <InfoField icon={FaUser} value={firstName} />
                    </div>
                    <div className="col-6">
                      <label className="form-label text-muted">Last Name</label>
                      <InfoField icon={FaUser} value={lastName} />
                    </div>
                    <div className="col-12">
                      <label className="form-label text-muted">Email</label>
                      <InfoField icon={FaEnvelope} value={email} />
                    </div>
                    <div className="col-12">
                      <label className="form-label text-muted">
                        Phone Number
                      </label>
                      <InfoField icon={FaPhone} value={phone} />
                    </div>
                    <div className="col-12">
                      <label className="form-label text-muted">Role</label>
                      <span className="d-block">Administrator</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

BasicDetailsCard.displayName = "BasicDetailsCard";

const ProfileSettings = () => {
  const { user } = useSelector((state) => state.user);
  const { openChangePassword } = useSidebarActions();

  // For admin portal, use user data directly
  const profileData = user;
  const isLoading = false;
  const error = null;

  // Memoize display name to prevent recalculation on every render
  const displayName = useMemo(() => {
    return (
      profileData?.displayName ||
      `${profileData?.firstName} ${profileData?.lastName}`
    );
  }, [profileData?.displayName, profileData?.firstName, profileData?.lastName]);

  // Memoize user type label to prevent recalculation
  const userTypeLabel = useMemo(() => {
    return "Administrator";
  }, []);

  // Memoize retry handler
  const handleRetry = useCallback(() => {
    // Add retry logic here if needed
    console.log("Retry clicked");
  }, []);

  if (isLoading) {
    return <LoadingState />;
  }

  if (error) {
    return <ErrorState onRetry={handleRetry} />;
  }

  return (
    <div className="container-fluid py-4">
      {/* Header */}
      <ProfileHeader onChangePassword={openChangePassword} />
      {/* Profile Header Card */}
      <ProfileCard displayName={displayName} userTypeLabel={userTypeLabel} />
      {/* Administrator Details Card */}
      <BasicDetailsCard profileData={profileData} />
    </div>
  );
};

export default ProfileSettings;
