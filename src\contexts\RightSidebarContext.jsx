import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  useMemo,
} from "react";

const RightSidebarContext = createContext();

export const useRightSidebar = () => {
  const context = useContext(RightSidebarContext);
  if (!context) {
    throw new Error(
      "useRightSidebar must be used within a RightSidebarProvider"
    );
  }
  return context;
};

export const RightSidebarProvider = ({ children }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [ContentComponent, setContentComponent] = useState(null);
  const [title, setTitle] = useState("");
  const [icon, setIcon] = useState(null);
  const [width, setWidth] = useState("500px");

  // Memoize the openSidebar function to prevent unnecessary re-renders
  const openSidebar = useCallback(
    ({ ContentComponent, title, icon, width = "500px" }) => {
      setContentComponent(() => ContentComponent);
      setTitle(title);
      setIcon(icon);
      setWidth(width);
      setIsOpen(true);
    },
    []
  );

  // Memoize the closeSidebar function to prevent unnecessary re-renders
  const closeSidebar = useCallback(() => {
    setIsOpen(false);
    // Clear content after simple animation (0.3s)
    setTimeout(() => {
      setContentComponent(null);
      setTitle("");
      setIcon(null);
      setWidth("500px");
    }, 300);
  }, []);

  // Memoize the context value to prevent unnecessary re-renders of consumers
  const value = useMemo(
    () => ({
      isOpen,
      ContentComponent,
      title,
      icon,
      width,
      openSidebar,
      closeSidebar,
    }),
    [isOpen, ContentComponent, title, icon, width, openSidebar, closeSidebar]
  );

  return (
    <RightSidebarContext.Provider value={value}>
      {children}
    </RightSidebarContext.Provider>
  );
};

export default RightSidebarContext;
