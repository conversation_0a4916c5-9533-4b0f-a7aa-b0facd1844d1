import React from 'react';
import { Field, ErrorMessage } from 'formik';
import SelectInput from '@components/Common/SelectInput';

const INDUSTRY_TYPE_OPTIONS = [
  { value: 'Manufacturing', label: 'Manufacturing' },
  { value: 'Agriculture', label: 'Agriculture' },
  { value: 'Technology', label: 'Technology' },
  { value: 'Healthcare', label: 'Healthcare' },
  { value: 'Education', label: 'Education' },
  { value: 'Finance', label: 'Finance' },
  { value: 'Retail', label: 'Retail' },
  { value: 'Construction', label: 'Construction' },
  { value: 'Transportation', label: 'Transportation' },
  { value: 'Energy', label: 'Energy' },
  { value: 'Others', label: 'Others' }
];

const BUSINESS_CATEGORY_OPTIONS = [
  { value: 'Small Scale', label: 'Small Scale' },
  { value: 'Medium Scale', label: 'Medium Scale' },
  { value: 'Large Scale', label: 'Large Scale' },
  { value: 'Startup', label: 'Startup' },
  { value: 'Enterprise', label: 'Enterprise' },
  { value: 'Others', label: 'Others' }
];

const IndustryDetailsForm = ({ formik, existingDocuments, onDocumentChange }) => {
  // existingDocuments and onDocumentChange are not used in this form but passed from parent
  const showCustomIndustryType = formik.values.industryDetails?.industryType === 'Others';
  const showCustomBusinessCategory = formik.values.industryDetails?.businessCategory === 'Others';

  return (
    <div className="clean-form-section">
      <div className="clean-section-header">
        <h3 className="clean-section-title">Industry Details</h3>
        <p className="clean-section-subtitle">Please provide information about your industry and business</p>
      </div>
      <div className="clean-form-fields">
        <div className="row">
          <div className="col-md-6 mb-3">
            <label htmlFor="industryDetails.industryType" className="clean-form-label">
              Industry Type <span className="text-danger">*</span>
            </label>
            <SelectInput
              name="industryDetails.industryType"
              options={INDUSTRY_TYPE_OPTIONS}
              value={formik.values.industryDetails?.industryType || ''}
              onChange={(selectedOption) => {
                formik.setFieldValue('industryDetails.industryType', selectedOption?.value || '');
                // Clear custom industry type if not "Others"
                if (selectedOption?.value !== 'Others') {
                  formik.setFieldValue('industryDetails.customIndustryType', '');
                }
              }}
              onBlur={formik.handleBlur}
              placeholder="Select industry type"
              isInvalid={formik.touched.industryDetails?.industryType && formik.errors.industryDetails?.industryType}
              isValid={formik.touched.industryDetails?.industryType && formik.values.industryDetails?.industryType && !formik.errors.industryDetails?.industryType}
            />
            <ErrorMessage
              name="industryDetails.industryType"
              component="div"
              className="clean-form-error"
            />
          </div>

          {showCustomIndustryType && (
            <div className="col-md-6 mb-3">
              <label htmlFor="industryDetails.customIndustryType" className="clean-form-label">
                Custom Industry Type <span className="text-danger">*</span>
              </label>
              <Field
                type="text"
                name="industryDetails.customIndustryType"
                className={`clean-form-control ${
                  formik.touched.industryDetails?.customIndustryType && formik.errors.industryDetails?.customIndustryType
                    ? 'is-invalid'
                    : formik.touched.industryDetails?.customIndustryType && formik.values.industryDetails?.customIndustryType
                    ? 'is-valid'
                    : ''
                }`}
                placeholder="Enter custom industry type"
              />
              <ErrorMessage
                name="industryDetails.customIndustryType"
                component="div"
                className="clean-form-error"
              />
            </div>
          )}

          <div className="col-md-6 mb-3">
            <label htmlFor="industryDetails.businessCategory" className="clean-form-label">
              Business Category <span className="text-danger">*</span>
            </label>
            <SelectInput
              name="industryDetails.businessCategory"
              options={BUSINESS_CATEGORY_OPTIONS}
              value={formik.values.industryDetails?.businessCategory || ''}
              onChange={(selectedOption) => {
                formik.setFieldValue('industryDetails.businessCategory', selectedOption?.value || '');
                // Clear custom business category if not "Others"
                if (selectedOption?.value !== 'Others') {
                  formik.setFieldValue('industryDetails.customBusinessCategory', '');
                }
              }}
              onBlur={formik.handleBlur}
              placeholder="Select business category"
              isInvalid={formik.touched.industryDetails?.businessCategory && formik.errors.industryDetails?.businessCategory}
              isValid={formik.touched.industryDetails?.businessCategory && formik.values.industryDetails?.businessCategory && !formik.errors.industryDetails?.businessCategory}
            />
            <ErrorMessage
              name="industryDetails.businessCategory"
              component="div"
              className="clean-form-error"
            />
          </div>

          {showCustomBusinessCategory && (
            <div className="col-md-6 mb-3">
              <label htmlFor="industryDetails.customBusinessCategory" className="clean-form-label">
                Custom Business Category <span className="text-danger">*</span>
              </label>
              <Field
                type="text"
                name="industryDetails.customBusinessCategory"
                className={`clean-form-control ${
                  formik.touched.industryDetails?.customBusinessCategory && formik.errors.industryDetails?.customBusinessCategory
                    ? 'is-invalid'
                    : formik.touched.industryDetails?.customBusinessCategory && formik.values.industryDetails?.customBusinessCategory
                    ? 'is-valid'
                    : ''
                }`}
                placeholder="Enter custom business category"
              />
              <ErrorMessage
                name="industryDetails.customBusinessCategory"
                component="div"
                className="clean-form-error"
              />
            </div>
          )}
        </div>

        <div className="row mt-3">
          <div className="col-12">
            <div className="alert alert-info">
              <i className="fas fa-info-circle me-2"></i>
              <strong>Industry Classification:</strong> This information helps us understand your business 
              better and provide relevant services. Choose the category that best describes your primary 
              business activity.
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IndustryDetailsForm;
